import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';

@Injectable({ providedIn: 'root' })
export class PouchdbService {
  private db!: PouchDB.Database;

  constructor() {
    PouchDB.plugin(PouchDBFind);
    this.initDB('patients_db'); // ✅ Initialize IndexedDB
  }

  private initDB(name: string) {
    this.db = new PouchDB(name, { adapter: 'idb' });
    console.log(`✅ PouchDB Initialized: ${name}`);
  }

  // ✅ Create New Document
  createDoc<T>(doc: T): Observable<any> {
    return from(this.db.put(doc as any)).pipe(
      map((res) => res),
      catchError(this.handleError)
    );
  }

  // ✅ Get Document By ID
  getDocById<T>(id: string): Observable<T> {
    return from(this.db.get<T>(id)).pipe(
      map((res) => res),
      catchError(this.handleError)
    );
  }

  // ✅ Get All Documents
  getAllDocs<T>(): Observable<T[]> {
    return from(this.db.allDocs({ include_docs: true })).pipe(
      map((res) => res.rows.map((r) => r.doc as T)),
      catchError(this.handleError)
    );
  }

  // ✅ Update Document
  updateDoc<T>(doc: any): Observable<any> {
    if (!doc._id || !doc._rev) {
      return throwError(() => new Error('❌ Document must have _id and _rev for update'));
    }
    return from(this.db.put(doc)).pipe(
      map((res) => res),
      catchError(this.handleError)
    );
  }

  // ✅ Delete Document
  deleteDoc(id: string): Observable<any> {
    return from(
      this.db.get(id).then((doc) => this.db.remove(doc))
    ).pipe(
      map((res) => res),
      catchError(this.handleError)
    );
  }

  // ✅ Common Error Handler
  private handleError(error: any) {
    console.error('❌ PouchDB Error:', error);
    return throwError(() => error);
  }
}








// // import { Injectable } from '@angular/core';
// // import { Observable, from, throwError } from 'rxjs';
// // import { catchError, map } from 'rxjs/operators';
// // import PouchDB from 'pouchdb-browser'; // ✅ FIXED (Only default import)
// // import PouchDBFind from 'pouchdb-find';

// // @Injectable({ providedIn: 'root' })
// // export class PouchdbService {
// //   private db!: PouchDB.Database;

// //   constructor() {
// //     PouchDB.plugin(PouchDBFind);
// //     this.initDB('patients_db'); // ✅ Initializes IndexedDB on Android/Web
// //   }

// //   private initDB(name: string) {
// //     this.db = new PouchDB(name, { adapter: 'idb' }); // ✅ Works for Capacitor Android
// //     console.log(`✅ PouchDB Initialized: ${name}`);
// //   }

// //   createDoc<T>(doc: T): Observable<any> {
// //     return from(this.db.put(doc as any)).pipe(
// //       map(res => res),
// //       catchError(this.handleError)
// //     );
// //   }

// //   getDocById<T>(id: string): Observable<T> {
// //     return from(this.db.get<T>(id)).pipe(
// //       map(res => res),
// //       catchError(this.handleError)
// //     );
// //   }

// //   getAllDocs<T>(): Observable<T[]> {
// //     return from(this.db.allDocs({ include_docs: true })).pipe(
// //       map(res => res.rows.map(r => r.doc as T)),
// //       catchError(this.handleError)
// //     );
// //   }

// //   updateDoc<T>(doc: any): Observable<any> {
// //     return from(this.db.put(doc)).pipe(
// //       map(res => res),
// //       catchError(this.handleError)
// //     );
// //   }

// //   deleteDoc(id: string): Observable<any> {
// //     return from(this.db.get(id).then(doc => this.db.remove(doc))).pipe(
// //       map(res => res),
// //       catchError(this.handleError)
// //     );
// //   }

// //   private handleError(error: any) {
// //     console.error('❌ PouchDB Error:', error);
// //     return throwError(() => error);
// //   }
// // }
