import {
  require_events,
  require_spark_md5
} from "./chunk-JOFLF65I.js";
import {
  __async,
  __toESM
} from "./chunk-UL2P3LPA.js";

// node_modules/pouchdb-errors/lib/index.es.js
var PouchError = class extends Error {
  constructor(status, error, reason) {
    super();
    this.status = status;
    this.name = error;
    this.message = reason;
    this.error = true;
  }
  toString() {
    return JSON.stringify({
      status: this.status,
      name: this.name,
      message: this.message,
      reason: this.reason
    });
  }
};
var UNAUTHORIZED = new PouchError(401, "unauthorized", "Name or password is incorrect.");
var MISSING_BULK_DOCS = new PouchError(400, "bad_request", "Missing JSON list of 'docs'");
var MISSING_DOC = new PouchError(404, "not_found", "missing");
var REV_CONFLICT = new PouchError(409, "conflict", "Document update conflict");
var INVALID_ID = new PouchError(400, "bad_request", "_id field must contain a string");
var MISSING_ID = new PouchError(412, "missing_id", "_id is required for puts");
var RESERVED_ID = new PouchError(400, "bad_request", "Only reserved document ids may start with underscore.");
var NOT_OPEN = new PouchError(412, "precondition_failed", "Database not open");
var UNKNOWN_ERROR = new PouchError(500, "unknown_error", "Database encountered an unknown error");
var BAD_ARG = new PouchError(500, "badarg", "Some query argument is invalid");
var INVALID_REQUEST = new PouchError(400, "invalid_request", "Request was invalid");
var QUERY_PARSE_ERROR = new PouchError(400, "query_parse_error", "Some query parameter is invalid");
var DOC_VALIDATION = new PouchError(500, "doc_validation", "Bad special document member");
var BAD_REQUEST = new PouchError(400, "bad_request", "Something wrong with the request");
var NOT_AN_OBJECT = new PouchError(400, "bad_request", "Document must be a JSON object");
var DB_MISSING = new PouchError(404, "not_found", "Database not found");
var IDB_ERROR = new PouchError(500, "indexed_db_went_bad", "unknown");
var WSQ_ERROR = new PouchError(500, "web_sql_went_bad", "unknown");
var LDB_ERROR = new PouchError(500, "levelDB_went_went_bad", "unknown");
var FORBIDDEN = new PouchError(403, "forbidden", "Forbidden by design doc validate_doc_update function");
var INVALID_REV = new PouchError(400, "bad_request", "Invalid rev format");
var FILE_EXISTS = new PouchError(412, "file_exists", "The database could not be created, the file already exists.");
var MISSING_STUB = new PouchError(412, "missing_stub", "A pre-existing attachment stub wasn't found");
var INVALID_URL = new PouchError(413, "invalid_url", "Provided URL is invalid");
function createError(error, reason) {
  function CustomPouchError(reason2) {
    var names = Object.getOwnPropertyNames(error);
    for (var i = 0, len = names.length; i < len; i++) {
      if (typeof error[names[i]] !== "function") {
        this[names[i]] = error[names[i]];
      }
    }
    if (this.stack === void 0) {
      this.stack = new Error().stack;
    }
    if (reason2 !== void 0) {
      this.reason = reason2;
    }
  }
  CustomPouchError.prototype = PouchError.prototype;
  return new CustomPouchError(reason);
}
function generateErrorFromResponse(err) {
  if (typeof err !== "object") {
    var data = err;
    err = UNKNOWN_ERROR;
    err.data = data;
  }
  if ("error" in err && err.error === "conflict") {
    err.name = "conflict";
    err.status = 409;
  }
  if (!("name" in err)) {
    err.name = err.error || "unknown";
  }
  if (!("status" in err)) {
    err.status = 500;
  }
  if (!("message" in err)) {
    err.message = err.message || err.reason;
  }
  if (!("stack" in err)) {
    err.stack = new Error().stack;
  }
  return err;
}

// node_modules/pouchdb-fetch/lib/index-browser.es.js
var h = Headers;

// node_modules/pouchdb-binary-utils/lib/index-browser.es.js
var thisAtob = function(str) {
  return atob(str);
};
function createBlob(parts, properties) {
  parts = parts || [];
  properties = properties || {};
  try {
    return new Blob(parts, properties);
  } catch (e) {
    if (e.name !== "TypeError") {
      throw e;
    }
    var Builder = typeof BlobBuilder !== "undefined" ? BlobBuilder : typeof MSBlobBuilder !== "undefined" ? MSBlobBuilder : typeof MozBlobBuilder !== "undefined" ? MozBlobBuilder : WebKitBlobBuilder;
    var builder = new Builder();
    for (var i = 0; i < parts.length; i += 1) {
      builder.append(parts[i]);
    }
    return builder.getBlob(properties.type);
  }
}
function binaryStringToArrayBuffer(bin) {
  var length = bin.length;
  var buf = new ArrayBuffer(length);
  var arr = new Uint8Array(buf);
  for (var i = 0; i < length; i++) {
    arr[i] = bin.charCodeAt(i);
  }
  return buf;
}
function binStringToBluffer(binString, type) {
  return createBlob([binaryStringToArrayBuffer(binString)], { type });
}
function b64ToBluffer(b64, type) {
  return binStringToBluffer(thisAtob(b64), type);
}

// node_modules/pouchdb-collate/lib/index.es.js
function pad(str, padWith, upToLength) {
  var padding = "";
  var targetLength = upToLength - str.length;
  while (padding.length < targetLength) {
    padding += padWith;
  }
  return padding;
}
function padLeft(str, padWith, upToLength) {
  var padding = pad(str, padWith, upToLength);
  return padding + str;
}
var MIN_MAGNITUDE = -324;
var MAGNITUDE_DIGITS = 3;
var SEP = "";
function collate(a, b) {
  if (a === b) {
    return 0;
  }
  a = normalizeKey(a);
  b = normalizeKey(b);
  var ai = collationIndex(a);
  var bi = collationIndex(b);
  if (ai - bi !== 0) {
    return ai - bi;
  }
  switch (typeof a) {
    case "number":
      return a - b;
    case "boolean":
      return a < b ? -1 : 1;
    case "string":
      return stringCollate(a, b);
  }
  return Array.isArray(a) ? arrayCollate(a, b) : objectCollate(a, b);
}
function normalizeKey(key) {
  switch (typeof key) {
    case "undefined":
      return null;
    case "number":
      if (key === Infinity || key === -Infinity || isNaN(key)) {
        return null;
      }
      return key;
    case "object":
      var origKey = key;
      if (Array.isArray(key)) {
        var len = key.length;
        key = new Array(len);
        for (var i = 0; i < len; i++) {
          key[i] = normalizeKey(origKey[i]);
        }
      } else if (key instanceof Date) {
        return key.toJSON();
      } else if (key !== null) {
        key = {};
        for (var k in origKey) {
          if (Object.prototype.hasOwnProperty.call(origKey, k)) {
            var val = origKey[k];
            if (typeof val !== "undefined") {
              key[k] = normalizeKey(val);
            }
          }
        }
      }
  }
  return key;
}
function indexify(key) {
  if (key !== null) {
    switch (typeof key) {
      case "boolean":
        return key ? 1 : 0;
      case "number":
        return numToIndexableString(key);
      case "string":
        return key.replace(/\u0002/g, "").replace(/\u0001/g, "").replace(/\u0000/g, "");
      /* eslint-enable no-control-regex */
      case "object":
        var isArray = Array.isArray(key);
        var arr = isArray ? key : Object.keys(key);
        var i = -1;
        var len = arr.length;
        var result = "";
        if (isArray) {
          while (++i < len) {
            result += toIndexableString(arr[i]);
          }
        } else {
          while (++i < len) {
            var objKey = arr[i];
            result += toIndexableString(objKey) + toIndexableString(key[objKey]);
          }
        }
        return result;
    }
  }
  return "";
}
function toIndexableString(key) {
  var zero = "\0";
  key = normalizeKey(key);
  return collationIndex(key) + SEP + indexify(key) + zero;
}
function parseNumber(str, i) {
  var originalIdx = i;
  var num;
  var zero = str[i] === "1";
  if (zero) {
    num = 0;
    i++;
  } else {
    var neg = str[i] === "0";
    i++;
    var numAsString = "";
    var magAsString = str.substring(i, i + MAGNITUDE_DIGITS);
    var magnitude = parseInt(magAsString, 10) + MIN_MAGNITUDE;
    if (neg) {
      magnitude = -magnitude;
    }
    i += MAGNITUDE_DIGITS;
    while (true) {
      var ch = str[i];
      if (ch === "\0") {
        break;
      } else {
        numAsString += ch;
      }
      i++;
    }
    numAsString = numAsString.split(".");
    if (numAsString.length === 1) {
      num = parseInt(numAsString, 10);
    } else {
      num = parseFloat(numAsString[0] + "." + numAsString[1]);
    }
    if (neg) {
      num = num - 10;
    }
    if (magnitude !== 0) {
      num = parseFloat(num + "e" + magnitude);
    }
  }
  return { num, length: i - originalIdx };
}
function pop(stack, metaStack) {
  var obj = stack.pop();
  if (metaStack.length) {
    var lastMetaElement = metaStack[metaStack.length - 1];
    if (obj === lastMetaElement.element) {
      metaStack.pop();
      lastMetaElement = metaStack[metaStack.length - 1];
    }
    var element = lastMetaElement.element;
    var lastElementIndex = lastMetaElement.index;
    if (Array.isArray(element)) {
      element.push(obj);
    } else if (lastElementIndex === stack.length - 2) {
      var key = stack.pop();
      element[key] = obj;
    } else {
      stack.push(obj);
    }
  }
}
function parseIndexableString(str) {
  var stack = [];
  var metaStack = [];
  var i = 0;
  while (true) {
    var collationIndex2 = str[i++];
    if (collationIndex2 === "\0") {
      if (stack.length === 1) {
        return stack.pop();
      } else {
        pop(stack, metaStack);
        continue;
      }
    }
    switch (collationIndex2) {
      case "1":
        stack.push(null);
        break;
      case "2":
        stack.push(str[i] === "1");
        i++;
        break;
      case "3":
        var parsedNum = parseNumber(str, i);
        stack.push(parsedNum.num);
        i += parsedNum.length;
        break;
      case "4":
        var parsedStr = "";
        while (true) {
          var ch = str[i];
          if (ch === "\0") {
            break;
          }
          parsedStr += ch;
          i++;
        }
        parsedStr = parsedStr.replace(/\u0001\u0001/g, "\0").replace(/\u0001\u0002/g, "").replace(/\u0002\u0002/g, "");
        stack.push(parsedStr);
        break;
      case "5":
        var arrayElement = { element: [], index: stack.length };
        stack.push(arrayElement.element);
        metaStack.push(arrayElement);
        break;
      case "6":
        var objElement = { element: {}, index: stack.length };
        stack.push(objElement.element);
        metaStack.push(objElement);
        break;
      /* istanbul ignore next */
      default:
        throw new Error(
          "bad collationIndex or unexpectedly reached end of input: " + collationIndex2
        );
    }
  }
}
function arrayCollate(a, b) {
  var len = Math.min(a.length, b.length);
  for (var i = 0; i < len; i++) {
    var sort = collate(a[i], b[i]);
    if (sort !== 0) {
      return sort;
    }
  }
  return a.length === b.length ? 0 : a.length > b.length ? 1 : -1;
}
function stringCollate(a, b) {
  return a === b ? 0 : a > b ? 1 : -1;
}
function objectCollate(a, b) {
  var ak = Object.keys(a), bk = Object.keys(b);
  var len = Math.min(ak.length, bk.length);
  for (var i = 0; i < len; i++) {
    var sort = collate(ak[i], bk[i]);
    if (sort !== 0) {
      return sort;
    }
    sort = collate(a[ak[i]], b[bk[i]]);
    if (sort !== 0) {
      return sort;
    }
  }
  return ak.length === bk.length ? 0 : ak.length > bk.length ? 1 : -1;
}
function collationIndex(x) {
  var id = ["boolean", "number", "string", "object"];
  var idx = id.indexOf(typeof x);
  if (~idx) {
    if (x === null) {
      return 1;
    }
    if (Array.isArray(x)) {
      return 5;
    }
    return idx < 3 ? idx + 2 : idx + 3;
  }
  if (Array.isArray(x)) {
    return 5;
  }
}
function numToIndexableString(num) {
  if (num === 0) {
    return "1";
  }
  var expFormat = num.toExponential().split(/e\+?/);
  var magnitude = parseInt(expFormat[1], 10);
  var neg = num < 0;
  var result = neg ? "0" : "2";
  var magForComparison = (neg ? -magnitude : magnitude) - MIN_MAGNITUDE;
  var magString = padLeft(magForComparison.toString(), "0", MAGNITUDE_DIGITS);
  result += SEP + magString;
  var factor = Math.abs(parseFloat(expFormat[0]));
  if (neg) {
    factor = 10 - factor;
  }
  var factorStr = factor.toFixed(20);
  factorStr = factorStr.replace(/\.?0+$/, "");
  result += SEP + factorStr;
  return result;
}

// node_modules/pouchdb-utils/lib/index-browser.es.js
var import_events = __toESM(require_events());

// node_modules/pouchdb-md5/lib/index-browser.es.js
var import_spark_md5 = __toESM(require_spark_md5());
var setImmediateShim = self.setImmediate || self.setTimeout;
function stringMd5(string) {
  return import_spark_md5.default.hash(string);
}

// node_modules/pouchdb-utils/lib/index-browser.es.js
function isBinaryObject(object) {
  return typeof ArrayBuffer !== "undefined" && object instanceof ArrayBuffer || typeof Blob !== "undefined" && object instanceof Blob;
}
function cloneBinaryObject(object) {
  return object instanceof ArrayBuffer ? object.slice(0) : object.slice(0, object.size, object.type);
}
var funcToString = Function.prototype.toString;
var objectCtorString = funcToString.call(Object);
function isPlainObject(value) {
  var proto = Object.getPrototypeOf(value);
  if (proto === null) {
    return true;
  }
  var Ctor = proto.constructor;
  return typeof Ctor == "function" && Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString;
}
function clone(object) {
  var newObject;
  var i;
  var len;
  if (!object || typeof object !== "object") {
    return object;
  }
  if (Array.isArray(object)) {
    newObject = [];
    for (i = 0, len = object.length; i < len; i++) {
      newObject[i] = clone(object[i]);
    }
    return newObject;
  }
  if (object instanceof Date && isFinite(object)) {
    return object.toISOString();
  }
  if (isBinaryObject(object)) {
    return cloneBinaryObject(object);
  }
  if (!isPlainObject(object)) {
    return object;
  }
  newObject = {};
  for (i in object) {
    if (Object.prototype.hasOwnProperty.call(object, i)) {
      var value = clone(object[i]);
      if (typeof value !== "undefined") {
        newObject[i] = value;
      }
    }
  }
  return newObject;
}
var hasLocal;
try {
  localStorage.setItem("_pouch_check_localstorage", 1);
  hasLocal = !!localStorage.getItem("_pouch_check_localstorage");
} catch (e) {
  hasLocal = false;
}
var nextTick = typeof queueMicrotask === "function" ? queueMicrotask : function nextTick2(fn) {
  Promise.resolve().then(fn);
};
function guardedConsole(method) {
  if (typeof console !== "undefined" && typeof console[method] === "function") {
    var args = Array.prototype.slice.call(arguments, 1);
    console[method].apply(console, args);
  }
}
function f() {
}
var hasName = f.name;
var res;
if (hasName) {
  res = function(fun) {
    return fun.name;
  };
} else {
  res = function(fun) {
    var match2 = fun.toString().match(/^\s*function\s*(?:(\S+)\s*)?\(/);
    if (match2 && match2[1]) {
      return match2[1];
    } else {
      return "";
    }
  };
}
function isRemote(db) {
  if (typeof db._remote === "boolean") {
    return db._remote;
  }
  if (typeof db.type === "function") {
    guardedConsole(
      "warn",
      "db.type() is deprecated and will be removed in a future version of PouchDB"
    );
    return db.type() === "http";
  }
  return false;
}
function upsert(db, docId, diffFun) {
  return db.get(docId).catch(function(err) {
    if (err.status !== 404) {
      throw err;
    }
    return {};
  }).then(function(doc) {
    var docRev = doc._rev;
    var newDoc = diffFun(doc);
    if (!newDoc) {
      return { updated: false, rev: docRev };
    }
    newDoc._id = docId;
    newDoc._rev = docRev;
    return tryAndPut(db, newDoc, diffFun);
  });
}
function tryAndPut(db, doc, diffFun) {
  return db.put(doc).then(function(res2) {
    return {
      updated: true,
      rev: res2.rev
    };
  }, function(err) {
    if (err.status !== 409) {
      throw err;
    }
    return upsert(db, doc._id, diffFun);
  });
}

// node_modules/pouchdb-mapreduce-utils/lib/index.es.js
var QueryParseError = class _QueryParseError extends Error {
  constructor(message) {
    super();
    this.status = 400;
    this.name = "query_parse_error";
    this.message = message;
    this.error = true;
    try {
      Error.captureStackTrace(this, _QueryParseError);
    } catch (e) {
    }
  }
};
var NotFoundError = class _NotFoundError extends Error {
  constructor(message) {
    super();
    this.status = 404;
    this.name = "not_found";
    this.message = message;
    this.error = true;
    try {
      Error.captureStackTrace(this, _NotFoundError);
    } catch (e) {
    }
  }
};
var BuiltInError = class _BuiltInError extends Error {
  constructor(message) {
    super();
    this.status = 500;
    this.name = "invalid_value";
    this.message = message;
    this.error = true;
    try {
      Error.captureStackTrace(this, _BuiltInError);
    } catch (e) {
    }
  }
};
function promisedCallback(promise, callback) {
  if (callback) {
    promise.then(function(res2) {
      nextTick(function() {
        callback(null, res2);
      });
    }, function(reason) {
      nextTick(function() {
        callback(reason);
      });
    });
  }
  return promise;
}
function callbackify(fun) {
  return function(...args) {
    var cb = args.pop();
    var promise = fun.apply(this, args);
    if (typeof cb === "function") {
      promisedCallback(promise, cb);
    }
    return promise;
  };
}
function fin(promise, finalPromiseFactory) {
  return promise.then(function(res2) {
    return finalPromiseFactory().then(function() {
      return res2;
    });
  }, function(reason) {
    return finalPromiseFactory().then(function() {
      throw reason;
    });
  });
}
function sequentialize(queue, promiseFactory) {
  return function() {
    var args = arguments;
    var that = this;
    return queue.add(function() {
      return promiseFactory.apply(that, args);
    });
  };
}
function uniq(arr) {
  var theSet = new Set(arr);
  var result = new Array(theSet.size);
  var index = -1;
  theSet.forEach(function(value) {
    result[++index] = value;
  });
  return result;
}
function mapToKeysArray(map) {
  var result = new Array(map.size);
  var index = -1;
  map.forEach(function(value, key) {
    result[++index] = key;
  });
  return result;
}

// node_modules/pouchdb-abstract-mapreduce/lib/index.es.js
var TaskQueue = class {
  constructor() {
    this.promise = Promise.resolve();
  }
  add(promiseFactory) {
    this.promise = this.promise.catch(() => {
    }).then(() => promiseFactory());
    return this.promise;
  }
  finish() {
    return this.promise;
  }
};
function stringify(input) {
  if (!input) {
    return "undefined";
  }
  switch (typeof input) {
    case "function":
      return input.toString();
    case "string":
      return input.toString();
    default:
      return JSON.stringify(input);
  }
}
function createViewSignature(mapFun, reduceFun) {
  return stringify(mapFun) + stringify(reduceFun) + "undefined";
}
function createView(sourceDB, viewName, mapFun, reduceFun, temporary, localDocName) {
  return __async(this, null, function* () {
    const viewSignature = createViewSignature(mapFun, reduceFun);
    let cachedViews;
    if (!temporary) {
      cachedViews = sourceDB._cachedViews = sourceDB._cachedViews || {};
      if (cachedViews[viewSignature]) {
        return cachedViews[viewSignature];
      }
    }
    const promiseForView = sourceDB.info().then(function(info) {
      return __async(this, null, function* () {
        const depDbName = info.db_name + "-mrview-" + (temporary ? "temp" : stringMd5(viewSignature));
        function diffFunction(doc) {
          doc.views = doc.views || {};
          let fullViewName = viewName;
          if (fullViewName.indexOf("/") === -1) {
            fullViewName = viewName + "/" + viewName;
          }
          const depDbs = doc.views[fullViewName] = doc.views[fullViewName] || {};
          if (depDbs[depDbName]) {
            return;
          }
          depDbs[depDbName] = true;
          return doc;
        }
        yield upsert(sourceDB, "_local/" + localDocName, diffFunction);
        const res2 = yield sourceDB.registerDependentDatabase(depDbName);
        const db = res2.db;
        db.auto_compaction = true;
        const view = {
          name: depDbName,
          db,
          sourceDB,
          adapter: sourceDB.adapter,
          mapFun,
          reduceFun
        };
        let lastSeqDoc;
        try {
          lastSeqDoc = yield view.db.get("_local/lastSeq");
        } catch (err) {
          if (err.status !== 404) {
            throw err;
          }
        }
        view.seq = lastSeqDoc ? lastSeqDoc.seq : 0;
        if (cachedViews) {
          view.db.once("destroyed", function() {
            delete cachedViews[viewSignature];
          });
        }
        return view;
      });
    });
    if (cachedViews) {
      cachedViews[viewSignature] = promiseForView;
    }
    return promiseForView;
  });
}
var persistentQueues = {};
var tempViewQueue = new TaskQueue();
var CHANGES_BATCH_SIZE = 50;
function parseViewName(name) {
  return name.indexOf("/") === -1 ? [name, name] : name.split("/");
}
function isGenOne(changes) {
  return changes.length === 1 && /^1-/.test(changes[0].rev);
}
function emitError(db, e, data) {
  try {
    db.emit("error", e);
  } catch (err) {
    guardedConsole(
      "error",
      "The user's map/reduce function threw an uncaught error.\nYou can debug this error by doing:\nmyDatabase.on('error', function (err) { debugger; });\nPlease double-check your map/reduce function."
    );
    guardedConsole("error", e, data);
  }
}
function createAbstractMapReduce(localDocName, mapper2, reducer2, ddocValidator2) {
  function tryMap(db, fun, doc) {
    try {
      fun(doc);
    } catch (e) {
      emitError(db, e, { fun, doc });
    }
  }
  function tryReduce(db, fun, keys, values, rereduce) {
    try {
      return { output: fun(keys, values, rereduce) };
    } catch (e) {
      emitError(db, e, { fun, keys, values, rereduce });
      return { error: e };
    }
  }
  function sortByKeyThenValue(x, y) {
    const keyCompare = collate(x.key, y.key);
    return keyCompare !== 0 ? keyCompare : collate(x.value, y.value);
  }
  function sliceResults(results, limit, skip) {
    skip = skip || 0;
    if (typeof limit === "number") {
      return results.slice(skip, limit + skip);
    } else if (skip > 0) {
      return results.slice(skip);
    }
    return results;
  }
  function rowToDocId(row) {
    const val = row.value;
    const docId = val && typeof val === "object" && val._id || row.id;
    return docId;
  }
  function readAttachmentsAsBlobOrBuffer(res2) {
    for (const row of res2.rows) {
      const atts = row.doc && row.doc._attachments;
      if (!atts) {
        continue;
      }
      for (const filename of Object.keys(atts)) {
        const att = atts[filename];
        atts[filename].data = b64ToBluffer(att.data, att.content_type);
      }
    }
  }
  function postprocessAttachments(opts) {
    return function(res2) {
      if (opts.include_docs && opts.attachments && opts.binary) {
        readAttachmentsAsBlobOrBuffer(res2);
      }
      return res2;
    };
  }
  function addHttpParam(paramName, opts, params, asJson) {
    let val = opts[paramName];
    if (typeof val !== "undefined") {
      if (asJson) {
        val = encodeURIComponent(JSON.stringify(val));
      }
      params.push(paramName + "=" + val);
    }
  }
  function coerceInteger(integerCandidate) {
    if (typeof integerCandidate !== "undefined") {
      const asNumber = Number(integerCandidate);
      if (!isNaN(asNumber) && asNumber === parseInt(integerCandidate, 10)) {
        return asNumber;
      } else {
        return integerCandidate;
      }
    }
  }
  function coerceOptions(opts) {
    opts.group_level = coerceInteger(opts.group_level);
    opts.limit = coerceInteger(opts.limit);
    opts.skip = coerceInteger(opts.skip);
    return opts;
  }
  function checkPositiveInteger(number) {
    if (number) {
      if (typeof number !== "number") {
        return new QueryParseError(`Invalid value for integer: "${number}"`);
      }
      if (number < 0) {
        return new QueryParseError(`Invalid value for positive integer: "${number}"`);
      }
    }
  }
  function checkQueryParseError(options, fun) {
    const startkeyName = options.descending ? "endkey" : "startkey";
    const endkeyName = options.descending ? "startkey" : "endkey";
    if (typeof options[startkeyName] !== "undefined" && typeof options[endkeyName] !== "undefined" && collate(options[startkeyName], options[endkeyName]) > 0) {
      throw new QueryParseError("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");
    } else if (fun.reduce && options.reduce !== false) {
      if (options.include_docs) {
        throw new QueryParseError("{include_docs:true} is invalid for reduce");
      } else if (options.keys && options.keys.length > 1 && !options.group && !options.group_level) {
        throw new QueryParseError("Multi-key fetches for reduce views must use {group: true}");
      }
    }
    for (const optionName of ["group_level", "limit", "skip"]) {
      const error = checkPositiveInteger(options[optionName]);
      if (error) {
        throw error;
      }
    }
  }
  function httpQuery(db, fun, opts) {
    return __async(this, null, function* () {
      let params = [];
      let body;
      let method = "GET";
      let ok;
      addHttpParam("reduce", opts, params);
      addHttpParam("include_docs", opts, params);
      addHttpParam("attachments", opts, params);
      addHttpParam("limit", opts, params);
      addHttpParam("descending", opts, params);
      addHttpParam("group", opts, params);
      addHttpParam("group_level", opts, params);
      addHttpParam("skip", opts, params);
      addHttpParam("stale", opts, params);
      addHttpParam("conflicts", opts, params);
      addHttpParam("startkey", opts, params, true);
      addHttpParam("start_key", opts, params, true);
      addHttpParam("endkey", opts, params, true);
      addHttpParam("end_key", opts, params, true);
      addHttpParam("inclusive_end", opts, params);
      addHttpParam("key", opts, params, true);
      addHttpParam("update_seq", opts, params);
      params = params.join("&");
      params = params === "" ? "" : "?" + params;
      if (typeof opts.keys !== "undefined") {
        const MAX_URL_LENGTH = 2e3;
        const keysAsString = `keys=${encodeURIComponent(JSON.stringify(opts.keys))}`;
        if (keysAsString.length + params.length + 1 <= MAX_URL_LENGTH) {
          params += (params[0] === "?" ? "&" : "?") + keysAsString;
        } else {
          method = "POST";
          if (typeof fun === "string") {
            body = { keys: opts.keys };
          } else {
            fun.keys = opts.keys;
          }
        }
      }
      if (typeof fun === "string") {
        const parts = parseViewName(fun);
        const response2 = yield db.fetch("_design/" + parts[0] + "/_view/" + parts[1] + params, {
          headers: new h({ "Content-Type": "application/json" }),
          method,
          body: JSON.stringify(body)
        });
        ok = response2.ok;
        const result2 = yield response2.json();
        if (!ok) {
          result2.status = response2.status;
          throw generateErrorFromResponse(result2);
        }
        for (const row of result2.rows) {
          if (row.value && row.value.error && row.value.error === "builtin_reduce_error") {
            throw new Error(row.reason);
          }
        }
        return new Promise(function(resolve) {
          resolve(result2);
        }).then(postprocessAttachments(opts));
      }
      body = body || {};
      for (const key of Object.keys(fun)) {
        if (Array.isArray(fun[key])) {
          body[key] = fun[key];
        } else {
          body[key] = fun[key].toString();
        }
      }
      const response = yield db.fetch("_temp_view" + params, {
        headers: new h({ "Content-Type": "application/json" }),
        method: "POST",
        body: JSON.stringify(body)
      });
      ok = response.ok;
      const result = yield response.json();
      if (!ok) {
        result.status = response.status;
        throw generateErrorFromResponse(result);
      }
      return new Promise(function(resolve) {
        resolve(result);
      }).then(postprocessAttachments(opts));
    });
  }
  function customQuery(db, fun, opts) {
    return new Promise(function(resolve, reject) {
      db._query(fun, opts, function(err, res2) {
        if (err) {
          return reject(err);
        }
        resolve(res2);
      });
    });
  }
  function customViewCleanup(db) {
    return new Promise(function(resolve, reject) {
      db._viewCleanup(function(err, res2) {
        if (err) {
          return reject(err);
        }
        resolve(res2);
      });
    });
  }
  function defaultsTo(value) {
    return function(reason) {
      if (reason.status === 404) {
        return value;
      } else {
        throw reason;
      }
    };
  }
  function getDocsToPersist(docId, view, docIdsToChangesAndEmits) {
    return __async(this, null, function* () {
      const metaDocId = "_local/doc_" + docId;
      const defaultMetaDoc = { _id: metaDocId, keys: [] };
      const docData = docIdsToChangesAndEmits.get(docId);
      const indexableKeysToKeyValues = docData[0];
      const changes = docData[1];
      function getMetaDoc() {
        if (isGenOne(changes)) {
          return Promise.resolve(defaultMetaDoc);
        }
        return view.db.get(metaDocId).catch(defaultsTo(defaultMetaDoc));
      }
      function getKeyValueDocs(metaDoc2) {
        if (!metaDoc2.keys.length) {
          return Promise.resolve({ rows: [] });
        }
        return view.db.allDocs({
          keys: metaDoc2.keys,
          include_docs: true
        });
      }
      function processKeyValueDocs(metaDoc2, kvDocsRes) {
        const kvDocs = [];
        const oldKeys = /* @__PURE__ */ new Set();
        for (const row of kvDocsRes.rows) {
          const doc = row.doc;
          if (!doc) {
            continue;
          }
          kvDocs.push(doc);
          oldKeys.add(doc._id);
          doc._deleted = !indexableKeysToKeyValues.has(doc._id);
          if (!doc._deleted) {
            const keyValue = indexableKeysToKeyValues.get(doc._id);
            if ("value" in keyValue) {
              doc.value = keyValue.value;
            }
          }
        }
        const newKeys = mapToKeysArray(indexableKeysToKeyValues);
        for (const key of newKeys) {
          if (!oldKeys.has(key)) {
            const kvDoc = {
              _id: key
            };
            const keyValue = indexableKeysToKeyValues.get(key);
            if ("value" in keyValue) {
              kvDoc.value = keyValue.value;
            }
            kvDocs.push(kvDoc);
          }
        }
        metaDoc2.keys = uniq(newKeys.concat(metaDoc2.keys));
        kvDocs.push(metaDoc2);
        return kvDocs;
      }
      const metaDoc = yield getMetaDoc();
      const keyValueDocs = yield getKeyValueDocs(metaDoc);
      return processKeyValueDocs(metaDoc, keyValueDocs);
    });
  }
  function updatePurgeSeq(view) {
    return view.sourceDB.get("_local/purges").then(function(res2) {
      const purgeSeq = res2.purgeSeq;
      return view.db.get("_local/purgeSeq").then(function(res3) {
        return res3._rev;
      }).catch(defaultsTo(void 0)).then(function(rev) {
        return view.db.put({
          _id: "_local/purgeSeq",
          _rev: rev,
          purgeSeq
        });
      });
    }).catch(function(err) {
      if (err.status !== 404) {
        throw err;
      }
    });
  }
  function saveKeyValues(view, docIdsToChangesAndEmits, seq) {
    var seqDocId = "_local/lastSeq";
    return view.db.get(seqDocId).catch(defaultsTo({ _id: seqDocId, seq: 0 })).then(function(lastSeqDoc) {
      var docIds = mapToKeysArray(docIdsToChangesAndEmits);
      return Promise.all(docIds.map(function(docId) {
        return getDocsToPersist(docId, view, docIdsToChangesAndEmits);
      })).then(function(listOfDocsToPersist) {
        var docsToPersist = listOfDocsToPersist.flat();
        lastSeqDoc.seq = seq;
        docsToPersist.push(lastSeqDoc);
        return view.db.bulkDocs({ docs: docsToPersist });
      }).then(() => updatePurgeSeq(view));
    });
  }
  function getQueue(view) {
    const viewName = typeof view === "string" ? view : view.name;
    let queue = persistentQueues[viewName];
    if (!queue) {
      queue = persistentQueues[viewName] = new TaskQueue();
    }
    return queue;
  }
  function updateView(view, opts) {
    return __async(this, null, function* () {
      return sequentialize(getQueue(view), function() {
        return updateViewInQueue(view, opts);
      })();
    });
  }
  function updateViewInQueue(view, opts) {
    return __async(this, null, function* () {
      let mapResults;
      let doc;
      let taskId;
      function emit(key, value) {
        const output = { id: doc._id, key: normalizeKey(key) };
        if (typeof value !== "undefined" && value !== null) {
          output.value = normalizeKey(value);
        }
        mapResults.push(output);
      }
      const mapFun = mapper2(view.mapFun, emit);
      let currentSeq = view.seq || 0;
      function createTask() {
        return view.sourceDB.info().then(function(info) {
          taskId = view.sourceDB.activeTasks.add({
            name: "view_indexing",
            total_items: info.update_seq - currentSeq
          });
        });
      }
      function processChange(docIdsToChangesAndEmits, seq) {
        return function() {
          return saveKeyValues(view, docIdsToChangesAndEmits, seq);
        };
      }
      let indexed_docs = 0;
      const progress = {
        view: view.name,
        indexed_docs
      };
      view.sourceDB.emit("indexing", progress);
      const queue = new TaskQueue();
      function processNextBatch() {
        return __async(this, null, function* () {
          const response = yield view.sourceDB.changes({
            return_docs: true,
            conflicts: true,
            include_docs: true,
            style: "all_docs",
            since: currentSeq,
            limit: opts.changes_batch_size
          });
          const purges = yield getRecentPurges();
          return processBatch(response, purges);
        });
      }
      function getRecentPurges() {
        return view.db.get("_local/purgeSeq").then(function(res2) {
          return res2.purgeSeq;
        }).catch(defaultsTo(-1)).then(function(purgeSeq) {
          return view.sourceDB.get("_local/purges").then(function(res2) {
            const recentPurges = res2.purges.filter(function(purge, index) {
              return index > purgeSeq;
            }).map((purge) => purge.docId);
            const uniquePurges = recentPurges.filter(function(docId, index) {
              return recentPurges.indexOf(docId) === index;
            });
            return Promise.all(uniquePurges.map(function(docId) {
              return view.sourceDB.get(docId).then(function(doc2) {
                return { docId, doc: doc2 };
              }).catch(defaultsTo({ docId }));
            }));
          }).catch(defaultsTo([]));
        });
      }
      function processBatch(response, purges) {
        const results = response.results;
        if (!results.length && !purges.length) {
          return;
        }
        for (const purge of purges) {
          const index = results.findIndex(function(change) {
            return change.id === purge.docId;
          });
          if (index < 0) {
            const entry = {
              _id: purge.docId,
              doc: {
                _id: purge.docId,
                _deleted: 1
              },
              changes: []
            };
            if (purge.doc) {
              entry.doc = purge.doc;
              entry.changes.push({ rev: purge.doc._rev });
            }
            results.push(entry);
          }
        }
        const docIdsToChangesAndEmits = createDocIdsToChangesAndEmits(results);
        queue.add(processChange(docIdsToChangesAndEmits, currentSeq));
        indexed_docs = indexed_docs + results.length;
        const progress2 = {
          view: view.name,
          last_seq: response.last_seq,
          results_count: results.length,
          indexed_docs
        };
        view.sourceDB.emit("indexing", progress2);
        view.sourceDB.activeTasks.update(taskId, { completed_items: indexed_docs });
        if (results.length < opts.changes_batch_size) {
          return;
        }
        return processNextBatch();
      }
      function createDocIdsToChangesAndEmits(results) {
        const docIdsToChangesAndEmits = /* @__PURE__ */ new Map();
        for (const change of results) {
          if (change.doc._id[0] !== "_") {
            mapResults = [];
            doc = change.doc;
            if (!doc._deleted) {
              tryMap(view.sourceDB, mapFun, doc);
            }
            mapResults.sort(sortByKeyThenValue);
            const indexableKeysToKeyValues = createIndexableKeysToKeyValues(mapResults);
            docIdsToChangesAndEmits.set(change.doc._id, [
              indexableKeysToKeyValues,
              change.changes
            ]);
          }
          currentSeq = change.seq;
        }
        return docIdsToChangesAndEmits;
      }
      function createIndexableKeysToKeyValues(mapResults2) {
        const indexableKeysToKeyValues = /* @__PURE__ */ new Map();
        let lastKey;
        for (let i = 0, len = mapResults2.length; i < len; i++) {
          const emittedKeyValue = mapResults2[i];
          const complexKey = [emittedKeyValue.key, emittedKeyValue.id];
          if (i > 0 && collate(emittedKeyValue.key, lastKey) === 0) {
            complexKey.push(i);
          }
          indexableKeysToKeyValues.set(toIndexableString(complexKey), emittedKeyValue);
          lastKey = emittedKeyValue.key;
        }
        return indexableKeysToKeyValues;
      }
      try {
        yield createTask();
        yield processNextBatch();
        yield queue.finish();
        view.seq = currentSeq;
        view.sourceDB.activeTasks.remove(taskId);
      } catch (error) {
        view.sourceDB.activeTasks.remove(taskId, error);
      }
    });
  }
  function reduceView(view, results, options) {
    if (options.group_level === 0) {
      delete options.group_level;
    }
    const shouldGroup = options.group || options.group_level;
    const reduceFun = reducer2(view.reduceFun);
    const groups = [];
    const lvl = isNaN(options.group_level) ? Number.POSITIVE_INFINITY : options.group_level;
    for (const result of results) {
      const last = groups[groups.length - 1];
      let groupKey = shouldGroup ? result.key : null;
      if (shouldGroup && Array.isArray(groupKey)) {
        groupKey = groupKey.slice(0, lvl);
      }
      if (last && collate(last.groupKey, groupKey) === 0) {
        last.keys.push([result.key, result.id]);
        last.values.push(result.value);
        continue;
      }
      groups.push({
        keys: [[result.key, result.id]],
        values: [result.value],
        groupKey
      });
    }
    results = [];
    for (const group of groups) {
      const reduceTry = tryReduce(view.sourceDB, reduceFun, group.keys, group.values, false);
      if (reduceTry.error && reduceTry.error instanceof BuiltInError) {
        throw reduceTry.error;
      }
      results.push({
        // CouchDB just sets the value to null if a non-built-in errors out
        value: reduceTry.error ? null : reduceTry.output,
        key: group.groupKey
      });
    }
    return { rows: sliceResults(results, options.limit, options.skip) };
  }
  function queryView(view, opts) {
    return sequentialize(getQueue(view), function() {
      return queryViewInQueue(view, opts);
    })();
  }
  function queryViewInQueue(view, opts) {
    return __async(this, null, function* () {
      let totalRows;
      const shouldReduce = view.reduceFun && opts.reduce !== false;
      const skip = opts.skip || 0;
      if (typeof opts.keys !== "undefined" && !opts.keys.length) {
        opts.limit = 0;
        delete opts.keys;
      }
      function fetchFromView(viewOpts) {
        return __async(this, null, function* () {
          viewOpts.include_docs = true;
          const res2 = yield view.db.allDocs(viewOpts);
          totalRows = res2.total_rows;
          return res2.rows.map(function(result) {
            if ("value" in result.doc && typeof result.doc.value === "object" && result.doc.value !== null) {
              const keys = Object.keys(result.doc.value).sort();
              const expectedKeys = ["id", "key", "value"];
              if (!(keys < expectedKeys || keys > expectedKeys)) {
                return result.doc.value;
              }
            }
            const parsedKeyAndDocId = parseIndexableString(result.doc._id);
            return {
              key: parsedKeyAndDocId[0],
              id: parsedKeyAndDocId[1],
              value: "value" in result.doc ? result.doc.value : null
            };
          });
        });
      }
      function onMapResultsReady(rows) {
        return __async(this, null, function* () {
          let finalResults;
          if (shouldReduce) {
            finalResults = reduceView(view, rows, opts);
          } else if (typeof opts.keys === "undefined") {
            finalResults = {
              total_rows: totalRows,
              offset: skip,
              rows
            };
          } else {
            finalResults = {
              total_rows: totalRows,
              offset: skip,
              rows: sliceResults(rows, opts.limit, opts.skip)
            };
          }
          if (opts.update_seq) {
            finalResults.update_seq = view.seq;
          }
          if (opts.include_docs) {
            const docIds = uniq(rows.map(rowToDocId));
            const allDocsRes = yield view.sourceDB.allDocs({
              keys: docIds,
              include_docs: true,
              conflicts: opts.conflicts,
              attachments: opts.attachments,
              binary: opts.binary
            });
            const docIdsToDocs = /* @__PURE__ */ new Map();
            for (const row of allDocsRes.rows) {
              docIdsToDocs.set(row.id, row.doc);
            }
            for (const row of rows) {
              const docId = rowToDocId(row);
              const doc = docIdsToDocs.get(docId);
              if (doc) {
                row.doc = doc;
              }
            }
          }
          return finalResults;
        });
      }
      if (typeof opts.keys !== "undefined") {
        const keys = opts.keys;
        const fetchPromises = keys.map(function(key) {
          const viewOpts = {
            startkey: toIndexableString([key]),
            endkey: toIndexableString([key, {}])
          };
          if (opts.update_seq) {
            viewOpts.update_seq = true;
          }
          return fetchFromView(viewOpts);
        });
        const result = yield Promise.all(fetchPromises);
        const flattenedResult = result.flat();
        return onMapResultsReady(flattenedResult);
      } else {
        const viewOpts = {
          descending: opts.descending
        };
        if (opts.update_seq) {
          viewOpts.update_seq = true;
        }
        let startkey;
        let endkey;
        if ("start_key" in opts) {
          startkey = opts.start_key;
        }
        if ("startkey" in opts) {
          startkey = opts.startkey;
        }
        if ("end_key" in opts) {
          endkey = opts.end_key;
        }
        if ("endkey" in opts) {
          endkey = opts.endkey;
        }
        if (typeof startkey !== "undefined") {
          viewOpts.startkey = opts.descending ? toIndexableString([startkey, {}]) : toIndexableString([startkey]);
        }
        if (typeof endkey !== "undefined") {
          let inclusiveEnd = opts.inclusive_end !== false;
          if (opts.descending) {
            inclusiveEnd = !inclusiveEnd;
          }
          viewOpts.endkey = toIndexableString(
            inclusiveEnd ? [endkey, {}] : [endkey]
          );
        }
        if (typeof opts.key !== "undefined") {
          const keyStart = toIndexableString([opts.key]);
          const keyEnd = toIndexableString([opts.key, {}]);
          if (viewOpts.descending) {
            viewOpts.endkey = keyStart;
            viewOpts.startkey = keyEnd;
          } else {
            viewOpts.startkey = keyStart;
            viewOpts.endkey = keyEnd;
          }
        }
        if (!shouldReduce) {
          if (typeof opts.limit === "number") {
            viewOpts.limit = opts.limit;
          }
          viewOpts.skip = skip;
        }
        const result = yield fetchFromView(viewOpts);
        return onMapResultsReady(result);
      }
    });
  }
  function httpViewCleanup(db) {
    return __async(this, null, function* () {
      const response = yield db.fetch("_view_cleanup", {
        headers: new h({ "Content-Type": "application/json" }),
        method: "POST"
      });
      return response.json();
    });
  }
  function localViewCleanup(db) {
    return __async(this, null, function* () {
      try {
        const metaDoc = yield db.get("_local/" + localDocName);
        const docsToViews = /* @__PURE__ */ new Map();
        for (const fullViewName of Object.keys(metaDoc.views)) {
          const parts = parseViewName(fullViewName);
          const designDocName = "_design/" + parts[0];
          const viewName = parts[1];
          let views = docsToViews.get(designDocName);
          if (!views) {
            views = /* @__PURE__ */ new Set();
            docsToViews.set(designDocName, views);
          }
          views.add(viewName);
        }
        const opts = {
          keys: mapToKeysArray(docsToViews),
          include_docs: true
        };
        const res2 = yield db.allDocs(opts);
        const viewsToStatus = {};
        for (const row of res2.rows) {
          const ddocName = row.key.substring(8);
          for (const viewName of docsToViews.get(row.key)) {
            let fullViewName = ddocName + "/" + viewName;
            if (!metaDoc.views[fullViewName]) {
              fullViewName = viewName;
            }
            const viewDBNames = Object.keys(metaDoc.views[fullViewName]);
            const statusIsGood = row.doc && row.doc.views && row.doc.views[viewName];
            for (const viewDBName of viewDBNames) {
              viewsToStatus[viewDBName] = viewsToStatus[viewDBName] || statusIsGood;
            }
          }
        }
        const dbsToDelete = Object.keys(viewsToStatus).filter(function(viewDBName) {
          return !viewsToStatus[viewDBName];
        });
        const destroyPromises = dbsToDelete.map(function(viewDBName) {
          return sequentialize(getQueue(viewDBName), function() {
            return new db.constructor(viewDBName, db.__opts).destroy();
          })();
        });
        return Promise.all(destroyPromises).then(function() {
          return { ok: true };
        });
      } catch (err) {
        if (err.status === 404) {
          return { ok: true };
        } else {
          throw err;
        }
      }
    });
  }
  function queryPromised(db, fun, opts) {
    return __async(this, null, function* () {
      if (typeof db._query === "function") {
        return customQuery(db, fun, opts);
      }
      if (isRemote(db)) {
        return httpQuery(db, fun, opts);
      }
      const updateViewOpts = {
        changes_batch_size: db.__opts.view_update_changes_batch_size || CHANGES_BATCH_SIZE
      };
      if (typeof fun !== "string") {
        checkQueryParseError(opts, fun);
        tempViewQueue.add(function() {
          return __async(this, null, function* () {
            const view = yield createView(
              /* sourceDB */
              db,
              /* viewName */
              "temp_view/temp_view",
              /* mapFun */
              fun.map,
              /* reduceFun */
              fun.reduce,
              /* temporary */
              true,
              /* localDocName */
              localDocName
            );
            return fin(
              updateView(view, updateViewOpts).then(
                function() {
                  return queryView(view, opts);
                }
              ),
              function() {
                return view.db.destroy();
              }
            );
          });
        });
        return tempViewQueue.finish();
      } else {
        const fullViewName = fun;
        const parts = parseViewName(fullViewName);
        const designDocName = parts[0];
        const viewName = parts[1];
        const doc = yield db.get("_design/" + designDocName);
        fun = doc.views && doc.views[viewName];
        if (!fun) {
          throw new NotFoundError(`ddoc ${doc._id} has no view named ${viewName}`);
        }
        ddocValidator2(doc, viewName);
        checkQueryParseError(opts, fun);
        const view = yield createView(
          /* sourceDB */
          db,
          /* viewName */
          fullViewName,
          /* mapFun */
          fun.map,
          /* reduceFun */
          fun.reduce,
          /* temporary */
          false,
          /* localDocName */
          localDocName
        );
        if (opts.stale === "ok" || opts.stale === "update_after") {
          if (opts.stale === "update_after") {
            nextTick(function() {
              updateView(view, updateViewOpts);
            });
          }
          return queryView(view, opts);
        } else {
          yield updateView(view, updateViewOpts);
          return queryView(view, opts);
        }
      }
    });
  }
  function abstractQuery(fun, opts, callback) {
    const db = this;
    if (typeof opts === "function") {
      callback = opts;
      opts = {};
    }
    opts = opts ? coerceOptions(opts) : {};
    if (typeof fun === "function") {
      fun = { map: fun };
    }
    const promise = Promise.resolve().then(function() {
      return queryPromised(db, fun, opts);
    });
    promisedCallback(promise, callback);
    return promise;
  }
  const abstractViewCleanup = callbackify(function() {
    const db = this;
    if (typeof db._viewCleanup === "function") {
      return customViewCleanup(db);
    }
    if (isRemote(db)) {
      return httpViewCleanup(db);
    }
    return localViewCleanup(db);
  });
  return {
    query: abstractQuery,
    viewCleanup: abstractViewCleanup
  };
}
var index_es_default = createAbstractMapReduce;

// node_modules/pouchdb-selector-core/lib/index.es.js
function getFieldFromDoc(doc, parsedField) {
  var value = doc;
  for (var i = 0, len = parsedField.length; i < len; i++) {
    var key = parsedField[i];
    value = value[key];
    if (!value) {
      break;
    }
  }
  return value;
}
function setFieldInDoc(doc, parsedField, value) {
  for (var i = 0, len = parsedField.length; i < len - 1; i++) {
    var elem = parsedField[i];
    doc = doc[elem] = doc[elem] || {};
  }
  doc[parsedField[len - 1]] = value;
}
function compare(left, right) {
  return left < right ? -1 : left > right ? 1 : 0;
}
function parseField(fieldName) {
  var fields = [];
  var current = "";
  for (var i = 0, len = fieldName.length; i < len; i++) {
    var ch = fieldName[i];
    if (i > 0 && fieldName[i - 1] === "\\" && (ch === "$" || ch === ".")) {
      current = current.substring(0, current.length - 1) + ch;
    } else if (ch === ".") {
      fields.push(current);
      current = "";
    } else {
      current += ch;
    }
  }
  fields.push(current);
  return fields;
}
var combinationFields = ["$or", "$nor", "$not"];
function isCombinationalField(field) {
  return combinationFields.indexOf(field) > -1;
}
function getKey(obj) {
  return Object.keys(obj)[0];
}
function getValue(obj) {
  return obj[getKey(obj)];
}
function mergeAndedSelectors(selectors) {
  var res2 = {};
  var first = { $or: true, $nor: true };
  selectors.forEach(function(selector) {
    Object.keys(selector).forEach(function(field) {
      var matcher = selector[field];
      if (typeof matcher !== "object") {
        matcher = { $eq: matcher };
      }
      if (isCombinationalField(field)) {
        if (matcher instanceof Array) {
          if (first[field]) {
            first[field] = false;
            res2[field] = matcher;
            return;
          }
          var entries = [];
          res2[field].forEach(function(existing) {
            Object.keys(matcher).forEach(function(key) {
              var m = matcher[key];
              var longest = Math.max(Object.keys(existing).length, Object.keys(m).length);
              var merged = mergeAndedSelectors([existing, m]);
              if (Object.keys(merged).length <= longest) {
                return;
              }
              entries.push(merged);
            });
          });
          res2[field] = entries;
        } else {
          res2[field] = mergeAndedSelectors([matcher]);
        }
      } else {
        var fieldMatchers = res2[field] = res2[field] || {};
        Object.keys(matcher).forEach(function(operator) {
          var value = matcher[operator];
          if (operator === "$gt" || operator === "$gte") {
            return mergeGtGte(operator, value, fieldMatchers);
          } else if (operator === "$lt" || operator === "$lte") {
            return mergeLtLte(operator, value, fieldMatchers);
          } else if (operator === "$ne") {
            return mergeNe(value, fieldMatchers);
          } else if (operator === "$eq") {
            return mergeEq(value, fieldMatchers);
          } else if (operator === "$regex") {
            return mergeRegex(value, fieldMatchers);
          }
          fieldMatchers[operator] = value;
        });
      }
    });
  });
  return res2;
}
function mergeGtGte(operator, value, fieldMatchers) {
  if (typeof fieldMatchers.$eq !== "undefined") {
    return;
  }
  if (typeof fieldMatchers.$gte !== "undefined") {
    if (operator === "$gte") {
      if (value > fieldMatchers.$gte) {
        fieldMatchers.$gte = value;
      }
    } else {
      if (value >= fieldMatchers.$gte) {
        delete fieldMatchers.$gte;
        fieldMatchers.$gt = value;
      }
    }
  } else if (typeof fieldMatchers.$gt !== "undefined") {
    if (operator === "$gte") {
      if (value > fieldMatchers.$gt) {
        delete fieldMatchers.$gt;
        fieldMatchers.$gte = value;
      }
    } else {
      if (value > fieldMatchers.$gt) {
        fieldMatchers.$gt = value;
      }
    }
  } else {
    fieldMatchers[operator] = value;
  }
}
function mergeLtLte(operator, value, fieldMatchers) {
  if (typeof fieldMatchers.$eq !== "undefined") {
    return;
  }
  if (typeof fieldMatchers.$lte !== "undefined") {
    if (operator === "$lte") {
      if (value < fieldMatchers.$lte) {
        fieldMatchers.$lte = value;
      }
    } else {
      if (value <= fieldMatchers.$lte) {
        delete fieldMatchers.$lte;
        fieldMatchers.$lt = value;
      }
    }
  } else if (typeof fieldMatchers.$lt !== "undefined") {
    if (operator === "$lte") {
      if (value < fieldMatchers.$lt) {
        delete fieldMatchers.$lt;
        fieldMatchers.$lte = value;
      }
    } else {
      if (value < fieldMatchers.$lt) {
        fieldMatchers.$lt = value;
      }
    }
  } else {
    fieldMatchers[operator] = value;
  }
}
function mergeNe(value, fieldMatchers) {
  if ("$ne" in fieldMatchers) {
    fieldMatchers.$ne.push(value);
  } else {
    fieldMatchers.$ne = [value];
  }
}
function mergeEq(value, fieldMatchers) {
  delete fieldMatchers.$gt;
  delete fieldMatchers.$gte;
  delete fieldMatchers.$lt;
  delete fieldMatchers.$lte;
  delete fieldMatchers.$ne;
  fieldMatchers.$eq = value;
}
function mergeRegex(value, fieldMatchers) {
  if ("$regex" in fieldMatchers) {
    fieldMatchers.$regex.push(value);
  } else {
    fieldMatchers.$regex = [value];
  }
}
function mergeAndedSelectorsNested(obj) {
  for (var prop in obj) {
    if (Array.isArray(obj)) {
      for (var i in obj) {
        if (obj[i]["$and"]) {
          obj[i] = mergeAndedSelectors(obj[i]["$and"]);
        }
      }
    }
    var value = obj[prop];
    if (typeof value === "object") {
      mergeAndedSelectorsNested(value);
    }
  }
  return obj;
}
function isAndInSelector(obj, isAnd) {
  for (var prop in obj) {
    if (prop === "$and") {
      isAnd = true;
    }
    var value = obj[prop];
    if (typeof value === "object") {
      isAnd = isAndInSelector(value, isAnd);
    }
  }
  return isAnd;
}
function massageSelector(input) {
  var result = clone(input);
  if (isAndInSelector(result, false)) {
    result = mergeAndedSelectorsNested(result);
    if ("$and" in result) {
      result = mergeAndedSelectors(result["$and"]);
    }
  }
  ["$or", "$nor"].forEach(function(orOrNor) {
    if (orOrNor in result) {
      result[orOrNor].forEach(function(subSelector) {
        var fields2 = Object.keys(subSelector);
        for (var i2 = 0; i2 < fields2.length; i2++) {
          var field2 = fields2[i2];
          var matcher2 = subSelector[field2];
          if (typeof matcher2 !== "object" || matcher2 === null) {
            subSelector[field2] = { $eq: matcher2 };
          }
        }
      });
    }
  });
  if ("$not" in result) {
    result["$not"] = mergeAndedSelectors([result["$not"]]);
  }
  var fields = Object.keys(result);
  for (var i = 0; i < fields.length; i++) {
    var field = fields[i];
    var matcher = result[field];
    if (typeof matcher !== "object" || matcher === null) {
      matcher = { $eq: matcher };
    }
    result[field] = matcher;
  }
  normalizeArrayOperators(result);
  return result;
}
function normalizeArrayOperators(selector) {
  Object.keys(selector).forEach(function(field) {
    var matcher = selector[field];
    if (Array.isArray(matcher)) {
      matcher.forEach(function(matcherItem) {
        if (matcherItem && typeof matcherItem === "object") {
          normalizeArrayOperators(matcherItem);
        }
      });
    } else if (field === "$ne") {
      selector.$ne = [matcher];
    } else if (field === "$regex") {
      selector.$regex = [matcher];
    } else if (matcher && typeof matcher === "object") {
      normalizeArrayOperators(matcher);
    }
  });
}
function createFieldSorter(sort) {
  function getFieldValuesAsArray(doc) {
    return sort.map(function(sorting) {
      var fieldName = getKey(sorting);
      var parsedField = parseField(fieldName);
      var docFieldValue = getFieldFromDoc(doc, parsedField);
      return docFieldValue;
    });
  }
  return function(aRow, bRow) {
    var aFieldValues = getFieldValuesAsArray(aRow.doc);
    var bFieldValues = getFieldValuesAsArray(bRow.doc);
    var collation = collate(aFieldValues, bFieldValues);
    if (collation !== 0) {
      return collation;
    }
    return compare(aRow.doc._id, bRow.doc._id);
  };
}
function filterInMemoryFields(rows, requestDef, inMemoryFields) {
  rows = rows.filter(function(row) {
    return rowFilter(row.doc, requestDef.selector, inMemoryFields);
  });
  if (requestDef.sort) {
    var fieldSorter = createFieldSorter(requestDef.sort);
    rows = rows.sort(fieldSorter);
    if (typeof requestDef.sort[0] !== "string" && getValue(requestDef.sort[0]) === "desc") {
      rows = rows.reverse();
    }
  }
  if ("limit" in requestDef || "skip" in requestDef) {
    var skip = requestDef.skip || 0;
    var limit = ("limit" in requestDef ? requestDef.limit : rows.length) + skip;
    rows = rows.slice(skip, limit);
  }
  return rows;
}
function rowFilter(doc, selector, inMemoryFields) {
  return inMemoryFields.every(function(field) {
    var matcher = selector[field];
    var parsedField = parseField(field);
    var docFieldValue = getFieldFromDoc(doc, parsedField);
    if (isCombinationalField(field)) {
      return matchCominationalSelector(field, matcher, doc);
    }
    return matchSelector(matcher, doc, parsedField, docFieldValue);
  });
}
function matchSelector(matcher, doc, parsedField, docFieldValue) {
  if (!matcher) {
    return true;
  }
  if (typeof matcher === "object") {
    return Object.keys(matcher).every(function(maybeUserOperator) {
      var userValue = matcher[maybeUserOperator];
      if (maybeUserOperator.indexOf("$") === 0) {
        return match(maybeUserOperator, doc, userValue, parsedField, docFieldValue);
      } else {
        var subParsedField = parseField(maybeUserOperator);
        if (docFieldValue === void 0 && typeof userValue !== "object" && subParsedField.length > 0) {
          return false;
        }
        var subDocFieldValue = getFieldFromDoc(docFieldValue, subParsedField);
        if (typeof userValue === "object") {
          return matchSelector(userValue, doc, parsedField, subDocFieldValue);
        }
        return match("$eq", doc, userValue, subParsedField, subDocFieldValue);
      }
    });
  }
  return matcher === docFieldValue;
}
function matchCominationalSelector(field, matcher, doc) {
  if (field === "$or") {
    return matcher.some(function(orMatchers) {
      return rowFilter(doc, orMatchers, Object.keys(orMatchers));
    });
  }
  if (field === "$not") {
    return !rowFilter(doc, matcher, Object.keys(matcher));
  }
  return !matcher.find(function(orMatchers) {
    return rowFilter(doc, orMatchers, Object.keys(orMatchers));
  });
}
function match(userOperator, doc, userValue, parsedField, docFieldValue) {
  if (!matchers[userOperator]) {
    throw new Error('unknown operator "' + userOperator + '" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');
  }
  return matchers[userOperator](doc, userValue, parsedField, docFieldValue);
}
function fieldExists(docFieldValue) {
  return typeof docFieldValue !== "undefined" && docFieldValue !== null;
}
function fieldIsNotUndefined(docFieldValue) {
  return typeof docFieldValue !== "undefined";
}
function modField(docFieldValue, userValue) {
  if (typeof docFieldValue !== "number" || parseInt(docFieldValue, 10) !== docFieldValue) {
    return false;
  }
  var divisor = userValue[0];
  var mod = userValue[1];
  return docFieldValue % divisor === mod;
}
function arrayContainsValue(docFieldValue, userValue) {
  return userValue.some(function(val) {
    if (docFieldValue instanceof Array) {
      return docFieldValue.some(function(docFieldValueItem) {
        return collate(val, docFieldValueItem) === 0;
      });
    }
    return collate(val, docFieldValue) === 0;
  });
}
function arrayContainsAllValues(docFieldValue, userValue) {
  return userValue.every(function(val) {
    return docFieldValue.some(function(docFieldValueItem) {
      return collate(val, docFieldValueItem) === 0;
    });
  });
}
function arraySize(docFieldValue, userValue) {
  return docFieldValue.length === userValue;
}
function regexMatch(docFieldValue, userValue) {
  var re = new RegExp(userValue);
  return re.test(docFieldValue);
}
function typeMatch(docFieldValue, userValue) {
  switch (userValue) {
    case "null":
      return docFieldValue === null;
    case "boolean":
      return typeof docFieldValue === "boolean";
    case "number":
      return typeof docFieldValue === "number";
    case "string":
      return typeof docFieldValue === "string";
    case "array":
      return docFieldValue instanceof Array;
    case "object":
      return {}.toString.call(docFieldValue) === "[object Object]";
  }
}
var matchers = {
  "$elemMatch": function(doc, userValue, parsedField, docFieldValue) {
    if (!Array.isArray(docFieldValue)) {
      return false;
    }
    if (docFieldValue.length === 0) {
      return false;
    }
    if (typeof docFieldValue[0] === "object" && docFieldValue[0] !== null) {
      return docFieldValue.some(function(val) {
        return rowFilter(val, userValue, Object.keys(userValue));
      });
    }
    return docFieldValue.some(function(val) {
      return matchSelector(userValue, doc, parsedField, val);
    });
  },
  "$allMatch": function(doc, userValue, parsedField, docFieldValue) {
    if (!Array.isArray(docFieldValue)) {
      return false;
    }
    if (docFieldValue.length === 0) {
      return false;
    }
    if (typeof docFieldValue[0] === "object" && docFieldValue[0] !== null) {
      return docFieldValue.every(function(val) {
        return rowFilter(val, userValue, Object.keys(userValue));
      });
    }
    return docFieldValue.every(function(val) {
      return matchSelector(userValue, doc, parsedField, val);
    });
  },
  "$eq": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) === 0;
  },
  "$gte": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) >= 0;
  },
  "$gt": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) > 0;
  },
  "$lte": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) <= 0;
  },
  "$lt": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) < 0;
  },
  "$exists": function(doc, userValue, parsedField, docFieldValue) {
    if (userValue) {
      return fieldIsNotUndefined(docFieldValue);
    }
    return !fieldIsNotUndefined(docFieldValue);
  },
  "$mod": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && modField(docFieldValue, userValue);
  },
  "$ne": function(doc, userValue, parsedField, docFieldValue) {
    return userValue.every(function(neValue) {
      return collate(docFieldValue, neValue) !== 0;
    });
  },
  "$in": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && arrayContainsValue(docFieldValue, userValue);
  },
  "$nin": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && !arrayContainsValue(docFieldValue, userValue);
  },
  "$size": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && Array.isArray(docFieldValue) && arraySize(docFieldValue, userValue);
  },
  "$all": function(doc, userValue, parsedField, docFieldValue) {
    return Array.isArray(docFieldValue) && arrayContainsAllValues(docFieldValue, userValue);
  },
  "$regex": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && typeof docFieldValue == "string" && userValue.every(function(regexValue) {
      return regexMatch(docFieldValue, regexValue);
    });
  },
  "$type": function(doc, userValue, parsedField, docFieldValue) {
    return typeMatch(docFieldValue, userValue);
  }
};
function matchesSelector(doc, selector) {
  if (typeof selector !== "object") {
    throw new Error("Selector error: expected a JSON object");
  }
  selector = massageSelector(selector);
  var row = {
    doc
  };
  var rowsMatched = filterInMemoryFields([row], { selector }, Object.keys(selector));
  return rowsMatched && rowsMatched.length === 1;
}

// node_modules/pouchdb-find/lib/index-browser.es.js
var nativeFlat = (...args) => args.flat(Infinity);
var polyFlat = (...args) => {
  let res2 = [];
  for (const subArr of args) {
    if (Array.isArray(subArr)) {
      res2 = res2.concat(polyFlat(...subArr));
    } else {
      res2.push(subArr);
    }
  }
  return res2;
};
var flatten = typeof Array.prototype.flat === "function" ? nativeFlat : polyFlat;
function mergeObjects(arr) {
  const res2 = {};
  for (const element of arr) {
    Object.assign(res2, element);
  }
  return res2;
}
function pick(obj, arr) {
  const res2 = {};
  for (const field of arr) {
    const parsedField = parseField(field);
    const value = getFieldFromDoc(obj, parsedField);
    if (typeof value !== "undefined") {
      setFieldInDoc(res2, parsedField, value);
    }
  }
  return res2;
}
function oneArrayIsSubArrayOfOther(left, right) {
  for (let i = 0, len = Math.min(left.length, right.length); i < len; i++) {
    if (left[i] !== right[i]) {
      return false;
    }
  }
  return true;
}
function oneArrayIsStrictSubArrayOfOther(left, right) {
  if (left.length > right.length) {
    return false;
  }
  return oneArrayIsSubArrayOfOther(left, right);
}
function oneSetIsSubArrayOfOther(left, right) {
  left = left.slice();
  for (const field of right) {
    if (!left.length) {
      break;
    }
    const leftIdx = left.indexOf(field);
    if (leftIdx === -1) {
      return false;
    } else {
      left.splice(leftIdx, 1);
    }
  }
  return true;
}
function arrayToObject(arr) {
  const res2 = {};
  for (const field of arr) {
    res2[field] = true;
  }
  return res2;
}
function max(arr, fun) {
  let max2 = null;
  let maxScore = -1;
  for (const element of arr) {
    const score = fun(element);
    if (score > maxScore) {
      maxScore = score;
      max2 = element;
    }
  }
  return max2;
}
function arrayEquals(arr1, arr2) {
  if (arr1.length !== arr2.length) {
    return false;
  }
  for (let i = 0, len = arr1.length; i < len; i++) {
    if (arr1[i] !== arr2[i]) {
      return false;
    }
  }
  return true;
}
function uniq2(arr) {
  return Array.from(new Set(arr));
}
function resolveToCallback(fun) {
  return function(...args) {
    const maybeCallback = args[args.length - 1];
    if (typeof maybeCallback === "function") {
      const fulfilled = maybeCallback.bind(null, null);
      const rejected = maybeCallback.bind(null);
      fun.apply(this, args.slice(0, -1)).then(fulfilled, rejected);
    } else {
      return fun.apply(this, args);
    }
  };
}
function massageCreateIndexRequest(requestDef) {
  requestDef = clone(requestDef);
  if (!requestDef.index) {
    requestDef.index = {};
  }
  for (const key of ["type", "name", "ddoc"]) {
    if (requestDef.index[key]) {
      requestDef[key] = requestDef.index[key];
      delete requestDef.index[key];
    }
  }
  if (requestDef.fields) {
    requestDef.index.fields = requestDef.fields;
    delete requestDef.fields;
  }
  if (!requestDef.type) {
    requestDef.type = "json";
  }
  return requestDef;
}
function isNonNullObject(value) {
  return typeof value === "object" && value !== null;
}
function checkFieldValueType(name, value, isHttp) {
  let message = "";
  let received = value;
  let addReceived = true;
  if (["$in", "$nin", "$or", "$and", "$mod", "$nor", "$all"].indexOf(name) !== -1) {
    if (!Array.isArray(value)) {
      message = "Query operator " + name + " must be an array.";
    }
  }
  if (["$not", "$elemMatch", "$allMatch"].indexOf(name) !== -1) {
    if (!(!Array.isArray(value) && isNonNullObject(value))) {
      message = "Query operator " + name + " must be an object.";
    }
  }
  if (name === "$mod" && Array.isArray(value)) {
    if (value.length !== 2) {
      message = "Query operator $mod must be in the format [divisor, remainder], where divisor and remainder are both integers.";
    } else {
      const divisor = value[0];
      const mod = value[1];
      if (divisor === 0) {
        message = "Query operator $mod's divisor cannot be 0, cannot divide by zero.";
        addReceived = false;
      }
      if (typeof divisor !== "number" || parseInt(divisor, 10) !== divisor) {
        message = "Query operator $mod's divisor is not an integer.";
        received = divisor;
      }
      if (parseInt(mod, 10) !== mod) {
        message = "Query operator $mod's remainder is not an integer.";
        received = mod;
      }
    }
  }
  if (name === "$exists") {
    if (typeof value !== "boolean") {
      message = "Query operator $exists must be a boolean.";
    }
  }
  if (name === "$type") {
    const allowed = ["null", "boolean", "number", "string", "array", "object"];
    const allowedStr = '"' + allowed.slice(0, allowed.length - 1).join('", "') + '", or "' + allowed[allowed.length - 1] + '"';
    if (typeof value !== "string") {
      message = "Query operator $type must be a string. Supported values: " + allowedStr + ".";
    } else if (allowed.indexOf(value) == -1) {
      message = "Query operator $type must be a string. Supported values: " + allowedStr + ".";
    }
  }
  if (name === "$size") {
    if (parseInt(value, 10) !== value) {
      message = "Query operator $size must be a integer.";
    }
  }
  if (name === "$regex") {
    if (typeof value !== "string") {
      if (isHttp) {
        message = "Query operator $regex must be a string.";
      } else if (!(value instanceof RegExp)) {
        message = "Query operator $regex must be a string or an instance of a javascript regular expression.";
      }
    }
  }
  if (message) {
    if (addReceived) {
      const type = received === null ? " " : Array.isArray(received) ? " array" : " " + typeof received;
      const receivedStr = isNonNullObject(received) ? JSON.stringify(received, null, "	") : received;
      message += " Received" + type + ": " + receivedStr;
    }
    throw new Error(message);
  }
}
var requireValidation = ["$all", "$allMatch", "$and", "$elemMatch", "$exists", "$in", "$mod", "$nin", "$nor", "$not", "$or", "$regex", "$size", "$type"];
var arrayTypeComparisonOperators = ["$in", "$nin", "$mod", "$all"];
var equalityOperators = ["$eq", "$gt", "$gte", "$lt", "$lte"];
function validateSelector(input, isHttp) {
  if (Array.isArray(input)) {
    for (const entry of input) {
      if (isNonNullObject(entry)) {
        validateSelector(entry, isHttp);
      }
    }
  } else {
    for (const [key, value] of Object.entries(input)) {
      if (requireValidation.indexOf(key) !== -1) {
        checkFieldValueType(key, value, isHttp);
      }
      if (equalityOperators.indexOf(key) !== -1) {
        continue;
      }
      if (arrayTypeComparisonOperators.indexOf(key) !== -1) {
        continue;
      }
      if (isNonNullObject(value)) {
        validateSelector(value, isHttp);
      }
    }
  }
}
function dbFetch(db, path, opts) {
  return __async(this, null, function* () {
    if (opts.body) {
      opts.body = JSON.stringify(opts.body);
      opts.headers = new h({ "Content-type": "application/json" });
    }
    const response = yield db.fetch(path, opts);
    const json = yield response.json();
    if (!response.ok) {
      json.status = response.status;
      const pouchError = createError(json);
      throw generateErrorFromResponse(pouchError);
    }
    return json;
  });
}
function createIndex(db, requestDef) {
  return __async(this, null, function* () {
    return yield dbFetch(db, "_index", {
      method: "POST",
      body: massageCreateIndexRequest(requestDef)
    });
  });
}
function find(db, requestDef) {
  return __async(this, null, function* () {
    validateSelector(requestDef.selector, true);
    return yield dbFetch(db, "_find", {
      method: "POST",
      body: requestDef
    });
  });
}
function explain(db, requestDef) {
  return __async(this, null, function* () {
    return yield dbFetch(db, "_explain", {
      method: "POST",
      body: requestDef
    });
  });
}
function getIndexes(db) {
  return __async(this, null, function* () {
    return yield dbFetch(db, "_index", {
      method: "GET"
    });
  });
}
function deleteIndex(db, indexDef) {
  return __async(this, null, function* () {
    const ddoc = indexDef.ddoc;
    const type = indexDef.type || "json";
    const name = indexDef.name;
    if (!ddoc) {
      throw new Error("you must provide an index's ddoc");
    }
    if (!name) {
      throw new Error("you must provide an index's name");
    }
    const url = "_index/" + [ddoc, type, name].map(encodeURIComponent).join("/");
    return yield dbFetch(db, url, { method: "DELETE" });
  });
}
function getDeepValue(value, path) {
  for (const key of path) {
    value = value[key];
    if (value === void 0) {
      return void 0;
    }
  }
  return value;
}
function createDeepMultiMapper(fields, emit, selector) {
  return function(doc) {
    if (selector && !matchesSelector(doc, selector)) {
      return;
    }
    const toEmit = [];
    for (const field of fields) {
      const value = getDeepValue(doc, parseField(field));
      if (value === void 0) {
        return;
      }
      toEmit.push(value);
    }
    emit(toEmit);
  };
}
function createDeepSingleMapper(field, emit, selector) {
  const parsedField = parseField(field);
  return function(doc) {
    if (selector && !matchesSelector(doc, selector)) {
      return;
    }
    const value = getDeepValue(doc, parsedField);
    if (value !== void 0) {
      emit(value);
    }
  };
}
function createShallowSingleMapper(field, emit, selector) {
  return function(doc) {
    if (selector && !matchesSelector(doc, selector)) {
      return;
    }
    emit(doc[field]);
  };
}
function createShallowMultiMapper(fields, emit, selector) {
  return function(doc) {
    if (selector && !matchesSelector(doc, selector)) {
      return;
    }
    const toEmit = fields.map((field) => doc[field]);
    emit(toEmit);
  };
}
function checkShallow(fields) {
  return fields.every((field) => field.indexOf(".") === -1);
}
function createMapper(fields, emit, selector) {
  const isShallow = checkShallow(fields);
  const isSingle = fields.length === 1;
  if (isShallow) {
    if (isSingle) {
      return createShallowSingleMapper(fields[0], emit, selector);
    } else {
      return createShallowMultiMapper(fields, emit, selector);
    }
  } else {
    if (isSingle) {
      return createDeepSingleMapper(fields[0], emit, selector);
    } else {
      return createDeepMultiMapper(fields, emit, selector);
    }
  }
}
function mapper(mapFunDef, emit) {
  const fields = Object.keys(mapFunDef.fields);
  const partialSelector = mapFunDef.partial_filter_selector;
  return createMapper(fields, emit, partialSelector);
}
function reducer() {
  throw new Error("reduce not supported");
}
function ddocValidator(ddoc, viewName) {
  const view = ddoc.views[viewName];
  if (!view.map || !view.map.fields) {
    throw new Error("ddoc " + ddoc._id + " with view " + viewName + " doesn't have map.fields defined. maybe it wasn't created by this plugin?");
  }
}
var abstractMapper = index_es_default(
  /* localDocName */
  "indexes",
  mapper,
  reducer,
  ddocValidator
);
function abstractMapper$1(db) {
  if (db._customFindAbstractMapper) {
    return {
      // Calls the _customFindAbstractMapper, but with a third argument:
      // the standard findAbstractMapper query/viewCleanup.
      // This allows the indexeddb adapter to support partial_filter_selector.
      query: function addQueryFallback(signature, opts) {
        const fallback = abstractMapper.query.bind(this);
        return db._customFindAbstractMapper.query.call(this, signature, opts, fallback);
      },
      viewCleanup: function addViewCleanupFallback() {
        const fallback = abstractMapper.viewCleanup.bind(this);
        return db._customFindAbstractMapper.viewCleanup.call(this, fallback);
      }
    };
  }
  return abstractMapper;
}
function massageSort(sort) {
  if (!Array.isArray(sort)) {
    throw new Error("invalid sort json - should be an array");
  }
  return sort.map(function(sorting) {
    if (typeof sorting === "string") {
      const obj = {};
      obj[sorting] = "asc";
      return obj;
    } else {
      return sorting;
    }
  });
}
var ddocIdPrefix = /^_design\//;
function massageUseIndex(useIndex) {
  let cleanedUseIndex = [];
  if (typeof useIndex === "string") {
    cleanedUseIndex.push(useIndex);
  } else {
    cleanedUseIndex = useIndex;
  }
  return cleanedUseIndex.map(function(name) {
    return name.replace(ddocIdPrefix, "");
  });
}
function massageIndexDef(indexDef) {
  indexDef.fields = indexDef.fields.map(function(field) {
    if (typeof field === "string") {
      const obj = {};
      obj[field] = "asc";
      return obj;
    }
    return field;
  });
  if (indexDef.partial_filter_selector) {
    indexDef.partial_filter_selector = massageSelector(
      indexDef.partial_filter_selector
    );
  }
  return indexDef;
}
function getKeyFromDoc(doc, index) {
  return index.def.fields.map((obj) => {
    const field = getKey(obj);
    return getFieldFromDoc(doc, parseField(field));
  });
}
function filterInclusiveStart(rows, targetValue, index) {
  const indexFields = index.def.fields;
  let startAt = 0;
  for (const row of rows) {
    let docKey = getKeyFromDoc(row.doc, index);
    if (indexFields.length === 1) {
      docKey = docKey[0];
    } else {
      while (docKey.length > targetValue.length) {
        docKey.pop();
      }
    }
    if (Math.abs(collate(docKey, targetValue)) > 0) {
      break;
    }
    ++startAt;
  }
  return startAt > 0 ? rows.slice(startAt) : rows;
}
function reverseOptions(opts) {
  const newOpts = clone(opts);
  delete newOpts.startkey;
  delete newOpts.endkey;
  delete newOpts.inclusive_start;
  delete newOpts.inclusive_end;
  if ("endkey" in opts) {
    newOpts.startkey = opts.endkey;
  }
  if ("startkey" in opts) {
    newOpts.endkey = opts.startkey;
  }
  if ("inclusive_start" in opts) {
    newOpts.inclusive_end = opts.inclusive_start;
  }
  if ("inclusive_end" in opts) {
    newOpts.inclusive_start = opts.inclusive_end;
  }
  return newOpts;
}
function validateIndex(index) {
  const ascFields = index.fields.filter(function(field) {
    return getValue(field) === "asc";
  });
  if (ascFields.length !== 0 && ascFields.length !== index.fields.length) {
    throw new Error("unsupported mixed sorting");
  }
}
function validateSort(requestDef, index) {
  if (index.defaultUsed && requestDef.sort) {
    const noneIdSorts = requestDef.sort.filter(function(sortItem) {
      return Object.keys(sortItem)[0] !== "_id";
    }).map(function(sortItem) {
      return Object.keys(sortItem)[0];
    });
    if (noneIdSorts.length > 0) {
      throw new Error('Cannot sort on field(s) "' + noneIdSorts.join(",") + '" when using the default index');
    }
  }
  if (index.defaultUsed) {
    return;
  }
}
function validateFindRequest(requestDef) {
  if (typeof requestDef.selector !== "object") {
    throw new Error("you must provide a selector when you find()");
  }
}
function getUserFields(selector, sort) {
  const selectorFields = Object.keys(selector);
  const sortFields = sort ? sort.map(getKey) : [];
  let userFields;
  if (selectorFields.length >= sortFields.length) {
    userFields = selectorFields;
  } else {
    userFields = sortFields;
  }
  if (sortFields.length === 0) {
    return {
      fields: userFields
    };
  }
  userFields = userFields.sort(function(left, right) {
    let leftIdx = sortFields.indexOf(left);
    if (leftIdx === -1) {
      leftIdx = Number.MAX_VALUE;
    }
    let rightIdx = sortFields.indexOf(right);
    if (rightIdx === -1) {
      rightIdx = Number.MAX_VALUE;
    }
    return leftIdx < rightIdx ? -1 : leftIdx > rightIdx ? 1 : 0;
  });
  return {
    fields: userFields,
    sortOrder: sort.map(getKey)
  };
}
function createIndex$1(db, requestDef) {
  return __async(this, null, function* () {
    requestDef = massageCreateIndexRequest(requestDef);
    const originalIndexDef = clone(requestDef.index);
    requestDef.index = massageIndexDef(requestDef.index);
    validateIndex(requestDef.index);
    let md5;
    function getMd5() {
      return md5 || (md5 = stringMd5(JSON.stringify(requestDef)));
    }
    const viewName = requestDef.name || "idx-" + getMd5();
    const ddocName = requestDef.ddoc || "idx-" + getMd5();
    const ddocId = "_design/" + ddocName;
    let hasInvalidLanguage = false;
    let viewExists = false;
    function updateDdoc(doc) {
      if (doc._rev && doc.language !== "query") {
        hasInvalidLanguage = true;
      }
      doc.language = "query";
      doc.views = doc.views || {};
      viewExists = !!doc.views[viewName];
      if (viewExists) {
        return false;
      }
      doc.views[viewName] = {
        map: {
          fields: mergeObjects(requestDef.index.fields),
          partial_filter_selector: requestDef.index.partial_filter_selector
        },
        reduce: "_count",
        options: {
          def: originalIndexDef
        }
      };
      return doc;
    }
    db.constructor.emit("debug", ["find", "creating index", ddocId]);
    yield upsert(db, ddocId, updateDdoc);
    if (hasInvalidLanguage) {
      throw new Error('invalid language for ddoc with id "' + ddocId + '" (should be "query")');
    }
    const signature = ddocName + "/" + viewName;
    yield abstractMapper$1(db).query.call(db, signature, {
      limit: 0,
      reduce: false
    });
    return {
      id: ddocId,
      name: viewName,
      result: viewExists ? "exists" : "created"
    };
  });
}
function getIndexes$1(db) {
  return __async(this, null, function* () {
    const allDocsRes = yield db.allDocs({
      startkey: "_design/",
      endkey: "_design/￿",
      include_docs: true
    });
    const res2 = {
      indexes: [{
        ddoc: null,
        name: "_all_docs",
        type: "special",
        def: {
          fields: [{ _id: "asc" }]
        }
      }]
    };
    res2.indexes = flatten(res2.indexes, allDocsRes.rows.filter(function(row) {
      return row.doc.language === "query";
    }).map(function(row) {
      const viewNames = row.doc.views !== void 0 ? Object.keys(row.doc.views) : [];
      return viewNames.map(function(viewName) {
        const view = row.doc.views[viewName];
        return {
          ddoc: row.id,
          name: viewName,
          type: "json",
          def: massageIndexDef(view.options.def)
        };
      });
    }));
    res2.indexes.sort(function(left, right) {
      return compare(left.name, right.name);
    });
    res2.total_rows = res2.indexes.length;
    return res2;
  });
}
var COLLATE_LO = null;
var COLLATE_HI = { "￿": {} };
var SHORT_CIRCUIT_QUERY = {
  queryOpts: { limit: 0, startkey: COLLATE_HI, endkey: COLLATE_LO },
  inMemoryFields: []
};
function checkFieldInIndex(index, field) {
  return index.def.fields.some((key) => getKey(key) === field);
}
function userOperatorLosesPrecision(selector, field) {
  const matcher = selector[field];
  const userOperator = getKey(matcher);
  return userOperator !== "$eq";
}
function sortFieldsByIndex(userFields, index) {
  const indexFields = index.def.fields.map(getKey);
  return userFields.slice().sort(function(a, b) {
    let aIdx = indexFields.indexOf(a);
    let bIdx = indexFields.indexOf(b);
    if (aIdx === -1) {
      aIdx = Number.MAX_VALUE;
    }
    if (bIdx === -1) {
      bIdx = Number.MAX_VALUE;
    }
    return compare(aIdx, bIdx);
  });
}
function getBasicInMemoryFields(index, selector, userFields) {
  userFields = sortFieldsByIndex(userFields, index);
  let needToFilterInMemory = false;
  for (let i = 0, len = userFields.length; i < len; i++) {
    const field = userFields[i];
    if (needToFilterInMemory || !checkFieldInIndex(index, field)) {
      return userFields.slice(i);
    }
    if (i < len - 1 && userOperatorLosesPrecision(selector, field)) {
      needToFilterInMemory = true;
    }
  }
  return [];
}
function getInMemoryFieldsFromNe(selector) {
  const fields = [];
  for (const [field, matcher] of Object.entries(selector)) {
    for (const operator of Object.keys(matcher)) {
      if (operator === "$ne") {
        fields.push(field);
      }
    }
  }
  return fields;
}
function getInMemoryFields(coreInMemoryFields, index, selector, userFields) {
  const result = flatten(
    // in-memory fields reported as necessary by the query planner
    coreInMemoryFields,
    // combine with another pass that checks for any we may have missed
    getBasicInMemoryFields(index, selector, userFields),
    // combine with another pass that checks for $ne's
    getInMemoryFieldsFromNe(selector)
  );
  return sortFieldsByIndex(uniq2(result), index);
}
function checkIndexFieldsMatch(indexFields, sortOrder, fields) {
  if (sortOrder) {
    const sortMatches = oneArrayIsStrictSubArrayOfOther(sortOrder, indexFields);
    const selectorMatches = oneArrayIsSubArrayOfOther(fields, indexFields);
    return sortMatches && selectorMatches;
  }
  return oneSetIsSubArrayOfOther(fields, indexFields);
}
var logicalMatchers = ["$eq", "$gt", "$gte", "$lt", "$lte"];
function isNonLogicalMatcher(matcher) {
  return logicalMatchers.indexOf(matcher) === -1;
}
function checkFieldsLogicallySound(indexFields, selector) {
  const firstField = indexFields[0];
  const matcher = selector[firstField];
  if (typeof matcher === "undefined") {
    return true;
  }
  const isInvalidNe = Object.keys(matcher).length === 1 && getKey(matcher) === "$ne";
  return !isInvalidNe;
}
function checkIndexMatches(index, sortOrder, fields, selector) {
  const indexFields = index.def.fields.map(getKey);
  const fieldsMatch = checkIndexFieldsMatch(indexFields, sortOrder, fields);
  if (!fieldsMatch) {
    return false;
  }
  return checkFieldsLogicallySound(indexFields, selector);
}
function findMatchingIndexes(selector, userFields, sortOrder, indexes) {
  return indexes.filter(function(index) {
    return checkIndexMatches(index, sortOrder, userFields, selector);
  });
}
function findBestMatchingIndex(selector, userFields, sortOrder, indexes, useIndex) {
  const matchingIndexes = findMatchingIndexes(selector, userFields, sortOrder, indexes);
  if (matchingIndexes.length === 0) {
    if (useIndex) {
      throw {
        error: "no_usable_index",
        message: "There is no index available for this selector."
      };
    }
    const defaultIndex = indexes[0];
    defaultIndex.defaultUsed = true;
    return defaultIndex;
  }
  if (matchingIndexes.length === 1 && !useIndex) {
    return matchingIndexes[0];
  }
  const userFieldsMap = arrayToObject(userFields);
  function scoreIndex(index) {
    const indexFields = index.def.fields.map(getKey);
    let score = 0;
    for (const indexField of indexFields) {
      if (userFieldsMap[indexField]) {
        score++;
      }
    }
    return score;
  }
  if (useIndex) {
    const useIndexDdoc = "_design/" + useIndex[0];
    const useIndexName = useIndex.length === 2 ? useIndex[1] : false;
    const index = matchingIndexes.find(function(index2) {
      if (useIndexName && index2.ddoc === useIndexDdoc && useIndexName === index2.name) {
        return true;
      }
      if (index2.ddoc === useIndexDdoc) {
        return true;
      }
      return false;
    });
    if (!index) {
      throw {
        error: "unknown_error",
        message: "Could not find that index or could not use that index for the query"
      };
    }
    return index;
  }
  return max(matchingIndexes, scoreIndex);
}
function getSingleFieldQueryOptsFor(userOperator, userValue) {
  switch (userOperator) {
    case "$eq":
      return { key: userValue };
    case "$lte":
      return { endkey: userValue };
    case "$gte":
      return { startkey: userValue };
    case "$lt":
      return {
        endkey: userValue,
        inclusive_end: false
      };
    case "$gt":
      return {
        startkey: userValue,
        inclusive_start: false
      };
  }
  return {
    startkey: COLLATE_LO
  };
}
function getSingleFieldCoreQueryPlan(selector, index) {
  const field = getKey(index.def.fields[0]);
  const matcher = selector[field] || {};
  const inMemoryFields = [];
  const userOperators = Object.keys(matcher);
  let combinedOpts;
  for (const userOperator of userOperators) {
    if (isNonLogicalMatcher(userOperator)) {
      inMemoryFields.push(field);
    }
    const userValue = matcher[userOperator];
    const newQueryOpts = getSingleFieldQueryOptsFor(userOperator, userValue);
    if (combinedOpts) {
      combinedOpts = mergeObjects([combinedOpts, newQueryOpts]);
    } else {
      combinedOpts = newQueryOpts;
    }
  }
  return {
    queryOpts: combinedOpts,
    inMemoryFields
  };
}
function getMultiFieldCoreQueryPlan(userOperator, userValue) {
  switch (userOperator) {
    case "$eq":
      return {
        startkey: userValue,
        endkey: userValue
      };
    case "$lte":
      return {
        endkey: userValue
      };
    case "$gte":
      return {
        startkey: userValue
      };
    case "$lt":
      return {
        endkey: userValue,
        inclusive_end: false
      };
    case "$gt":
      return {
        startkey: userValue,
        inclusive_start: false
      };
  }
}
function getMultiFieldQueryOpts(selector, index) {
  const indexFields = index.def.fields.map(getKey);
  let inMemoryFields = [];
  const startkey = [];
  const endkey = [];
  let inclusiveStart;
  let inclusiveEnd;
  function finish(i) {
    if (inclusiveStart !== false) {
      startkey.push(COLLATE_LO);
    }
    if (inclusiveEnd !== false) {
      endkey.push(COLLATE_HI);
    }
    inMemoryFields = indexFields.slice(i);
  }
  for (let i = 0, len = indexFields.length; i < len; i++) {
    const indexField = indexFields[i];
    const matcher = selector[indexField];
    if (!matcher || !Object.keys(matcher).length) {
      finish(i);
      break;
    } else if (Object.keys(matcher).some(isNonLogicalMatcher)) {
      finish(i);
      break;
    } else if (i > 0) {
      const usingGtlt = "$gt" in matcher || "$gte" in matcher || "$lt" in matcher || "$lte" in matcher;
      const previousKeys = Object.keys(selector[indexFields[i - 1]]);
      const previousWasEq = arrayEquals(previousKeys, ["$eq"]);
      const previousWasSame = arrayEquals(previousKeys, Object.keys(matcher));
      const gtltLostSpecificity = usingGtlt && !previousWasEq && !previousWasSame;
      if (gtltLostSpecificity) {
        finish(i);
        break;
      }
    }
    const userOperators = Object.keys(matcher);
    let combinedOpts = null;
    for (const userOperator of userOperators) {
      const userValue = matcher[userOperator];
      const newOpts = getMultiFieldCoreQueryPlan(userOperator, userValue);
      if (combinedOpts) {
        combinedOpts = mergeObjects([combinedOpts, newOpts]);
      } else {
        combinedOpts = newOpts;
      }
    }
    startkey.push("startkey" in combinedOpts ? combinedOpts.startkey : COLLATE_LO);
    endkey.push("endkey" in combinedOpts ? combinedOpts.endkey : COLLATE_HI);
    if ("inclusive_start" in combinedOpts) {
      inclusiveStart = combinedOpts.inclusive_start;
    }
    if ("inclusive_end" in combinedOpts) {
      inclusiveEnd = combinedOpts.inclusive_end;
    }
  }
  const res2 = {
    startkey,
    endkey
  };
  if (typeof inclusiveStart !== "undefined") {
    res2.inclusive_start = inclusiveStart;
  }
  if (typeof inclusiveEnd !== "undefined") {
    res2.inclusive_end = inclusiveEnd;
  }
  return {
    queryOpts: res2,
    inMemoryFields
  };
}
function shouldShortCircuit(selector) {
  const values = Object.keys(selector).map(function(key) {
    return selector[key];
  });
  return values.some(function(val) {
    return typeof val === "object" && Object.keys(val).length === 0;
  });
}
function getDefaultQueryPlan(selector) {
  return {
    queryOpts: { startkey: null },
    inMemoryFields: [Object.keys(selector)]
  };
}
function getCoreQueryPlan(selector, index) {
  if (index.defaultUsed) {
    return getDefaultQueryPlan(selector, index);
  }
  if (index.def.fields.length === 1) {
    return getSingleFieldCoreQueryPlan(selector, index);
  }
  return getMultiFieldQueryOpts(selector, index);
}
function planQuery(request, indexes) {
  const selector = request.selector;
  const sort = request.sort;
  if (shouldShortCircuit(selector)) {
    return Object.assign({}, SHORT_CIRCUIT_QUERY, { index: indexes[0] });
  }
  const userFieldsRes = getUserFields(selector, sort);
  const userFields = userFieldsRes.fields;
  const sortOrder = userFieldsRes.sortOrder;
  const index = findBestMatchingIndex(selector, userFields, sortOrder, indexes, request.use_index);
  const coreQueryPlan = getCoreQueryPlan(selector, index);
  const queryOpts = coreQueryPlan.queryOpts;
  const coreInMemoryFields = coreQueryPlan.inMemoryFields;
  const inMemoryFields = getInMemoryFields(coreInMemoryFields, index, selector, userFields);
  return {
    queryOpts,
    index,
    inMemoryFields
  };
}
function indexToSignature(index) {
  return index.ddoc.substring(8) + "/" + index.name;
}
function doAllDocs(db, originalOpts) {
  return __async(this, null, function* () {
    const opts = clone(originalOpts);
    if (opts.descending) {
      if ("endkey" in opts && typeof opts.endkey !== "string") {
        opts.endkey = "";
      }
      if ("startkey" in opts && typeof opts.startkey !== "string") {
        opts.limit = 0;
      }
    } else {
      if ("startkey" in opts && typeof opts.startkey !== "string") {
        opts.startkey = "";
      }
      if ("endkey" in opts && typeof opts.endkey !== "string") {
        opts.limit = 0;
      }
    }
    if ("key" in opts && typeof opts.key !== "string") {
      opts.limit = 0;
    }
    if (opts.limit > 0 && opts.indexes_count) {
      opts.original_limit = opts.limit;
      opts.limit += opts.indexes_count;
    }
    const res2 = yield db.allDocs(opts);
    res2.rows = res2.rows.filter(function(row) {
      return !/^_design\//.test(row.id);
    });
    if (opts.original_limit) {
      opts.limit = opts.original_limit;
    }
    res2.rows = res2.rows.slice(0, opts.limit);
    return res2;
  });
}
function queryAllOrIndex(db, opts, indexToUse) {
  return __async(this, null, function* () {
    if (indexToUse.name === "_all_docs") {
      return doAllDocs(db, opts);
    }
    return abstractMapper$1(db).query.call(db, indexToSignature(indexToUse), opts);
  });
}
function find$1(db, requestDef, explain2) {
  return __async(this, null, function* () {
    if (requestDef.selector) {
      validateSelector(requestDef.selector, false);
      requestDef.selector = massageSelector(requestDef.selector);
    }
    if (requestDef.sort) {
      requestDef.sort = massageSort(requestDef.sort);
    }
    if (requestDef.use_index) {
      requestDef.use_index = massageUseIndex(requestDef.use_index);
    }
    if (!("limit" in requestDef)) {
      requestDef.limit = 25;
    }
    validateFindRequest(requestDef);
    const getIndexesRes = yield getIndexes$1(db);
    db.constructor.emit("debug", ["find", "planning query", requestDef]);
    const queryPlan = planQuery(requestDef, getIndexesRes.indexes);
    db.constructor.emit("debug", ["find", "query plan", queryPlan]);
    const indexToUse = queryPlan.index;
    validateSort(requestDef, indexToUse);
    let opts = Object.assign({
      include_docs: true,
      reduce: false,
      // Add amount of index for doAllDocs to use (related to issue #7810)
      indexes_count: getIndexesRes.total_rows
    }, queryPlan.queryOpts);
    if ("startkey" in opts && "endkey" in opts && collate(opts.startkey, opts.endkey) > 0) {
      return { docs: [] };
    }
    const isDescending = requestDef.sort && typeof requestDef.sort[0] !== "string" && getValue(requestDef.sort[0]) === "desc";
    if (isDescending) {
      opts.descending = true;
      opts = reverseOptions(opts);
    }
    if (!queryPlan.inMemoryFields.length) {
      opts.limit = requestDef.limit;
      if ("skip" in requestDef) {
        opts.skip = requestDef.skip;
      }
    }
    if (explain2) {
      return Promise.resolve(queryPlan, opts);
    }
    const res2 = yield queryAllOrIndex(db, opts, indexToUse);
    if (opts.inclusive_start === false) {
      res2.rows = filterInclusiveStart(res2.rows, opts.startkey, indexToUse);
    }
    if (queryPlan.inMemoryFields.length) {
      res2.rows = filterInMemoryFields(res2.rows, requestDef, queryPlan.inMemoryFields);
    }
    const resp = {
      docs: res2.rows.map(function(row) {
        const doc = row.doc;
        if (requestDef.fields) {
          return pick(doc, requestDef.fields);
        }
        return doc;
      })
    };
    if (indexToUse.defaultUsed) {
      resp.warning = "No matching index found, create an index to optimize query time.";
    }
    return resp;
  });
}
function explain$1(db, requestDef) {
  return __async(this, null, function* () {
    const queryPlan = yield find$1(db, requestDef, true);
    return {
      dbname: db.name,
      index: queryPlan.index,
      selector: requestDef.selector,
      range: {
        start_key: queryPlan.queryOpts.startkey,
        end_key: queryPlan.queryOpts.endkey
      },
      opts: {
        use_index: requestDef.use_index || [],
        bookmark: "nil",
        //hardcoded to match CouchDB since its not supported,
        limit: requestDef.limit,
        skip: requestDef.skip,
        sort: requestDef.sort || {},
        fields: requestDef.fields,
        conflicts: false,
        //hardcoded to match CouchDB since its not supported,
        r: [49]
        // hardcoded to match CouchDB since its not support
      },
      limit: requestDef.limit,
      skip: requestDef.skip || 0,
      fields: requestDef.fields
    };
  });
}
function deleteIndex$1(db, index) {
  return __async(this, null, function* () {
    if (!index.ddoc) {
      throw new Error("you must supply an index.ddoc when deleting");
    }
    if (!index.name) {
      throw new Error("you must supply an index.name when deleting");
    }
    const docId = index.ddoc;
    const viewName = index.name;
    function deltaFun(doc) {
      if (Object.keys(doc.views).length === 1 && doc.views[viewName]) {
        return { _id: docId, _deleted: true };
      }
      delete doc.views[viewName];
      return doc;
    }
    yield upsert(db, docId, deltaFun);
    yield abstractMapper$1(db).viewCleanup.apply(db);
    return { ok: true };
  });
}
var plugin = {};
plugin.createIndex = resolveToCallback(function(requestDef) {
  return __async(this, null, function* () {
    if (typeof requestDef !== "object") {
      throw new Error("you must provide an index to create");
    }
    const createIndex$$1 = isRemote(this) ? createIndex : createIndex$1;
    return createIndex$$1(this, requestDef);
  });
});
plugin.find = resolveToCallback(function(requestDef) {
  return __async(this, null, function* () {
    if (typeof requestDef !== "object") {
      throw new Error("you must provide search parameters to find()");
    }
    const find$$1 = isRemote(this) ? find : find$1;
    return find$$1(this, requestDef);
  });
});
plugin.explain = resolveToCallback(function(requestDef) {
  return __async(this, null, function* () {
    if (typeof requestDef !== "object") {
      throw new Error("you must provide search parameters to explain()");
    }
    const find$$1 = isRemote(this) ? explain : explain$1;
    return find$$1(this, requestDef);
  });
});
plugin.getIndexes = resolveToCallback(function() {
  return __async(this, null, function* () {
    const getIndexes$$1 = isRemote(this) ? getIndexes : getIndexes$1;
    return getIndexes$$1(this);
  });
});
plugin.deleteIndex = resolveToCallback(function(indexDef) {
  return __async(this, null, function* () {
    if (typeof indexDef !== "object") {
      throw new Error("you must provide an index to delete");
    }
    const deleteIndex$$1 = isRemote(this) ? deleteIndex : deleteIndex$1;
    return deleteIndex$$1(this, indexDef);
  });
});
var index_browser_es_default = plugin;
export {
  index_browser_es_default as default
};
//# sourceMappingURL=pouchdb-find.js.map
