import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { PouchdbService } from 'src/app/services/pouchdb.service';

import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonItem,
  IonInput,
  IonButton,
  IonList,
  IonLabel,
  IonSelect,
  IonSelectOption,
  IonListHeader,
  IonButtons,
  IonCard,
  IonCardContent
} from '@ionic/angular/standalone';

@Component({
  selector: 'app-form',
  standalone: true,
  templateUrl: './form.page.html',
  styleUrls: ['./form.page.scss'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonItem,
    IonInput,
    IonButton,
    IonList,
    IonLabel,
    IonSelect,
    IonSelectOption,
    IonListHeader,
    IonButtons,
    IonCard,
    IonCardContent
  ]
})
export class FormPage implements OnInit {
  patientForm!: FormGroup;
  patients: any[] = [];

  constructor(private fb: FormBuilder, private pouchService: PouchdbService) {}

  ngOnInit() {
    this.initForm();
    this.loadPatients();
  }

  private initForm() {
    this.patientForm = this.fb.group({
      _id: [''],
      _rev: [null],
      domainwisepid: [0],
      patientid: [0],
      first_name: [''],
      last_name: [''],
      dob: [''],
      gender: ['']
    });
  }

  savePatient() {
    const formValue = this.patientForm.value;

    if (!formValue._id) {
      // ✅ New Document
      formValue._id = 'patient_' + new Date().getTime();
      this.pouchService.createDoc(formValue).subscribe(() => this.loadPatients());
    } else {
      // ✅ Fetch latest doc to get correct _rev before update
      this.pouchService.getDocById<any>(formValue._id).subscribe((doc: any) => {
        const updatedDoc: any = { ...(doc as any), ...(formValue as any), _rev: doc._rev };
        this.pouchService.updateDoc(updatedDoc).subscribe(() => this.loadPatients());
      });
    }

    this.patientForm.reset();
  }

  loadPatients() {
    this.pouchService.getAllDocs<any>().subscribe((data) => (this.patients = data));
  }

  editPatient(patient: any) {
    this.patientForm.patchValue(patient);
  }

  deletePatient(patient: any) {
    this.pouchService.deleteDoc(patient._id).subscribe(() => this.loadPatients());
  }
}
