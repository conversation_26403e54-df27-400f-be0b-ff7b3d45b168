{"name": "@ionic/core", "version": "8.6.5", "description": "Base components for Ionic", "keywords": ["ionic", "framework", "stencil", "mobile", "app", "webapp", "capacitor", "<PERSON><PERSON>", "electron", "progressive web app", "pwa"], "main": "dist/index.cjs.js", "module": "dist/index.js", "es2015": "dist/esm/index.js", "es2017": "dist/esm/index.js", "jsnext:main": "dist/esm/index.js", "collection:main": "dist/collection/index.js", "collection": "dist/collection/collection-manifest.json", "types": "dist/types/interface.d.ts", "files": ["components/", "css/", "dist/", "hydrate/", "loader/"], "dependencies": {"@stencil/core": "4.33.1", "ionicons": "^7.2.2", "tslib": "^2.1.0"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@capacitor/core": "^7.0.0", "@capacitor/haptics": "^7.0.0", "@capacitor/keyboard": "^7.0.0", "@capacitor/status-bar": "^7.0.0", "@clack/prompts": "^0.11.0", "@ionic/eslint-config": "^0.3.0", "@ionic/prettier-config": "^2.0.0", "@playwright/test": "^1.54.1", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-virtual": "^2.0.3", "@stencil/angular-output-target": "^0.10.0", "@stencil/react-output-target": "0.5.3", "@stencil/sass": "^3.0.9", "@stencil/vue-output-target": "0.10.8", "@types/jest": "^29.5.6", "@types/node": "^14.6.0", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "chalk": "^5.3.0", "clean-css-cli": "^5.6.1", "domino": "^2.1.6", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-custom-rules": "file:custom-rules", "execa": "^8.0.1", "fs-extra": "^9.0.1", "jest": "^29.7.0", "jest-cli": "^29.7.0", "prettier": "^2.6.1", "rollup": "^2.26.4", "sass": "^1.33.0", "serve": "^14.0.1", "stylelint": "^13.13.1", "stylelint-order": "^4.1.0"}, "scripts": {"build": "npm run clean && npm run build.css && stencil build --es5 --docs-json dist/docs.json", "build.css": "npm run css.sass && npm run css.minify", "build.debug": "npm run clean && stencil build --debug", "build.docs.json": "stencil build --docs-json dist/docs.json", "clean": "node scripts/clean.js", "css.minify": "cleancss -O2 -o ./css/ionic.bundle.css ./css/ionic.bundle.css", "css.sass": "sass --embed-sources --style compressed src/css:./css", "eslint": "eslint src", "lint": "npm run lint.ts && npm run lint.sass && npm run prettier -- --write --cache", "lint.fix": "npm run lint.ts.fix && npm run lint.sass.fix && npm run prettier -- --write --cache", "lint.sass": "stylelint \"src/**/*.scss\"", "lint.sass.fix": "npm run lint.sass -- --fix", "lint.ts": "npm run eslint", "lint.ts.fix": "npm run eslint -- --fix", "prerender.e2e": "node scripts/testing/prerender.js", "prettier": "prettier \"./src/**/*.{html,ts,tsx,js,jsx}\"", "start": "npm run build.css && stencil build --dev --watch --serve", "test": "npm run test.spec && npm run test.e2e", "test.spec": "stencil test --spec --max-workers=2", "test.e2e": "npx playwright test", "test.e2e.update-snapshots": "npm run test.e2e -- --update-snapshots='changed'", "test.watch": "jest --watch --no-cache", "test.treeshake": "node scripts/treeshaking.js dist/index.js", "validate": "npm run lint && npm run test && npm run build && npm run test.treeshake", "docker.build": "docker build -t ionic-playwright .", "test.e2e.docker": "npm run docker.build && node ./scripts/docker.mjs", "test.e2e.docker.update-snapshots": "npm run test.e2e.docker -- --update-snapshots='changed'", "test.e2e.docker.ci": "npm run docker.build && CI=true node ./scripts/docker.mjs", "test.e2e.script": "node scripts/testing/e2e-script.mjs"}, "author": "Ionic Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/ionic-framework.git"}, "bugs": {"url": "https://github.com/ionic-team/ionic-framework/issues"}, "homepage": "https://ionicframework.com/", "jest": {"preset": "@stencil/core/testing"}}