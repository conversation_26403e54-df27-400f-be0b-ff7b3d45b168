<ion-header>
  <ion-toolbar>
    <ion-title>Patient Management</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

  <!-- ✅ Patient Form -->
  <form [formGroup]="patientForm" (ngSubmit)="savePatient()">
    <ion-item>
      <ion-label position="floating">First Name</ion-label>
      <ion-input formControlName="first_name"></ion-input>
    </ion-item>

    <ion-item>
      <ion-label position="floating">Last Name</ion-label>
      <ion-input formControlName="last_name"></ion-input>
    </ion-item>

    <ion-item>
      <ion-label position="floating">Gender</ion-label>
      <ion-select formControlName="gender">
        <ion-select-option value="Male">Male</ion-select-option>
        <ion-select-option value="Female">Female</ion-select-option>
      </ion-select>
    </ion-item>

    <ion-item>
      <ion-label position="floating">DOB</ion-label>
      <ion-input type="date" formControlName="dob"></ion-input>
    </ion-item>

    <ion-button expand="block" type="submit">
      {{ patientForm.value._id ? 'Update Patient' : 'Save Patient' }}
    </ion-button>
  </form>

  <ion-list *ngIf="patients.length > 0">
    <ion-list-header>
      <ion-label>Saved Patients</ion-label>
    </ion-list-header>

    <ion-item *ngFor="let p of patients">
      <ion-label>
        <h2>{{ p.first_name }} {{ p.last_name }}</h2>
        <p>{{ p.gender }} | DOB: {{ p.dob }}</p>
      </ion-label>

      <ion-buttons slot="end">
        <ion-button size="small" color="warning" (click)="editPatient(p)">
          Edit
        </ion-button>
        <ion-button size="small" color="danger" (click)="deletePatient(p)">
          Delete
        </ion-button>
      </ion-buttons>
    </ion-item>
  </ion-list>

  <ion-card *ngIf="patients.length === 0" class="ion-text-center ion-padding">
    <ion-card-content>No patients saved yet.</ion-card-content>
  </ion-card>

</ion-content>
