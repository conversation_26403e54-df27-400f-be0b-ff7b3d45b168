{"version": 3, "sources": ["../../../../../../node_modules/pouchdb-errors/lib/index.es.js", "../../../../../../node_modules/pouchdb-fetch/lib/index-browser.es.js", "../../../../../../node_modules/pouchdb-binary-utils/lib/index-browser.es.js", "../../../../../../node_modules/pouchdb-collate/lib/index.es.js", "../../../../../../node_modules/pouchdb-utils/lib/index-browser.es.js", "../../../../../../node_modules/pouchdb-md5/lib/index-browser.es.js", "../../../../../../node_modules/pouchdb-mapreduce-utils/lib/index.es.js", "../../../../../../node_modules/pouchdb-abstract-mapreduce/lib/index.es.js", "../../../../../../node_modules/pouchdb-selector-core/lib/index.es.js", "../../../../../../node_modules/pouchdb-find/lib/index-browser.es.js"], "sourcesContent": ["class PouchError extends Error {\n  constructor(status, error, reason) {\n    super();\n    this.status = status;\n    this.name = error;\n    this.message = reason;\n    this.error = true;\n  }\n\n  toString() {\n    return JSON.stringify({\n      status: this.status,\n      name: this.name,\n      message: this.message,\n      reason: this.reason\n    });\n  }\n}\n\nvar UNAUTHORIZED = new PouchError(401, 'unauthorized', \"Name or password is incorrect.\");\nvar MISSING_BULK_DOCS = new PouchError(400, 'bad_request', \"Missing JSON list of 'docs'\");\nvar MISSING_DOC = new PouchError(404, 'not_found', 'missing');\nvar REV_CONFLICT = new PouchError(409, 'conflict', 'Document update conflict');\nvar INVALID_ID = new PouchError(400, 'bad_request', '_id field must contain a string');\nvar MISSING_ID = new PouchError(412, 'missing_id', '_id is required for puts');\nvar RESERVED_ID = new PouchError(400, 'bad_request', 'Only reserved document ids may start with underscore.');\nvar NOT_OPEN = new PouchError(412, 'precondition_failed', 'Database not open');\nvar UNKNOWN_ERROR = new PouchError(500, 'unknown_error', 'Database encountered an unknown error');\nvar BAD_ARG = new PouchError(500, 'badarg', 'Some query argument is invalid');\nvar INVALID_REQUEST = new PouchError(400, 'invalid_request', 'Request was invalid');\nvar QUERY_PARSE_ERROR = new PouchError(400, 'query_parse_error', 'Some query parameter is invalid');\nvar DOC_VALIDATION = new PouchError(500, 'doc_validation', 'Bad special document member');\nvar BAD_REQUEST = new PouchError(400, 'bad_request', 'Something wrong with the request');\nvar NOT_AN_OBJECT = new PouchError(400, 'bad_request', 'Document must be a JSON object');\nvar DB_MISSING = new PouchError(404, 'not_found', 'Database not found');\nvar IDB_ERROR = new PouchError(500, 'indexed_db_went_bad', 'unknown');\nvar WSQ_ERROR = new PouchError(500, 'web_sql_went_bad', 'unknown');\nvar LDB_ERROR = new PouchError(500, 'levelDB_went_went_bad', 'unknown');\nvar FORBIDDEN = new PouchError(403, 'forbidden', 'Forbidden by design doc validate_doc_update function');\nvar INVALID_REV = new PouchError(400, 'bad_request', 'Invalid rev format');\nvar FILE_EXISTS = new PouchError(412, 'file_exists', 'The database could not be created, the file already exists.');\nvar MISSING_STUB = new PouchError(412, 'missing_stub', 'A pre-existing attachment stub wasn\\'t found');\nvar INVALID_URL = new PouchError(413, 'invalid_url', 'Provided URL is invalid');\n\nfunction createError(error, reason) {\n  function CustomPouchError(reason) {\n    // inherit error properties from our parent error manually\n    // so as to allow proper JSON parsing.\n    var names = Object.getOwnPropertyNames(error);\n    for (var i = 0, len = names.length; i < len; i++) {\n      if (typeof error[names[i]] !== 'function') {\n        this[names[i]] = error[names[i]];\n      }\n    }\n\n    if (this.stack === undefined) {\n      this.stack = (new Error()).stack;\n    }\n\n    if (reason !== undefined) {\n      this.reason = reason;\n    }\n  }\n  CustomPouchError.prototype = PouchError.prototype;\n  return new CustomPouchError(reason);\n}\n\nfunction generateErrorFromResponse(err) {\n\n  if (typeof err !== 'object') {\n    var data = err;\n    err = UNKNOWN_ERROR;\n    err.data = data;\n  }\n\n  if ('error' in err && err.error === 'conflict') {\n    err.name = 'conflict';\n    err.status = 409;\n  }\n\n  if (!('name' in err)) {\n    err.name = err.error || 'unknown';\n  }\n\n  if (!('status' in err)) {\n    err.status = 500;\n  }\n\n  if (!('message' in err)) {\n    err.message = err.message || err.reason;\n  }\n\n  if (!('stack' in err)) {\n    err.stack = (new Error()).stack;\n  }\n\n  return err;\n}\n\nexport { UNAUTHORIZED, MISSING_BULK_DOCS, MISSING_DOC, REV_CONFLICT, INVALID_ID, MISSING_ID, RESERVED_ID, NOT_OPEN, UNKNOWN_ERROR, BAD_ARG, INVALID_REQUEST, QUERY_PARSE_ERROR, DOC_VALIDATION, BAD_REQUEST, NOT_AN_OBJECT, DB_MISSING, WSQ_ERROR, LDB_ERROR, FORBIDDEN, INVALID_REV, FILE_EXISTS, MISSING_STUB, IDB_ERROR, INVALID_URL, createError, generateErrorFromResponse };\n", "var f = fetch;\nvar h = Headers;\n\nexport { f as fetch, h as Headers };\n", "var thisAtob = function (str) {\n  return atob(str);\n};\n\nvar thisBtoa = function (str) {\n  return btoa(str);\n};\n\n// Abstracts constructing a Blob object, so it also works in older\n// browsers that don't support the native Blob constructor (e.g.\n// old QtWebKit versions, Android < 4.4).\nfunction createBlob(parts, properties) {\n  /* global BlobBuilder,MSBlobBuilder,MozBlobBuilder,WebKitBlobBuilder */\n  parts = parts || [];\n  properties = properties || {};\n  try {\n    return new Blob(parts, properties);\n  } catch (e) {\n    if (e.name !== \"TypeError\") {\n      throw e;\n    }\n    var Builder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\n                  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\n                  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder :\n                  WebKitBlobBuilder;\n    var builder = new Builder();\n    for (var i = 0; i < parts.length; i += 1) {\n      builder.append(parts[i]);\n    }\n    return builder.getBlob(properties.type);\n  }\n}\n\n// From http://stackoverflow.com/questions/14967647/ (continues on next line)\n// encode-decode-image-with-base64-breaks-image (2013-04-21)\nfunction binaryStringToArrayBuffer(bin) {\n  var length = bin.length;\n  var buf = new ArrayBuffer(length);\n  var arr = new Uint8Array(buf);\n  for (var i = 0; i < length; i++) {\n    arr[i] = bin.charCodeAt(i);\n  }\n  return buf;\n}\n\nfunction binStringToBluffer(binString, type) {\n  return createBlob([binaryStringToArrayBuffer(binString)], {type});\n}\n\nfunction b64ToBluffer(b64, type) {\n  return binStringToBluffer(thisAtob(b64), type);\n}\n\n//Can't find original post, but this is close\n//http://stackoverflow.com/questions/6965107/ (continues on next line)\n//converting-between-strings-and-arraybuffers\nfunction arrayBufferToBinaryString(buffer) {\n  var binary = '';\n  var bytes = new Uint8Array(buffer);\n  var length = bytes.byteLength;\n  for (var i = 0; i < length; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return binary;\n}\n\n// shim for browsers that don't support it\nfunction readAsBinaryString(blob, callback) {\n  var reader = new FileReader();\n  var hasBinaryString = typeof reader.readAsBinaryString === 'function';\n  reader.onloadend = function (e) {\n    var result = e.target.result || '';\n    if (hasBinaryString) {\n      return callback(result);\n    }\n    callback(arrayBufferToBinaryString(result));\n  };\n  if (hasBinaryString) {\n    reader.readAsBinaryString(blob);\n  } else {\n    reader.readAsArrayBuffer(blob);\n  }\n}\n\nfunction blobToBinaryString(blobOrBuffer, callback) {\n  readAsBinaryString(blobOrBuffer, function (bin) {\n    callback(bin);\n  });\n}\n\nfunction blobToBase64(blobOrBuffer, callback) {\n  blobToBinaryString(blobOrBuffer, function (base64) {\n    callback(thisBtoa(base64));\n  });\n}\n\n// simplified API. universal browser support is assumed\nfunction readAsArrayBuffer(blob, callback) {\n  var reader = new FileReader();\n  reader.onloadend = function (e) {\n    var result = e.target.result || new ArrayBuffer(0);\n    callback(result);\n  };\n  reader.readAsArrayBuffer(blob);\n}\n\n// this is not used in the browser\nfunction typedBuffer() {\n}\n\nexport { thisAtob as atob, thisBtoa as btoa, b64ToBluffer as base64StringToBlobOrBuffer, binaryStringToArrayBuffer, binStringToBluffer as binaryStringToBlobOrBuffer, createBlob as blob, blobToBase64 as blobOrBufferToBase64, blobToBinaryString as blobOrBufferToBinaryString, readAsArrayBuffer, readAsBinaryString, typedBuffer };\n", "function pad(str, padWith, upToLength) {\n  var padding = '';\n  var targetLength = upToLength - str.length;\n  /* istanbul ignore next */\n  while (padding.length < targetLength) {\n    padding += padWith;\n  }\n  return padding;\n}\n\nfunction padLeft(str, padWith, upToLength) {\n  var padding = pad(str, padWith, upToLength);\n  return padding + str;\n}\n\nvar MIN_MAGNITUDE = -324; // verified by -Number.MIN_VALUE\nvar MAGNITUDE_DIGITS = 3; // ditto\nvar SEP = ''; // set to '_' for easier debugging\n\nfunction collate(a, b) {\n\n  if (a === b) {\n    return 0;\n  }\n\n  a = normalizeKey(a);\n  b = normalizeKey(b);\n\n  var ai = collationIndex(a);\n  var bi = collationIndex(b);\n  if ((ai - bi) !== 0) {\n    return ai - bi;\n  }\n  switch (typeof a) {\n    case 'number':\n      return a - b;\n    case 'boolean':\n      return a < b ? -1 : 1;\n    case 'string':\n      return stringCollate(a, b);\n  }\n  return Array.isArray(a) ? arrayCollate(a, b) : objectCollate(a, b);\n}\n\n// couch considers null/NaN/Infinity/-Infinity === undefined,\n// for the purposes of mapreduce indexes. also, dates get stringified.\nfunction normalizeKey(key) {\n  switch (typeof key) {\n    case 'undefined':\n      return null;\n    case 'number':\n      if (key === Infinity || key === -Infinity || isNaN(key)) {\n        return null;\n      }\n      return key;\n    case 'object':\n      var origKey = key;\n      if (Array.isArray(key)) {\n        var len = key.length;\n        key = new Array(len);\n        for (var i = 0; i < len; i++) {\n          key[i] = normalizeKey(origKey[i]);\n        }\n      /* istanbul ignore next */\n      } else if (key instanceof Date) {\n        return key.toJSON();\n      } else if (key !== null) { // generic object\n        key = {};\n        for (var k in origKey) {\n          if (Object.prototype.hasOwnProperty.call(origKey, k)) {\n            var val = origKey[k];\n            if (typeof val !== 'undefined') {\n              key[k] = normalizeKey(val);\n            }\n          }\n        }\n      }\n  }\n  return key;\n}\n\nfunction indexify(key) {\n  if (key !== null) {\n    switch (typeof key) {\n      case 'boolean':\n        return key ? 1 : 0;\n      case 'number':\n        return numToIndexableString(key);\n      case 'string':\n        // We've to be sure that key does not contain \\u0000\n        // Do order-preserving replacements:\n        // 0 -> 1, 1\n        // 1 -> 1, 2\n        // 2 -> 2, 2\n        /* eslint-disable no-control-regex */\n        return key\n          .replace(/\\u0002/g, '\\u0002\\u0002')\n          .replace(/\\u0001/g, '\\u0001\\u0002')\n          .replace(/\\u0000/g, '\\u0001\\u0001');\n        /* eslint-enable no-control-regex */\n      case 'object':\n        var isArray = Array.isArray(key);\n        var arr = isArray ? key : Object.keys(key);\n        var i = -1;\n        var len = arr.length;\n        var result = '';\n        if (isArray) {\n          while (++i < len) {\n            result += toIndexableString(arr[i]);\n          }\n        } else {\n          while (++i < len) {\n            var objKey = arr[i];\n            result += toIndexableString(objKey) +\n                toIndexableString(key[objKey]);\n          }\n        }\n        return result;\n    }\n  }\n  return '';\n}\n\n// convert the given key to a string that would be appropriate\n// for lexical sorting, e.g. within a database, where the\n// sorting is the same given by the collate() function.\nfunction toIndexableString(key) {\n  var zero = '\\u0000';\n  key = normalizeKey(key);\n  return collationIndex(key) + SEP + indexify(key) + zero;\n}\n\nfunction parseNumber(str, i) {\n  var originalIdx = i;\n  var num;\n  var zero = str[i] === '1';\n  if (zero) {\n    num = 0;\n    i++;\n  } else {\n    var neg = str[i] === '0';\n    i++;\n    var numAsString = '';\n    var magAsString = str.substring(i, i + MAGNITUDE_DIGITS);\n    var magnitude = parseInt(magAsString, 10) + MIN_MAGNITUDE;\n    /* istanbul ignore next */\n    if (neg) {\n      magnitude = -magnitude;\n    }\n    i += MAGNITUDE_DIGITS;\n    while (true) {\n      var ch = str[i];\n      if (ch === '\\u0000') {\n        break;\n      } else {\n        numAsString += ch;\n      }\n      i++;\n    }\n    numAsString = numAsString.split('.');\n    if (numAsString.length === 1) {\n      num = parseInt(numAsString, 10);\n    } else {\n      /* istanbul ignore next */\n      num = parseFloat(numAsString[0] + '.' + numAsString[1]);\n    }\n    /* istanbul ignore next */\n    if (neg) {\n      num = num - 10;\n    }\n    /* istanbul ignore next */\n    if (magnitude !== 0) {\n      // parseFloat is more reliable than pow due to rounding errors\n      // e.g. Number.MAX_VALUE would return Infinity if we did\n      // num * Math.pow(10, magnitude);\n      num = parseFloat(num + 'e' + magnitude);\n    }\n  }\n  return {num, length : i - originalIdx};\n}\n\n// move up the stack while parsing\n// this function moved outside of parseIndexableString for performance\nfunction pop(stack, metaStack) {\n  var obj = stack.pop();\n\n  if (metaStack.length) {\n    var lastMetaElement = metaStack[metaStack.length - 1];\n    if (obj === lastMetaElement.element) {\n      // popping a meta-element, e.g. an object whose value is another object\n      metaStack.pop();\n      lastMetaElement = metaStack[metaStack.length - 1];\n    }\n    var element = lastMetaElement.element;\n    var lastElementIndex = lastMetaElement.index;\n    if (Array.isArray(element)) {\n      element.push(obj);\n    } else if (lastElementIndex === stack.length - 2) { // obj with key+value\n      var key = stack.pop();\n      element[key] = obj;\n    } else {\n      stack.push(obj); // obj with key only\n    }\n  }\n}\n\nfunction parseIndexableString(str) {\n  var stack = [];\n  var metaStack = []; // stack for arrays and objects\n  var i = 0;\n\n  /*eslint no-constant-condition: [\"error\", { \"checkLoops\": false }]*/\n  while (true) {\n    var collationIndex = str[i++];\n    if (collationIndex === '\\u0000') {\n      if (stack.length === 1) {\n        return stack.pop();\n      } else {\n        pop(stack, metaStack);\n        continue;\n      }\n    }\n    switch (collationIndex) {\n      case '1':\n        stack.push(null);\n        break;\n      case '2':\n        stack.push(str[i] === '1');\n        i++;\n        break;\n      case '3':\n        var parsedNum = parseNumber(str, i);\n        stack.push(parsedNum.num);\n        i += parsedNum.length;\n        break;\n      case '4':\n        var parsedStr = '';\n        /*eslint no-constant-condition: [\"error\", { \"checkLoops\": false }]*/\n        while (true) {\n          var ch = str[i];\n          if (ch === '\\u0000') {\n            break;\n          }\n          parsedStr += ch;\n          i++;\n        }\n        // perform the reverse of the order-preserving replacement\n        // algorithm (see above)\n        /* eslint-disable no-control-regex */\n        parsedStr = parsedStr.replace(/\\u0001\\u0001/g, '\\u0000')\n          .replace(/\\u0001\\u0002/g, '\\u0001')\n          .replace(/\\u0002\\u0002/g, '\\u0002');\n        /* eslint-enable no-control-regex */\n        stack.push(parsedStr);\n        break;\n      case '5':\n        var arrayElement = { element: [], index: stack.length };\n        stack.push(arrayElement.element);\n        metaStack.push(arrayElement);\n        break;\n      case '6':\n        var objElement = { element: {}, index: stack.length };\n        stack.push(objElement.element);\n        metaStack.push(objElement);\n        break;\n      /* istanbul ignore next */\n      default:\n        throw new Error(\n          'bad collationIndex or unexpectedly reached end of input: ' +\n            collationIndex);\n    }\n  }\n}\n\nfunction arrayCollate(a, b) {\n  var len = Math.min(a.length, b.length);\n  for (var i = 0; i < len; i++) {\n    var sort = collate(a[i], b[i]);\n    if (sort !== 0) {\n      return sort;\n    }\n  }\n  return (a.length === b.length) ? 0 :\n    (a.length > b.length) ? 1 : -1;\n}\nfunction stringCollate(a, b) {\n  // See: https://github.com/daleharvey/pouchdb/issues/40\n  // This is incompatible with the CouchDB implementation, but its the\n  // best we can do for now\n  return (a === b) ? 0 : ((a > b) ? 1 : -1);\n}\nfunction objectCollate(a, b) {\n  var ak = Object.keys(a), bk = Object.keys(b);\n  var len = Math.min(ak.length, bk.length);\n  for (var i = 0; i < len; i++) {\n    // First sort the keys\n    var sort = collate(ak[i], bk[i]);\n    if (sort !== 0) {\n      return sort;\n    }\n    // if the keys are equal sort the values\n    sort = collate(a[ak[i]], b[bk[i]]);\n    if (sort !== 0) {\n      return sort;\n    }\n\n  }\n  return (ak.length === bk.length) ? 0 :\n    (ak.length > bk.length) ? 1 : -1;\n}\n// The collation is defined by erlangs ordered terms\n// the atoms null, true, false come first, then numbers, strings,\n// arrays, then objects\n// null/undefined/NaN/Infinity/-Infinity are all considered null\nfunction collationIndex(x) {\n  var id = ['boolean', 'number', 'string', 'object'];\n  var idx = id.indexOf(typeof x);\n  //false if -1 otherwise true, but fast!!!!1\n  if (~idx) {\n    if (x === null) {\n      return 1;\n    }\n    if (Array.isArray(x)) {\n      return 5;\n    }\n    return idx < 3 ? (idx + 2) : (idx + 3);\n  }\n  /* istanbul ignore next */\n  if (Array.isArray(x)) {\n    return 5;\n  }\n}\n\n// conversion:\n// x yyy zz...zz\n// x = 0 for negative, 1 for 0, 2 for positive\n// y = exponent (for negative numbers negated) moved so that it's >= 0\n// z = mantisse\nfunction numToIndexableString(num) {\n\n  if (num === 0) {\n    return '1';\n  }\n\n  // convert number to exponential format for easier and\n  // more succinct string sorting\n  var expFormat = num.toExponential().split(/e\\+?/);\n  var magnitude = parseInt(expFormat[1], 10);\n\n  var neg = num < 0;\n\n  var result = neg ? '0' : '2';\n\n  // first sort by magnitude\n  // it's easier if all magnitudes are positive\n  var magForComparison = ((neg ? -magnitude : magnitude) - MIN_MAGNITUDE);\n  var magString = padLeft((magForComparison).toString(), '0', MAGNITUDE_DIGITS);\n\n  result += SEP + magString;\n\n  // then sort by the factor\n  var factor = Math.abs(parseFloat(expFormat[0])); // [1..10)\n  /* istanbul ignore next */\n  if (neg) { // for negative reverse ordering\n    factor = 10 - factor;\n  }\n\n  var factorStr = factor.toFixed(20);\n\n  // strip zeros from the end\n  factorStr = factorStr.replace(/\\.?0+$/, '');\n\n  result += SEP + factorStr;\n\n  return result;\n}\n\nexport { collate, normalizeKey, toIndexableString, parseIndexableString };\n", "import { createError, BAD_REQUEST, INVALID_ID, MISSING_ID, RESERVED_ID } from 'pouchdb-errors';\nimport EventEmitter from 'events';\nimport { v4 } from 'uuid';\nimport { stringMd5 } from 'pouchdb-md5';\n\nfunction isBinaryObject(object) {\n  return (typeof ArrayBuffer !== 'undefined' && object instanceof ArrayBuffer) ||\n    (typeof Blob !== 'undefined' && object instanceof Blob);\n}\n\n/**\n * @template {ArrayBuffer | Blob} T\n * @param {T} object\n * @returns {T}\n */\nfunction cloneBinaryObject(object) {\n  return object instanceof ArrayBuffer\n    ? object.slice(0)\n    : object.slice(0, object.size, object.type);\n}\n\n// most of this is borrowed from lodash.isPlainObject:\n// https://github.com/fis-components/lodash.isplainobject/\n// blob/29c358140a74f252aeb08c9eb28bef86f2217d4a/index.js\n\nvar funcToString = Function.prototype.toString;\nvar objectCtorString = funcToString.call(Object);\n\nfunction isPlainObject(value) {\n  var proto = Object.getPrototypeOf(value);\n  /* istanbul ignore if */\n  if (proto === null) { // not sure when this happens, but I guess it can\n    return true;\n  }\n  var Ctor = proto.constructor;\n  return (typeof Ctor == 'function' &&\n    Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString);\n}\n\nfunction clone(object) {\n  var newObject;\n  var i;\n  var len;\n\n  if (!object || typeof object !== 'object') {\n    return object;\n  }\n\n  if (Array.isArray(object)) {\n    newObject = [];\n    for (i = 0, len = object.length; i < len; i++) {\n      newObject[i] = clone(object[i]);\n    }\n    return newObject;\n  }\n\n  // special case: to avoid inconsistencies between IndexedDB\n  // and other backends, we automatically stringify Dates\n  if (object instanceof Date && isFinite(object)) {\n    return object.toISOString();\n  }\n\n  if (isBinaryObject(object)) {\n    return cloneBinaryObject(object);\n  }\n\n  if (!isPlainObject(object)) {\n    return object; // don't clone objects like Workers\n  }\n\n  newObject = {};\n  for (i in object) {\n    /* istanbul ignore else */\n    if (Object.prototype.hasOwnProperty.call(object, i)) {\n      var value = clone(object[i]);\n      if (typeof value !== 'undefined') {\n        newObject[i] = value;\n      }\n    }\n  }\n  return newObject;\n}\n\nfunction once(fun) {\n  var called = false;\n  return function (...args) {\n    /* istanbul ignore if */\n    if (called) {\n      // this is a smoke test and should never actually happen\n      throw new Error('once called more than once');\n    } else {\n      called = true;\n      fun.apply(this, args);\n    }\n  };\n}\n\nfunction toPromise(func) {\n  //create the function we will be returning\n  return function (...args) {\n    // Clone arguments\n    args = clone(args);\n    var self = this;\n    // if the last argument is a function, assume its a callback\n    var usedCB = (typeof args[args.length - 1] === 'function') ? args.pop() : false;\n    var promise = new Promise(function (fulfill, reject) {\n      var resp;\n      try {\n        var callback = once(function (err, mesg) {\n          if (err) {\n            reject(err);\n          } else {\n            fulfill(mesg);\n          }\n        });\n        // create a callback for this invocation\n        // apply the function in the orig context\n        args.push(callback);\n        resp = func.apply(self, args);\n        if (resp && typeof resp.then === 'function') {\n          fulfill(resp);\n        }\n      } catch (e) {\n        reject(e);\n      }\n    });\n    // if there is a callback, call it back\n    if (usedCB) {\n      promise.then(function (result) {\n        usedCB(null, result);\n      }, usedCB);\n    }\n    return promise;\n  };\n}\n\nfunction logApiCall(self, name, args) {\n  /* istanbul ignore if */\n  if (self.constructor.listeners('debug').length) {\n    var logArgs = ['api', self.name, name];\n    for (var i = 0; i < args.length - 1; i++) {\n      logArgs.push(args[i]);\n    }\n    self.constructor.emit('debug', logArgs);\n\n    // override the callback itself to log the response\n    var origCallback = args[args.length - 1];\n    args[args.length - 1] = function (err, res) {\n      var responseArgs = ['api', self.name, name];\n      responseArgs = responseArgs.concat(\n        err ? ['error', err] : ['success', res]\n      );\n      self.constructor.emit('debug', responseArgs);\n      origCallback(err, res);\n    };\n  }\n}\n\nfunction adapterFun(name, callback) {\n  return toPromise(function (...args) {\n    if (this._closed) {\n      return Promise.reject(new Error('database is closed'));\n    }\n    if (this._destroyed) {\n      return Promise.reject(new Error('database is destroyed'));\n    }\n    var self = this;\n    logApiCall(self, name, args);\n    if (!this.taskqueue.isReady) {\n      return new Promise(function (fulfill, reject) {\n        self.taskqueue.addTask(function (failed) {\n          if (failed) {\n            reject(failed);\n          } else {\n            fulfill(self[name].apply(self, args));\n          }\n        });\n      });\n    }\n    return callback.apply(this, args);\n  });\n}\n\n// like underscore/lodash _.pick()\nfunction pick(obj, arr) {\n  var res = {};\n  for (var i = 0, len = arr.length; i < len; i++) {\n    var prop = arr[i];\n    if (prop in obj) {\n      res[prop] = obj[prop];\n    }\n  }\n  return res;\n}\n\n// Most browsers throttle concurrent requests at 6, so it's silly\n// to shim _bulk_get by trying to launch potentially hundreds of requests\n// and then letting the majority time out. We can handle this ourselves.\nvar MAX_NUM_CONCURRENT_REQUESTS = 6;\n\nfunction identityFunction(x) {\n  return x;\n}\n\nfunction formatResultForOpenRevsGet(result) {\n  return [{\n    ok: result\n  }];\n}\n\n// shim for P/CouchDB adapters that don't directly implement _bulk_get\nfunction bulkGet(db, opts, callback) {\n  var requests = opts.docs;\n\n  // consolidate into one request per doc if possible\n  var requestsById = new Map();\n  requests.forEach(function (request) {\n    if (requestsById.has(request.id)) {\n      requestsById.get(request.id).push(request);\n    } else {\n      requestsById.set(request.id, [request]);\n    }\n  });\n\n  var numDocs = requestsById.size;\n  var numDone = 0;\n  var perDocResults = new Array(numDocs);\n\n  function collapseResultsAndFinish() {\n    var results = [];\n    perDocResults.forEach(function (res) {\n      res.docs.forEach(function (info) {\n        results.push({\n          id: res.id,\n          docs: [info]\n        });\n      });\n    });\n    callback(null, {results});\n  }\n\n  function checkDone() {\n    if (++numDone === numDocs) {\n      collapseResultsAndFinish();\n    }\n  }\n\n  function gotResult(docIndex, id, docs) {\n    perDocResults[docIndex] = {id, docs};\n    checkDone();\n  }\n\n  var allRequests = [];\n  requestsById.forEach(function (value, key) {\n    allRequests.push(key);\n  });\n\n  var i = 0;\n\n  function nextBatch() {\n\n    if (i >= allRequests.length) {\n      return;\n    }\n\n    var upTo = Math.min(i + MAX_NUM_CONCURRENT_REQUESTS, allRequests.length);\n    var batch = allRequests.slice(i, upTo);\n    processBatch(batch, i);\n    i += batch.length;\n  }\n\n  function processBatch(batch, offset) {\n    batch.forEach(function (docId, j) {\n      var docIdx = offset + j;\n      var docRequests = requestsById.get(docId);\n\n      // just use the first request as the \"template\"\n      // TODO: The _bulk_get API allows for more subtle use cases than this,\n      // but for now it is unlikely that there will be a mix of different\n      // \"atts_since\" or \"attachments\" in the same request, since it's just\n      // replicate.js that is using this for the moment.\n      // Also, atts_since is aspirational, since we don't support it yet.\n      var docOpts = pick(docRequests[0], ['atts_since', 'attachments']);\n      docOpts.open_revs = docRequests.map(function (request) {\n        // rev is optional, open_revs disallowed\n        return request.rev;\n      });\n\n      // remove falsey / undefined revisions\n      docOpts.open_revs = docOpts.open_revs.filter(identityFunction);\n\n      var formatResult = identityFunction;\n\n      if (docOpts.open_revs.length === 0) {\n        delete docOpts.open_revs;\n\n        // when fetching only the \"winning\" leaf,\n        // transform the result so it looks like an open_revs\n        // request\n        formatResult = formatResultForOpenRevsGet;\n      }\n\n      // globally-supplied options\n      ['revs', 'attachments', 'binary', 'ajax', 'latest'].forEach(function (param) {\n        if (param in opts) {\n          docOpts[param] = opts[param];\n        }\n      });\n      db.get(docId, docOpts, function (err, res) {\n        var result;\n        /* istanbul ignore if */\n        if (err) {\n          result = [{error: err}];\n        } else {\n          result = formatResult(res);\n        }\n        gotResult(docIdx, docId, result);\n        nextBatch();\n      });\n    });\n  }\n\n  nextBatch();\n\n}\n\nvar hasLocal;\n\ntry {\n  localStorage.setItem('_pouch_check_localstorage', 1);\n  hasLocal = !!localStorage.getItem('_pouch_check_localstorage');\n} catch (e) {\n  hasLocal = false;\n}\n\nfunction hasLocalStorage() {\n  return hasLocal;\n}\n\nconst nextTick = typeof queueMicrotask === \"function\"\n  ? queueMicrotask\n  : function nextTick(fn) {\n    Promise.resolve().then(fn);\n  };\n\nclass Changes extends EventEmitter {\n  constructor() {\n    super();\n\n    this._listeners = {};\n\n    if (hasLocalStorage()) {\n      addEventListener(\"storage\", (e) => {\n        this.emit(e.key);\n      });\n    }\n  }\n\n  addListener(dbName, id, db, opts) {\n    if (this._listeners[id]) {\n      return;\n    }\n    var inprogress = false;\n    var self = this;\n    function eventFunction() {\n      if (!self._listeners[id]) {\n        return;\n      }\n      if (inprogress) {\n        inprogress = 'waiting';\n        return;\n      }\n      inprogress = true;\n      var changesOpts = pick(opts, [\n        'style', 'include_docs', 'attachments', 'conflicts', 'filter',\n        'doc_ids', 'view', 'since', 'query_params', 'binary', 'return_docs'\n      ]);\n\n      function onError() {\n        inprogress = false;\n      }\n\n      db.changes(changesOpts).on('change', function (c) {\n        if (c.seq > opts.since && !opts.cancelled) {\n          opts.since = c.seq;\n          opts.onChange(c);\n        }\n      }).on('complete', function () {\n        if (inprogress === 'waiting') {\n          nextTick(eventFunction);\n        }\n        inprogress = false;\n      }).on('error', onError);\n    }\n    this._listeners[id] = eventFunction;\n    this.on(dbName, eventFunction);\n  }\n\n  removeListener(dbName, id) {\n    if (!(id in this._listeners)) {\n      return;\n    }\n    super.removeListener(dbName, this._listeners[id]);\n    delete this._listeners[id];\n  }\n\n  notifyLocalWindows(dbName) {\n    //do a useless change on a storage thing\n    //in order to get other windows's listeners to activate\n    if (hasLocalStorage()) {\n      localStorage[dbName] = (localStorage[dbName] === \"a\") ? \"b\" : \"a\";\n    }\n  }\n\n  notify(dbName) {\n    this.emit(dbName);\n    this.notifyLocalWindows(dbName);\n  }\n}\n\nfunction guardedConsole(method) {\n  /* istanbul ignore else */\n  if (typeof console !== 'undefined' && typeof console[method] === 'function') {\n    var args = Array.prototype.slice.call(arguments, 1);\n    console[method].apply(console, args);\n  }\n}\n\nfunction randomNumber(min, max) {\n  var maxTimeout = 600000; // Hard-coded default of 10 minutes\n  min = parseInt(min, 10) || 0;\n  max = parseInt(max, 10);\n  if (max !== max || max <= min) {\n    max = (min || 1) << 1; //doubling\n  } else {\n    max = max + 1;\n  }\n  // In order to not exceed maxTimeout, pick a random value between half of maxTimeout and maxTimeout\n  if (max > maxTimeout) {\n    min = maxTimeout >> 1; // divide by two\n    max = maxTimeout;\n  }\n  var ratio = Math.random();\n  var range = max - min;\n\n  return ~~(range * ratio + min); // ~~ coerces to an int, but fast.\n}\n\nfunction defaultBackOff(min) {\n  var max = 0;\n  if (!min) {\n    max = 2000;\n  }\n  return randomNumber(min, max);\n}\n\n// designed to give info to browser users, who are disturbed\n// when they see http errors in the console\nfunction explainError(status, str) {\n  guardedConsole('info', 'The above ' + status + ' is totally normal. ' + str);\n}\n\nfunction tryFilter(filter, doc, req) {\n  try {\n    return !filter(doc, req);\n  } catch (err) {\n    var msg = 'Filter function threw: ' + err.toString();\n    return createError(BAD_REQUEST, msg);\n  }\n}\n\nfunction filterChange(opts) {\n  var req = {};\n  var hasFilter = opts.filter && typeof opts.filter === 'function';\n  req.query = opts.query_params;\n\n  return function filter(change) {\n    if (!change.doc) {\n      // CSG sends events on the changes feed that don't have documents,\n      // this hack makes a whole lot of existing code robust.\n      change.doc = {};\n    }\n\n    var filterReturn = hasFilter && tryFilter(opts.filter, change.doc, req);\n\n    if (typeof filterReturn === 'object') {\n      return filterReturn;\n    }\n\n    if (filterReturn) {\n      return false;\n    }\n\n    if (!opts.include_docs) {\n      delete change.doc;\n    } else if (!opts.attachments) {\n      for (var att in change.doc._attachments) {\n        /* istanbul ignore else */\n        if (Object.prototype.hasOwnProperty.call(change.doc._attachments, att)) {\n          change.doc._attachments[att].stub = true;\n        }\n      }\n    }\n    return true;\n  };\n}\n\n// shim for Function.prototype.name,\n// for browsers that don't support it like IE\n\n/* istanbul ignore next */\nfunction f() {}\n\nvar hasName = f.name;\nvar res;\n\n// We don't run coverage in IE\n/* istanbul ignore else */\nif (hasName) {\n  res = function (fun) {\n    return fun.name;\n  };\n} else {\n  res = function (fun) {\n    var match = fun.toString().match(/^\\s*function\\s*(?:(\\S+)\\s*)?\\(/);\n    if (match && match[1]) {\n      return match[1];\n    }\n    else {\n      return '';\n    }\n  };\n}\n\nvar res$1 = res;\n\n// Determine id an ID is valid\n//   - invalid IDs begin with an underescore that does not begin '_design' or\n//     '_local'\n//   - any other string value is a valid id\n// Returns the specific error object for each case\nfunction invalidIdError(id) {\n  var err;\n  if (!id) {\n    err = createError(MISSING_ID);\n  } else if (typeof id !== 'string') {\n    err = createError(INVALID_ID);\n  } else if (/^_/.test(id) && !(/^_(design|local)/).test(id)) {\n    err = createError(RESERVED_ID);\n  }\n  if (err) {\n    throw err;\n  }\n}\n\n// Checks if a PouchDB object is \"remote\" or not. This is\n\nfunction isRemote(db) {\n  if (typeof db._remote === 'boolean') {\n    return db._remote;\n  }\n  /* istanbul ignore next */\n  if (typeof db.type === 'function') {\n    guardedConsole('warn',\n      'db.type() is deprecated and will be removed in ' +\n      'a future version of PouchDB');\n    return db.type() === 'http';\n  }\n  /* istanbul ignore next */\n  return false;\n}\n\nfunction listenerCount(ee, type) {\n  return 'listenerCount' in ee ? ee.listenerCount(type) :\n                                 EventEmitter.listenerCount(ee, type);\n}\n\nfunction parseDesignDocFunctionName(s) {\n  if (!s) {\n    return null;\n  }\n  var parts = s.split('/');\n  if (parts.length === 2) {\n    return parts;\n  }\n  if (parts.length === 1) {\n    return [s, s];\n  }\n  return null;\n}\n\nfunction normalizeDesignDocFunctionName(s) {\n  var normalized = parseDesignDocFunctionName(s);\n  return normalized ? normalized.join('/') : null;\n}\n\n// originally parseUri 1.2.2, now patched by us\n// (c) Steven Levithan <stevenlevithan.com>\n// MIT License\nvar keys = [\"source\", \"protocol\", \"authority\", \"userInfo\", \"user\", \"password\",\n    \"host\", \"port\", \"relative\", \"path\", \"directory\", \"file\", \"query\", \"anchor\"];\nvar qName =\"queryKey\";\nvar qParser = /(?:^|&)([^&=]*)=?([^&]*)/g;\n\n// use the \"loose\" parser\n/* eslint no-useless-escape: 0 */\nvar parser = /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nfunction parseUri(str) {\n  var m = parser.exec(str);\n  var uri = {};\n  var i = 14;\n\n  while (i--) {\n    var key = keys[i];\n    var value = m[i] || \"\";\n    var encoded = ['user', 'password'].indexOf(key) !== -1;\n    uri[key] = encoded ? decodeURIComponent(value) : value;\n  }\n\n  uri[qName] = {};\n  uri[keys[12]].replace(qParser, function ($0, $1, $2) {\n    if ($1) {\n      uri[qName][$1] = $2;\n    }\n  });\n\n  return uri;\n}\n\n// Based on https://github.com/alexdavid/scope-eval v0.0.3\n// (source: https://unpkg.com/scope-eval@0.0.3/scope_eval.js)\n// This is basically just a wrapper around new Function()\n\nfunction scopeEval(source, scope) {\n  var keys = [];\n  var values = [];\n  for (var key in scope) {\n    if (Object.prototype.hasOwnProperty.call(scope, key)) {\n      keys.push(key);\n      values.push(scope[key]);\n    }\n  }\n  keys.push(source);\n  return Function.apply(null, keys).apply(null, values);\n}\n\n// this is essentially the \"update sugar\" function from daleharvey/pouchdb#1388\n// the diffFun tells us what delta to apply to the doc.  it either returns\n// the doc, or false if it doesn't need to do an update after all\nfunction upsert(db, docId, diffFun) {\n  return db.get(docId)\n    .catch(function (err) {\n      /* istanbul ignore next */\n      if (err.status !== 404) {\n        throw err;\n      }\n      return {};\n    })\n    .then(function (doc) {\n      // the user might change the _rev, so save it for posterity\n      var docRev = doc._rev;\n      var newDoc = diffFun(doc);\n\n      if (!newDoc) {\n        // if the diffFun returns falsy, we short-circuit as\n        // an optimization\n        return {updated: false, rev: docRev};\n      }\n\n      // users aren't allowed to modify these values,\n      // so reset them here\n      newDoc._id = docId;\n      newDoc._rev = docRev;\n      return tryAndPut(db, newDoc, diffFun);\n    });\n}\n\nfunction tryAndPut(db, doc, diffFun) {\n  return db.put(doc).then(function (res) {\n    return {\n      updated: true,\n      rev: res.rev\n    };\n  }, function (err) {\n    /* istanbul ignore next */\n    if (err.status !== 409) {\n      throw err;\n    }\n    return upsert(db, doc._id, diffFun);\n  });\n}\n\n/**\n * Creates a new revision string that does NOT include the revision height\n * For example '56649f1b0506c6ca9fda0746eb0cacdf'\n */\nfunction rev(doc, deterministic_revs) {\n  if (!deterministic_revs) {\n    return v4().replace(/-/g, '').toLowerCase();\n  }\n\n  var mutateableDoc = Object.assign({}, doc);\n  delete mutateableDoc._rev_tree;\n  return stringMd5(JSON.stringify(mutateableDoc));\n}\n\nvar uuid = v4; // mimic old import, only v4 is ever used elsewhere\n\nexport { adapterFun, bulkGet as bulkGetShim, Changes as changesHandler, clone, defaultBackOff, explainError, filterChange, res$1 as functionName, guardedConsole, hasLocalStorage, invalidIdError, isRemote, listenerCount, nextTick, normalizeDesignDocFunctionName as normalizeDdocFunctionName, once, parseDesignDocFunctionName as parseDdocFunctionName, parseUri, pick, rev, scopeEval, toPromise, upsert, uuid };\n", "import { btoa, readAsArrayBuffer } from 'pouchdb-binary-utils';\nimport Md5 from 'spark-md5';\n\nvar setImmediateShim = self.setImmediate || self.setTimeout;\nvar MD5_CHUNK_SIZE = 32768;\n\nfunction rawToBase64(raw) {\n  return btoa(raw);\n}\n\nfunction appendBlob(buffer, blob, start, end, callback) {\n  if (start > 0 || end < blob.size) {\n    // only slice blob if we really need to\n    blob = blob.slice(start, end);\n  }\n  readAsArrayBuffer(blob, function (arrayBuffer) {\n    buffer.append(arrayBuffer);\n    callback();\n  });\n}\n\nfunction appendString(buffer, string, start, end, callback) {\n  if (start > 0 || end < string.length) {\n    // only create a substring if we really need to\n    string = string.substring(start, end);\n  }\n  buffer.appendBinary(string);\n  callback();\n}\n\nfunction binaryMd5(data, callback) {\n  var inputIsString = typeof data === 'string';\n  var len = inputIsString ? data.length : data.size;\n  var chunkSize = Math.min(MD5_CHUNK_SIZE, len);\n  var chunks = Math.ceil(len / chunkSize);\n  var currentChunk = 0;\n  var buffer = inputIsString ? new Md5() : new Md5.ArrayBuffer();\n\n  var append = inputIsString ? appendString : appendBlob;\n\n  function next() {\n    setImmediateShim(loadNextChunk);\n  }\n\n  function done() {\n    var raw = buffer.end(true);\n    var base64 = rawToBase64(raw);\n    callback(base64);\n    buffer.destroy();\n  }\n\n  function loadNextChunk() {\n    var start = currentChunk * chunkSize;\n    var end = start + chunkSize;\n    currentChunk++;\n    if (currentChunk < chunks) {\n      append(buffer, data, start, end, next);\n    } else {\n      append(buffer, data, start, end, done);\n    }\n  }\n  loadNextChunk();\n}\n\nfunction stringMd5(string) {\n  return Md5.hash(string);\n}\n\nexport { binaryMd5, stringMd5 };\n", "import { nextTick } from 'pouchdb-utils';\n\nclass QueryParseError extends Error {\n  constructor(message) {\n    super();\n    this.status = 400;\n    this.name = 'query_parse_error';\n    this.message = message;\n    this.error = true;\n    try {\n      Error.captureStackTrace(this, QueryParseError);\n    } catch (e) {}\n  }\n}\n\nclass NotFoundError extends Error {\n  constructor(message) {\n    super();\n    this.status = 404;\n    this.name = 'not_found';\n    this.message = message;\n    this.error = true;\n    try {\n      Error.captureStackTrace(this, NotFoundError);\n    } catch (e) {}\n  }\n}\n\nclass BuiltInError extends Error {\n  constructor(message) {\n    super();\n    this.status = 500;\n    this.name = 'invalid_value';\n    this.message = message;\n    this.error = true;\n    try {\n      Error.captureStackTrace(this, BuiltInError);\n    } catch (e) {}\n  }\n}\n\nfunction promisedCallback(promise, callback) {\n  if (callback) {\n    promise.then(function (res) {\n      nextTick(function () {\n        callback(null, res);\n      });\n    }, function (reason) {\n      nextTick(function () {\n        callback(reason);\n      });\n    });\n  }\n  return promise;\n}\n\nfunction callbackify(fun) {\n  return function (...args) {\n    var cb = args.pop();\n    var promise = fun.apply(this, args);\n    if (typeof cb === 'function') {\n      promisedCallback(promise, cb);\n    }\n    return promise;\n  };\n}\n\n// Promise finally util similar to Q.finally\nfunction fin(promise, finalPromiseFactory) {\n  return promise.then(function (res) {\n    return finalPromiseFactory().then(function () {\n      return res;\n    });\n  }, function (reason) {\n    return finalPromiseFactory().then(function () {\n      throw reason;\n    });\n  });\n}\n\nfunction sequentialize(queue, promiseFactory) {\n  return function () {\n    var args = arguments;\n    var that = this;\n    return queue.add(function () {\n      return promiseFactory.apply(that, args);\n    });\n  };\n}\n\n// uniq an array of strings, order not guaranteed\n// similar to underscore/lodash _.uniq\nfunction uniq(arr) {\n  var theSet = new Set(arr);\n  var result = new Array(theSet.size);\n  var index = -1;\n  theSet.forEach(function (value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nfunction mapToKeysArray(map) {\n  var result = new Array(map.size);\n  var index = -1;\n  map.forEach(function (value, key) {\n    result[++index] = key;\n  });\n  return result;\n}\n\nexport { uniq, sequentialize, fin, callbackify, promisedCallback, mapToKeysArray, QueryParseError, NotFoundError, BuiltInError };\n", "import { base64StringToBlobOrBuffer } from 'pouchdb-binary-utils';\nimport { collate, toIndexableString, normalizeKey, parseIndexableString } from 'pouchdb-collate';\nimport { generateErrorFromResponse } from 'pouchdb-errors';\nimport { Headers } from 'pouchdb-fetch';\nimport { upsert, guardedConsole, nextTick, isRemote } from 'pouchdb-utils';\nimport { stringMd5 } from 'pouchdb-md5';\nimport { callbackify, sequentialize, uniq, fin, promisedCallback, mapToKeysArray, QueryParseError, NotFoundError, BuiltInError } from 'pouchdb-mapreduce-utils';\n\n/*\n * Simple task queue to sequentialize actions. Assumes\n * callbacks will eventually fire (once).\n */\n\nclass TaskQueue {\n  constructor() {\n    this.promise = Promise.resolve();\n  }\n\n  add(promiseFactory) {\n    this.promise = this.promise\n      // just recover\n      .catch(() => { })\n      .then(() => promiseFactory());\n    return this.promise;\n  }\n\n  finish() {\n    return this.promise;\n  }\n}\n\nfunction stringify(input) {\n  if (!input) {\n    return 'undefined'; // backwards compat for empty reduce\n  }\n  // for backwards compat with mapreduce, functions/strings are stringified\n  // as-is. everything else is JSON-stringified.\n  switch (typeof input) {\n    case 'function':\n      // e.g. a mapreduce map\n      return input.toString();\n    case 'string':\n      // e.g. a mapreduce built-in _reduce function\n      return input.toString();\n    default:\n      // e.g. a JSON object in the case of mango queries\n      return JSON.stringify(input);\n  }\n}\n\n/* create a string signature for a view so we can cache it and uniq it */\nfunction createViewSignature(mapFun, reduceFun) {\n  // the \"undefined\" part is for backwards compatibility\n  return stringify(mapFun) + stringify(reduceFun) + 'undefined';\n}\n\nasync function createView(sourceDB, viewName, mapFun, reduceFun, temporary, localDocName) {\n  const viewSignature = createViewSignature(mapFun, reduceFun);\n\n  let cachedViews;\n  if (!temporary) {\n    // cache this to ensure we don't try to update the same view twice\n    cachedViews = sourceDB._cachedViews = sourceDB._cachedViews || {};\n    if (cachedViews[viewSignature]) {\n      return cachedViews[viewSignature];\n    }\n  }\n\n  const promiseForView = sourceDB.info().then(async function (info) {\n    const depDbName = info.db_name + '-mrview-' +\n    (temporary ? 'temp' : stringMd5(viewSignature));\n\n    // save the view name in the source db so it can be cleaned up if necessary\n    // (e.g. when the _design doc is deleted, remove all associated view data)\n    function diffFunction(doc) {\n      doc.views = doc.views || {};\n      let fullViewName = viewName;\n      if (fullViewName.indexOf('/') === -1) {\n        fullViewName = viewName + '/' + viewName;\n      }\n      const depDbs = doc.views[fullViewName] = doc.views[fullViewName] || {};\n      /* istanbul ignore if */\n      if (depDbs[depDbName]) {\n        return; // no update necessary\n      }\n      depDbs[depDbName] = true;\n      return doc;\n    }\n    await upsert(sourceDB, '_local/' + localDocName, diffFunction);\n    const res = await sourceDB.registerDependentDatabase(depDbName);\n    const db = res.db;\n    db.auto_compaction = true;\n    const view = {\n      name: depDbName,\n      db,\n      sourceDB,\n      adapter: sourceDB.adapter,\n      mapFun,\n      reduceFun\n    };\n\n    let lastSeqDoc;\n    try {\n      lastSeqDoc = await view.db.get('_local/lastSeq');\n    } catch (err) {\n        /* istanbul ignore if */\n      if (err.status !== 404) {\n        throw err;\n      }\n    }\n\n    view.seq = lastSeqDoc ? lastSeqDoc.seq : 0;\n    if (cachedViews) {\n      view.db.once('destroyed', function () {\n        delete cachedViews[viewSignature];\n      });\n    }\n    return view;\n  });\n\n  if (cachedViews) {\n    cachedViews[viewSignature] = promiseForView;\n  }\n  return promiseForView;\n}\n\nconst persistentQueues = {};\nconst tempViewQueue = new TaskQueue();\nconst CHANGES_BATCH_SIZE = 50;\n\nfunction parseViewName(name) {\n  // can be either 'ddocname/viewname' or just 'viewname'\n  // (where the ddoc name is the same)\n  return name.indexOf('/') === -1 ? [name, name] : name.split('/');\n}\n\nfunction isGenOne(changes) {\n  // only return true if the current change is 1-\n  // and there are no other leafs\n  return changes.length === 1 && /^1-/.test(changes[0].rev);\n}\n\nfunction emitError(db, e, data) {\n  try {\n    db.emit('error', e);\n  } catch (err) {\n    guardedConsole('error',\n      'The user\\'s map/reduce function threw an uncaught error.\\n' +\n      'You can debug this error by doing:\\n' +\n      'myDatabase.on(\\'error\\', function (err) { debugger; });\\n' +\n      'Please double-check your map/reduce function.');\n    guardedConsole('error', e, data);\n  }\n}\n\n/**\n * Returns an \"abstract\" mapreduce object of the form:\n *\n *   {\n *     query: queryFun,\n *     viewCleanup: viewCleanupFun\n *   }\n *\n * Arguments are:\n *\n * localDoc: string\n *   This is for the local doc that gets saved in order to track the\n *   \"dependent\" DBs and clean them up for viewCleanup. It should be\n *   unique, so that indexer plugins don't collide with each other.\n * mapper: function (mapFunDef, emit)\n *   Returns a map function based on the mapFunDef, which in the case of\n *   normal map/reduce is just the de-stringified function, but may be\n *   something else, such as an object in the case of pouchdb-find.\n * reducer: function (reduceFunDef)\n *   Ditto, but for reducing. Modules don't have to support reducing\n *   (e.g. pouchdb-find).\n * ddocValidator: function (ddoc, viewName)\n *   Throws an error if the ddoc or viewName is not valid.\n *   This could be a way to communicate to the user that the configuration for the\n *   indexer is invalid.\n */\nfunction createAbstractMapReduce(localDocName, mapper, reducer, ddocValidator) {\n\n  function tryMap(db, fun, doc) {\n    // emit an event if there was an error thrown by a map function.\n    // putting try/catches in a single function also avoids deoptimizations.\n    try {\n      fun(doc);\n    } catch (e) {\n      emitError(db, e, {fun, doc});\n    }\n  }\n\n  function tryReduce(db, fun, keys, values, rereduce) {\n    // same as above, but returning the result or an error. there are two separate\n    // functions to avoid extra memory allocations since the tryCode() case is used\n    // for custom map functions (common) vs this function, which is only used for\n    // custom reduce functions (rare)\n    try {\n      return {output : fun(keys, values, rereduce)};\n    } catch (e) {\n      emitError(db, e, {fun, keys, values, rereduce});\n      return {error: e};\n    }\n  }\n\n  function sortByKeyThenValue(x, y) {\n    const keyCompare = collate(x.key, y.key);\n    return keyCompare !== 0 ? keyCompare : collate(x.value, y.value);\n  }\n\n  function sliceResults(results, limit, skip) {\n    skip = skip || 0;\n    if (typeof limit === 'number') {\n      return results.slice(skip, limit + skip);\n    } else if (skip > 0) {\n      return results.slice(skip);\n    }\n    return results;\n  }\n\n  function rowToDocId(row) {\n    const val = row.value;\n    // Users can explicitly specify a joined doc _id, or it\n    // defaults to the doc _id that emitted the key/value.\n    const docId = (val && typeof val === 'object' && val._id) || row.id;\n    return docId;\n  }\n\n  function readAttachmentsAsBlobOrBuffer(res) {\n    for (const row of res.rows) {\n      const atts = row.doc && row.doc._attachments;\n      if (!atts) {\n        continue;\n      }\n      for (const filename of Object.keys(atts)) {\n        const att = atts[filename];\n        atts[filename].data = base64StringToBlobOrBuffer(att.data, att.content_type);\n      }\n    }\n  }\n\n  function postprocessAttachments(opts) {\n    return function (res) {\n      if (opts.include_docs && opts.attachments && opts.binary) {\n        readAttachmentsAsBlobOrBuffer(res);\n      }\n      return res;\n    };\n  }\n\n  function addHttpParam(paramName, opts, params, asJson) {\n    // add an http param from opts to params, optionally json-encoded\n    let val = opts[paramName];\n    if (typeof val !== 'undefined') {\n      if (asJson) {\n        val = encodeURIComponent(JSON.stringify(val));\n      }\n      params.push(paramName + '=' + val);\n    }\n  }\n\n  function coerceInteger(integerCandidate) {\n    if (typeof integerCandidate !== 'undefined') {\n      const asNumber = Number(integerCandidate);\n      // prevents e.g. '1foo' or '1.1' being coerced to 1\n      if (!isNaN(asNumber) && asNumber === parseInt(integerCandidate, 10)) {\n        return asNumber;\n      } else {\n        return integerCandidate;\n      }\n    }\n  }\n\n  function coerceOptions(opts) {\n    opts.group_level = coerceInteger(opts.group_level);\n    opts.limit = coerceInteger(opts.limit);\n    opts.skip = coerceInteger(opts.skip);\n    return opts;\n  }\n\n  function checkPositiveInteger(number) {\n    if (number) {\n      if (typeof number !== 'number') {\n        return  new QueryParseError(`Invalid value for integer: \"${number}\"`);\n      }\n      if (number < 0) {\n        return new QueryParseError(`Invalid value for positive integer: \"${number}\"`);\n      }\n    }\n  }\n\n  function checkQueryParseError(options, fun) {\n    const startkeyName = options.descending ? 'endkey' : 'startkey';\n    const endkeyName = options.descending ? 'startkey' : 'endkey';\n\n    if (typeof options[startkeyName] !== 'undefined' &&\n      typeof options[endkeyName] !== 'undefined' &&\n      collate(options[startkeyName], options[endkeyName]) > 0) {\n      throw new QueryParseError('No rows can match your key range, ' +\n        'reverse your start_key and end_key or set {descending : true}');\n    } else if (fun.reduce && options.reduce !== false) {\n      if (options.include_docs) {\n        throw new QueryParseError('{include_docs:true} is invalid for reduce');\n      } else if (options.keys && options.keys.length > 1 &&\n        !options.group && !options.group_level) {\n        throw new QueryParseError('Multi-key fetches for reduce views must use ' +\n          '{group: true}');\n      }\n    }\n    for (const optionName of ['group_level', 'limit', 'skip']) {\n      const error = checkPositiveInteger(options[optionName]);\n      if (error) {\n        throw error;\n      }\n    }\n  }\n\n  async function httpQuery(db, fun, opts) {\n    // List of parameters to add to the PUT request\n    let params = [];\n    let body;\n    let method = 'GET';\n    let ok;\n\n    // If opts.reduce exists and is defined, then add it to the list\n    // of parameters.\n    // If reduce=false then the results are that of only the map function\n    // not the final result of map and reduce.\n    addHttpParam('reduce', opts, params);\n    addHttpParam('include_docs', opts, params);\n    addHttpParam('attachments', opts, params);\n    addHttpParam('limit', opts, params);\n    addHttpParam('descending', opts, params);\n    addHttpParam('group', opts, params);\n    addHttpParam('group_level', opts, params);\n    addHttpParam('skip', opts, params);\n    addHttpParam('stale', opts, params);\n    addHttpParam('conflicts', opts, params);\n    addHttpParam('startkey', opts, params, true);\n    addHttpParam('start_key', opts, params, true);\n    addHttpParam('endkey', opts, params, true);\n    addHttpParam('end_key', opts, params, true);\n    addHttpParam('inclusive_end', opts, params);\n    addHttpParam('key', opts, params, true);\n    addHttpParam('update_seq', opts, params);\n\n    // Format the list of parameters into a valid URI query string\n    params = params.join('&');\n    params = params === '' ? '' : '?' + params;\n\n    // If keys are supplied, issue a POST to circumvent GET query string limits\n    // see http://wiki.apache.org/couchdb/HTTP_view_API#Querying_Options\n    if (typeof opts.keys !== 'undefined') {\n      const MAX_URL_LENGTH = 2000;\n      // according to http://stackoverflow.com/a/417184/680742,\n      // the de facto URL length limit is 2000 characters\n\n      const keysAsString = `keys=${encodeURIComponent(JSON.stringify(opts.keys))}`;\n      if (keysAsString.length + params.length + 1 <= MAX_URL_LENGTH) {\n        // If the keys are short enough, do a GET. we do this to work around\n        // Safari not understanding 304s on POSTs (see pouchdb/pouchdb#1239)\n        params += (params[0] === '?' ? '&' : '?') + keysAsString;\n      } else {\n        method = 'POST';\n        if (typeof fun === 'string') {\n          body = {keys: opts.keys};\n        } else { // fun is {map : mapfun}, so append to this\n          fun.keys = opts.keys;\n        }\n      }\n    }\n\n    // We are referencing a query defined in the design doc\n    if (typeof fun === 'string') {\n      const parts = parseViewName(fun);\n\n      const response = await db.fetch('_design/' + parts[0] + '/_view/' + parts[1] + params, {\n        headers: new Headers({'Content-Type': 'application/json'}),\n        method,\n        body: JSON.stringify(body)\n      });\n      ok = response.ok;\n      // status = response.status;\n      const result = await response.json();\n\n      if (!ok) {\n        result.status = response.status;\n        throw generateErrorFromResponse(result);\n      }\n\n      // fail the entire request if the result contains an error\n      for (const row of result.rows) {\n        /* istanbul ignore if */\n        if (row.value && row.value.error && row.value.error === \"builtin_reduce_error\") {\n          throw new Error(row.reason);\n        }\n      }\n\n      return new Promise(function (resolve) {\n        resolve(result);\n      }).then(postprocessAttachments(opts));\n    }\n\n    // We are using a temporary view, terrible for performance, good for testing\n    body = body || {};\n    for (const key of Object.keys(fun)) {\n      if (Array.isArray(fun[key])) {\n        body[key] = fun[key];\n      } else {\n        body[key] = fun[key].toString();\n      }\n    }\n\n    const response = await db.fetch('_temp_view' + params, {\n      headers: new Headers({'Content-Type': 'application/json'}),\n      method: 'POST',\n      body: JSON.stringify(body)\n    });\n\n    ok = response.ok;\n    // status = response.status;\n    const result = await response.json();\n    if (!ok) {\n      result.status = response.status;\n      throw generateErrorFromResponse(result);\n    }\n\n    return new Promise(function (resolve) {\n      resolve(result);\n    }).then(postprocessAttachments(opts));\n  }\n\n  // custom adapters can define their own api._query\n  // and override the default behavior\n  /* istanbul ignore next */\n  function customQuery(db, fun, opts) {\n    return new Promise(function (resolve, reject) {\n      db._query(fun, opts, function (err, res) {\n        if (err) {\n          return reject(err);\n        }\n        resolve(res);\n      });\n    });\n  }\n\n  // custom adapters can define their own api._viewCleanup\n  // and override the default behavior\n  /* istanbul ignore next */\n  function customViewCleanup(db) {\n    return new Promise(function (resolve, reject) {\n      db._viewCleanup(function (err, res) {\n        if (err) {\n          return reject(err);\n        }\n        resolve(res);\n      });\n    });\n  }\n\n  function defaultsTo(value) {\n    return function (reason) {\n      /* istanbul ignore else */\n      if (reason.status === 404) {\n        return value;\n      } else {\n        throw reason;\n      }\n    };\n  }\n\n  // returns a promise for a list of docs to update, based on the input docId.\n  // the order doesn't matter, because post-3.2.0, bulkDocs\n  // is an atomic operation in all three adapters.\n  async function getDocsToPersist(docId, view, docIdsToChangesAndEmits) {\n    const metaDocId = '_local/doc_' + docId;\n    const defaultMetaDoc = {_id: metaDocId, keys: []};\n    const docData = docIdsToChangesAndEmits.get(docId);\n    const indexableKeysToKeyValues = docData[0];\n    const changes = docData[1];\n\n    function getMetaDoc() {\n      if (isGenOne(changes)) {\n        // generation 1, so we can safely assume initial state\n        // for performance reasons (avoids unnecessary GETs)\n        return Promise.resolve(defaultMetaDoc);\n      }\n      return view.db.get(metaDocId).catch(defaultsTo(defaultMetaDoc));\n    }\n\n    function getKeyValueDocs(metaDoc) {\n      if (!metaDoc.keys.length) {\n        // no keys, no need for a lookup\n        return Promise.resolve({rows: []});\n      }\n      return view.db.allDocs({\n        keys: metaDoc.keys,\n        include_docs: true\n      });\n    }\n\n    function processKeyValueDocs(metaDoc, kvDocsRes) {\n      const kvDocs = [];\n      const oldKeys = new Set();\n\n      for (const row of kvDocsRes.rows) {\n        const doc = row.doc;\n        if (!doc) { // deleted\n          continue;\n        }\n        kvDocs.push(doc);\n        oldKeys.add(doc._id);\n        doc._deleted = !indexableKeysToKeyValues.has(doc._id);\n        if (!doc._deleted) {\n          const keyValue = indexableKeysToKeyValues.get(doc._id);\n          if ('value' in keyValue) {\n            doc.value = keyValue.value;\n          }\n        }\n      }\n      const newKeys = mapToKeysArray(indexableKeysToKeyValues);\n      for (const key of newKeys) {\n        if (!oldKeys.has(key)) {\n          // new doc\n          const kvDoc = {\n            _id: key\n          };\n          const keyValue = indexableKeysToKeyValues.get(key);\n          if ('value' in keyValue) {\n            kvDoc.value = keyValue.value;\n          }\n          kvDocs.push(kvDoc);\n        }\n      }\n      metaDoc.keys = uniq(newKeys.concat(metaDoc.keys));\n      kvDocs.push(metaDoc);\n\n      return kvDocs;\n    }\n\n    const metaDoc = await getMetaDoc();\n    const keyValueDocs = await getKeyValueDocs(metaDoc);\n    return processKeyValueDocs(metaDoc, keyValueDocs);\n  }\n\n  function updatePurgeSeq(view) {\n    // with this approach, we just assume to have processed all missing purges and write the latest\n    // purgeSeq into the _local/purgeSeq doc.\n    return view.sourceDB.get('_local/purges').then(function (res) {\n      const purgeSeq = res.purgeSeq;\n      return view.db.get('_local/purgeSeq').then(function (res) {\n        return res._rev;\n      })\n      .catch(defaultsTo(undefined))\n      .then(function (rev) {\n        return view.db.put({\n          _id: '_local/purgeSeq',\n          _rev: rev,\n          purgeSeq,\n        });\n      });\n    }).catch(function (err) {\n      if (err.status !== 404) {\n        throw err;\n      }\n    });\n  }\n\n  // updates all emitted key/value docs and metaDocs in the mrview database\n  // for the given batch of documents from the source database\n  function saveKeyValues(view, docIdsToChangesAndEmits, seq) {\n    var seqDocId = '_local/lastSeq';\n    return view.db.get(seqDocId)\n      .catch(defaultsTo({_id: seqDocId, seq: 0}))\n      .then(function (lastSeqDoc) {\n        var docIds = mapToKeysArray(docIdsToChangesAndEmits);\n        return Promise.all(docIds.map(function (docId) {\n          return getDocsToPersist(docId, view, docIdsToChangesAndEmits);\n        })).then(function (listOfDocsToPersist) {\n          var docsToPersist = listOfDocsToPersist.flat();\n          lastSeqDoc.seq = seq;\n          docsToPersist.push(lastSeqDoc);\n          // write all docs in a single operation, update the seq once\n          return view.db.bulkDocs({docs : docsToPersist});\n        })\n          // TODO: this should be placed somewhere else, probably? we're querying both docs twice\n          //   (first time when getting the actual purges).\n          .then(() => updatePurgeSeq(view));\n      });\n  }\n\n  function getQueue(view) {\n    const viewName = typeof view === 'string' ? view : view.name;\n    let queue = persistentQueues[viewName];\n    if (!queue) {\n      queue = persistentQueues[viewName] = new TaskQueue();\n    }\n    return queue;\n  }\n\n  async function updateView(view, opts) {\n    return sequentialize(getQueue(view), function () {\n      return updateViewInQueue(view, opts);\n    })();\n  }\n\n  async function updateViewInQueue(view, opts) {\n    // bind the emit function once\n    let mapResults;\n    let doc;\n    let taskId;\n\n    function emit(key, value) {\n      const output = {id: doc._id, key: normalizeKey(key)};\n      // Don't explicitly store the value unless it's defined and non-null.\n      // This saves on storage space, because often people don't use it.\n      if (typeof value !== 'undefined' && value !== null) {\n        output.value = normalizeKey(value);\n      }\n      mapResults.push(output);\n    }\n\n    const mapFun = mapper(view.mapFun, emit);\n\n    let currentSeq = view.seq || 0;\n\n    function createTask() {\n      return view.sourceDB.info().then(function (info) {\n        taskId = view.sourceDB.activeTasks.add({\n          name: 'view_indexing',\n          total_items: info.update_seq - currentSeq,\n        });\n      });\n    }\n\n    function processChange(docIdsToChangesAndEmits, seq) {\n      return function () {\n        return saveKeyValues(view, docIdsToChangesAndEmits, seq);\n      };\n    }\n\n    let indexed_docs = 0;\n    const progress = {\n      view: view.name,\n      indexed_docs\n    };\n    view.sourceDB.emit('indexing', progress);\n\n    const queue = new TaskQueue();\n\n    async function processNextBatch() {\n      const response = await view.sourceDB.changes({\n        return_docs: true,\n        conflicts: true,\n        include_docs: true,\n        style: 'all_docs',\n        since: currentSeq,\n        limit: opts.changes_batch_size\n      });\n      const purges = await getRecentPurges();\n      return processBatch(response, purges);\n    }\n\n    function getRecentPurges() {\n      return view.db.get('_local/purgeSeq').then(function (res) {\n        return res.purgeSeq;\n      })\n      .catch(defaultsTo(-1))\n      .then(function (purgeSeq) {\n        return view.sourceDB.get('_local/purges').then(function (res) {\n          const recentPurges = res.purges.filter(function (purge, index) {\n            return index > purgeSeq;\n          }).map((purge) => purge.docId);\n\n          const uniquePurges = recentPurges.filter(function (docId, index) {\n            return recentPurges.indexOf(docId) === index;\n          });\n\n          return Promise.all(uniquePurges.map(function (docId) {\n            return view.sourceDB.get(docId).then(function (doc) {\n              return { docId, doc };\n            })\n            .catch(defaultsTo({ docId }));\n          }));\n        })\n        .catch(defaultsTo([]));\n      });\n    }\n\n    function processBatch(response, purges) {\n      const results = response.results;\n      if (!results.length && !purges.length) {\n        return;\n      }\n\n      for (const purge of purges) {\n        const index = results.findIndex(function (change) {\n          return change.id === purge.docId;\n        });\n        if (index < 0) {\n          // mimic a db.remove() on the changes feed\n          const entry = {\n            _id: purge.docId,\n            doc: {\n              _id: purge.docId,\n              _deleted: 1,\n            },\n            changes: [],\n          };\n\n          if (purge.doc) {\n            // update with new winning rev after purge\n            entry.doc = purge.doc;\n            entry.changes.push({ rev: purge.doc._rev });\n          }\n\n          results.push(entry);\n        }\n      }\n\n      const docIdsToChangesAndEmits = createDocIdsToChangesAndEmits(results);\n\n      queue.add(processChange(docIdsToChangesAndEmits, currentSeq));\n\n      indexed_docs = indexed_docs + results.length;\n      const progress = {\n        view: view.name,\n        last_seq: response.last_seq,\n        results_count: results.length,\n        indexed_docs\n      };\n      view.sourceDB.emit('indexing', progress);\n      view.sourceDB.activeTasks.update(taskId, {completed_items: indexed_docs});\n\n      if (results.length < opts.changes_batch_size) {\n        return;\n      }\n      return processNextBatch();\n    }\n\n    function createDocIdsToChangesAndEmits(results) {\n      const docIdsToChangesAndEmits = new Map();\n      for (const change of results) {\n        if (change.doc._id[0] !== '_') {\n          mapResults = [];\n          doc = change.doc;\n\n          if (!doc._deleted) {\n            tryMap(view.sourceDB, mapFun, doc);\n          }\n          mapResults.sort(sortByKeyThenValue);\n\n          const indexableKeysToKeyValues = createIndexableKeysToKeyValues(mapResults);\n          docIdsToChangesAndEmits.set(change.doc._id, [\n            indexableKeysToKeyValues,\n            change.changes\n          ]);\n        }\n        currentSeq = change.seq;\n      }\n      return docIdsToChangesAndEmits;\n    }\n\n    function createIndexableKeysToKeyValues(mapResults) {\n      const indexableKeysToKeyValues = new Map();\n      let lastKey;\n      for (let i = 0, len = mapResults.length; i < len; i++) {\n        const emittedKeyValue = mapResults[i];\n        const complexKey = [emittedKeyValue.key, emittedKeyValue.id];\n        if (i > 0 && collate(emittedKeyValue.key, lastKey) === 0) {\n          complexKey.push(i); // dup key+id, so make it unique\n        }\n        indexableKeysToKeyValues.set(toIndexableString(complexKey), emittedKeyValue);\n        lastKey = emittedKeyValue.key;\n      }\n      return indexableKeysToKeyValues;\n    }\n\n    try {\n      await createTask();\n      await processNextBatch();\n      await queue.finish();\n      view.seq = currentSeq;\n      view.sourceDB.activeTasks.remove(taskId);\n    } catch (error) {\n      view.sourceDB.activeTasks.remove(taskId, error);\n    }\n  }\n\n  function reduceView(view, results, options) {\n    if (options.group_level === 0) {\n      delete options.group_level;\n    }\n\n    const shouldGroup = options.group || options.group_level;\n    const reduceFun = reducer(view.reduceFun);\n    const groups = [];\n    const lvl = isNaN(options.group_level)\n      ? Number.POSITIVE_INFINITY\n      : options.group_level;\n\n    for (const result of results) {\n      const last = groups[groups.length - 1];\n      let groupKey = shouldGroup ? result.key : null;\n\n      // only set group_level for array keys\n      if (shouldGroup && Array.isArray(groupKey)) {\n        groupKey = groupKey.slice(0, lvl);\n      }\n\n      if (last && collate(last.groupKey, groupKey) === 0) {\n        last.keys.push([result.key, result.id]);\n        last.values.push(result.value);\n        continue;\n      }\n      groups.push({\n        keys: [[result.key, result.id]],\n        values: [result.value],\n        groupKey\n      });\n    }\n\n    results = [];\n    for (const group of groups) {\n      const reduceTry = tryReduce(view.sourceDB, reduceFun, group.keys, group.values, false);\n      if (reduceTry.error && reduceTry.error instanceof BuiltInError) {\n        // CouchDB returns an error if a built-in errors out\n        throw reduceTry.error;\n      }\n      results.push({\n        // CouchDB just sets the value to null if a non-built-in errors out\n        value: reduceTry.error ? null : reduceTry.output,\n        key: group.groupKey\n      });\n    }\n    // no total_rows/offset when reducing\n    return { rows: sliceResults(results, options.limit, options.skip) };\n  }\n\n  function queryView(view, opts) {\n    return sequentialize(getQueue(view), function () {\n      return queryViewInQueue(view, opts);\n    })();\n  }\n\n  async function queryViewInQueue(view, opts) {\n    let totalRows;\n    const shouldReduce = view.reduceFun && opts.reduce !== false;\n    const skip = opts.skip || 0;\n    if (typeof opts.keys !== 'undefined' && !opts.keys.length) {\n      // equivalent query\n      opts.limit = 0;\n      delete opts.keys;\n    }\n\n    async function fetchFromView(viewOpts) {\n      viewOpts.include_docs = true;\n      const res = await view.db.allDocs(viewOpts);\n      totalRows = res.total_rows;\n\n      return res.rows.map(function (result) {\n        // implicit migration - in older versions of PouchDB,\n        // we explicitly stored the doc as {id: ..., key: ..., value: ...}\n        // this is tested in a migration test\n        /* istanbul ignore next */\n        if ('value' in result.doc && typeof result.doc.value === 'object' &&\n          result.doc.value !== null) {\n          const keys = Object.keys(result.doc.value).sort();\n          // this detection method is not perfect, but it's unlikely the user\n          // emitted a value which was an object with these 3 exact keys\n          const expectedKeys = ['id', 'key', 'value'];\n          if (!(keys < expectedKeys || keys > expectedKeys)) {\n            return result.doc.value;\n          }\n        }\n\n        const parsedKeyAndDocId = parseIndexableString(result.doc._id);\n        return {\n          key: parsedKeyAndDocId[0],\n          id: parsedKeyAndDocId[1],\n          value: ('value' in result.doc ? result.doc.value : null)\n        };\n      });\n    }\n\n    async function onMapResultsReady(rows) {\n      let finalResults;\n      if (shouldReduce) {\n        finalResults = reduceView(view, rows, opts);\n      } else if (typeof opts.keys === 'undefined') {\n        finalResults = {\n          total_rows: totalRows,\n          offset: skip,\n          rows\n        };\n      } else {\n        // support limit, skip for keys query\n        finalResults = {\n          total_rows: totalRows,\n          offset: skip,\n          rows: sliceResults(rows,opts.limit,opts.skip)\n        };\n      }\n      /* istanbul ignore if */\n      if (opts.update_seq) {\n        finalResults.update_seq = view.seq;\n      }\n      if (opts.include_docs) {\n        const docIds = uniq(rows.map(rowToDocId));\n\n        const allDocsRes = await view.sourceDB.allDocs({\n          keys: docIds,\n          include_docs: true,\n          conflicts: opts.conflicts,\n          attachments: opts.attachments,\n          binary: opts.binary\n        });\n        const docIdsToDocs = new Map();\n        for (const row of allDocsRes.rows) {\n          docIdsToDocs.set(row.id, row.doc);\n        }\n        for (const row of rows) {\n          const docId = rowToDocId(row);\n          const doc = docIdsToDocs.get(docId);\n          if (doc) {\n            row.doc = doc;\n          }\n        }\n      }\n      return finalResults;\n    }\n\n    if (typeof opts.keys !== 'undefined') {\n      const keys = opts.keys;\n      const fetchPromises = keys.map(function (key) {\n        const viewOpts = {\n          startkey : toIndexableString([key]),\n          endkey   : toIndexableString([key, {}])\n        };\n        /* istanbul ignore if */\n        if (opts.update_seq) {\n          viewOpts.update_seq = true;\n        }\n        return fetchFromView(viewOpts);\n      });\n      const result = await Promise.all(fetchPromises);\n      const flattenedResult = result.flat();\n      return onMapResultsReady(flattenedResult);\n    } else { // normal query, no 'keys'\n      const viewOpts = {\n        descending : opts.descending\n      };\n      /* istanbul ignore if */\n      if (opts.update_seq) {\n        viewOpts.update_seq = true;\n      }\n      let startkey;\n      let endkey;\n      if ('start_key' in opts) {\n        startkey = opts.start_key;\n      }\n      if ('startkey' in opts) {\n        startkey = opts.startkey;\n      }\n      if ('end_key' in opts) {\n        endkey = opts.end_key;\n      }\n      if ('endkey' in opts) {\n        endkey = opts.endkey;\n      }\n      if (typeof startkey !== 'undefined') {\n        viewOpts.startkey = opts.descending ?\n          toIndexableString([startkey, {}]) :\n          toIndexableString([startkey]);\n      }\n      if (typeof endkey !== 'undefined') {\n        let inclusiveEnd = opts.inclusive_end !== false;\n        if (opts.descending) {\n          inclusiveEnd = !inclusiveEnd;\n        }\n\n        viewOpts.endkey = toIndexableString(\n          inclusiveEnd ? [endkey, {}] : [endkey]);\n      }\n      if (typeof opts.key !== 'undefined') {\n        const keyStart = toIndexableString([opts.key]);\n        const keyEnd = toIndexableString([opts.key, {}]);\n        if (viewOpts.descending) {\n          viewOpts.endkey = keyStart;\n          viewOpts.startkey = keyEnd;\n        } else {\n          viewOpts.startkey = keyStart;\n          viewOpts.endkey = keyEnd;\n        }\n      }\n      if (!shouldReduce) {\n        if (typeof opts.limit === 'number') {\n          viewOpts.limit = opts.limit;\n        }\n        viewOpts.skip = skip;\n      }\n\n      const result = await fetchFromView(viewOpts);\n      return onMapResultsReady(result);\n    }\n  }\n\n  async function httpViewCleanup(db) {\n    const response = await db.fetch('_view_cleanup', {\n      headers: new Headers({'Content-Type': 'application/json'}),\n      method: 'POST'\n    });\n    return response.json();\n  }\n\n  async function localViewCleanup(db) {\n    try {\n      const metaDoc = await db.get('_local/' + localDocName);\n      const docsToViews = new Map();\n\n      for (const fullViewName of Object.keys(metaDoc.views)) {\n        const parts = parseViewName(fullViewName);\n        const designDocName = '_design/' + parts[0];\n        const viewName = parts[1];\n        let views = docsToViews.get(designDocName);\n        if (!views) {\n          views = new Set();\n          docsToViews.set(designDocName, views);\n        }\n        views.add(viewName);\n      }\n      const opts = {\n        keys : mapToKeysArray(docsToViews),\n        include_docs : true\n      };\n\n      const res = await db.allDocs(opts);\n      const viewsToStatus = {};\n      for (const row of res.rows) {\n        const ddocName = row.key.substring(8); // cuts off '_design/'\n        for (const viewName of docsToViews.get(row.key)) {\n          let fullViewName = ddocName + '/' + viewName;\n          /* istanbul ignore if */\n          if (!metaDoc.views[fullViewName]) {\n            // new format, without slashes, to support PouchDB 2.2.0\n            // migration test in pouchdb's browser.migration.js verifies this\n            fullViewName = viewName;\n          }\n          const viewDBNames = Object.keys(metaDoc.views[fullViewName]);\n          // design doc deleted, or view function nonexistent\n          const statusIsGood = row.doc && row.doc.views &&\n            row.doc.views[viewName];\n          for (const viewDBName of viewDBNames) {\n            viewsToStatus[viewDBName] = viewsToStatus[viewDBName] || statusIsGood;\n          }\n        }\n      }\n\n      const dbsToDelete = Object.keys(viewsToStatus)\n        .filter(function (viewDBName) { return !viewsToStatus[viewDBName]; });\n\n      const destroyPromises = dbsToDelete.map(function (viewDBName) {\n        return sequentialize(getQueue(viewDBName), function () {\n          return new db.constructor(viewDBName, db.__opts).destroy();\n        })();\n      });\n\n      return Promise.all(destroyPromises).then(function () {\n        return {ok: true};\n      });\n    } catch (err) {\n      if (err.status === 404) {\n        return {ok: true};\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  async function queryPromised(db, fun, opts) {\n    /* istanbul ignore next */\n    if (typeof db._query === 'function') {\n      return customQuery(db, fun, opts);\n    }\n    if (isRemote(db)) {\n      return httpQuery(db, fun, opts);\n    }\n\n    const updateViewOpts = {\n      changes_batch_size: db.__opts.view_update_changes_batch_size || CHANGES_BATCH_SIZE\n    };\n\n    if (typeof fun !== 'string') {\n      // temp_view\n      checkQueryParseError(opts, fun);\n\n      tempViewQueue.add(async function () {\n        const view = await createView(\n          /* sourceDB */ db,\n          /* viewName */ 'temp_view/temp_view',\n          /* mapFun */ fun.map,\n          /* reduceFun */ fun.reduce,\n          /* temporary */ true,\n          /* localDocName */ localDocName);\n\n        return fin(updateView(view, updateViewOpts).then(\n          function () { return queryView(view, opts); }),\n          function () { return view.db.destroy(); }\n        );\n      });\n      return tempViewQueue.finish();\n    } else {\n      // persistent view\n      const fullViewName = fun;\n      const parts = parseViewName(fullViewName);\n      const designDocName = parts[0];\n      const viewName = parts[1];\n\n      const doc = await db.get('_design/' + designDocName);\n      fun = doc.views && doc.views[viewName];\n\n      if (!fun) {\n        // basic validator; it's assumed that every subclass would want this\n        throw new NotFoundError(`ddoc ${doc._id} has no view named ${viewName}`);\n      }\n\n      ddocValidator(doc, viewName);\n      checkQueryParseError(opts, fun);\n\n      const view = await createView(\n        /* sourceDB */ db,\n        /* viewName */ fullViewName,\n        /* mapFun */ fun.map,\n        /* reduceFun */ fun.reduce,\n        /* temporary */ false,\n        /* localDocName */ localDocName);\n\n      if (opts.stale === 'ok' || opts.stale === 'update_after') {\n        if (opts.stale === 'update_after') {\n          nextTick(function () {\n            updateView(view, updateViewOpts);\n          });\n        }\n        return queryView(view, opts);\n      } else { // stale not ok\n        await updateView(view, updateViewOpts);\n        return queryView(view, opts);\n      }\n    }\n  }\n\n  function abstractQuery(fun, opts, callback) {\n    const db = this;\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n    opts = opts ? coerceOptions(opts) : {};\n\n    if (typeof fun === 'function') {\n      fun = {map : fun};\n    }\n\n    const promise = Promise.resolve().then(function () {\n      return queryPromised(db, fun, opts);\n    });\n    promisedCallback(promise, callback);\n    return promise;\n  }\n\n  const abstractViewCleanup = callbackify(function () {\n    const db = this;\n    /* istanbul ignore next */\n    if (typeof db._viewCleanup === 'function') {\n      return customViewCleanup(db);\n    }\n    if (isRemote(db)) {\n      return httpViewCleanup(db);\n    }\n    return localViewCleanup(db);\n  });\n\n  return {\n    query: abstractQuery,\n    viewCleanup: abstractViewCleanup\n  };\n}\n\nexport default createAbstractMapReduce;\n", "import { clone } from 'pouchdb-utils';\nimport { collate } from 'pouchdb-collate';\n\n// this would just be \"return doc[field]\", but fields\n// can be \"deep\" due to dot notation\nfunction getFieldFromDoc(doc, parsedField) {\n  var value = doc;\n  for (var i = 0, len = parsedField.length; i < len; i++) {\n    var key = parsedField[i];\n    value = value[key];\n    if (!value) {\n      break;\n    }\n  }\n  return value;\n}\n\nfunction setFieldInDoc(doc, parsedField, value) {\n  for (var i = 0, len = parsedField.length; i < len-1; i++) {\n    var elem = parsedField[i];\n    doc = doc[elem] = doc[elem] || {};\n  }\n  doc[parsedField[len-1]] = value;\n}\n\nfunction compare(left, right) {\n  return left < right ? -1 : left > right ? 1 : 0;\n}\n\n// Converts a string in dot notation to an array of its components, with backslash escaping\nfunction parseField(fieldName) {\n  // fields may be deep (e.g. \"foo.bar.baz\"), so parse\n  var fields = [];\n  var current = '';\n  for (var i = 0, len = fieldName.length; i < len; i++) {\n    var ch = fieldName[i];\n    if (i > 0 && fieldName[i - 1] === '\\\\' && (ch === '$' || ch === '.')) {\n      // escaped delimiter\n      current = current.substring(0, current.length - 1) + ch;\n    } else if (ch === '.') {\n      // When `.` is not escaped (above), it is a field delimiter\n      fields.push(current);\n      current = '';\n    } else { // normal character\n      current += ch;\n    }\n  }\n  fields.push(current);\n  return fields;\n}\n\nvar combinationFields = ['$or', '$nor', '$not'];\nfunction isCombinationalField(field) {\n  return combinationFields.indexOf(field) > -1;\n}\n\nfunction getKey(obj) {\n  return Object.keys(obj)[0];\n}\n\nfunction getValue(obj) {\n  return obj[getKey(obj)];\n}\n\n\n// flatten an array of selectors joined by an $and operator\nfunction mergeAndedSelectors(selectors) {\n\n  // sort to ensure that e.g. if the user specified\n  // $and: [{$gt: 'a'}, {$gt: 'b'}], then it's collapsed into\n  // just {$gt: 'b'}\n  var res = {};\n  var first = {$or: true, $nor: true};\n\n  selectors.forEach(function (selector) {\n    Object.keys(selector).forEach(function (field) {\n      var matcher = selector[field];\n      if (typeof matcher !== 'object') {\n        matcher = {$eq: matcher};\n      }\n\n      if (isCombinationalField(field)) {\n        // or, nor\n        if (matcher instanceof Array) {\n          if (first[field]) {\n            first[field] = false;\n            res[field] = matcher;\n            return;\n          }\n\n          var entries = [];\n          res[field].forEach(function (existing) {\n            Object.keys(matcher).forEach(function (key) {\n              var m = matcher[key];\n              var longest = Math.max(Object.keys(existing).length, Object.keys(m).length);\n              var merged = mergeAndedSelectors([existing, m]);\n              if (Object.keys(merged).length <= longest) {\n                // we have a situation like: (a :{$eq :1} || ...) && (a {$eq: 2} || ...)\n                // merging would produce a $eq 2 when actually we shouldn't ever match against these merged conditions\n                // merged should always contain more values to be valid\n                return;\n              }\n              entries.push(merged);\n            });\n          });\n          res[field] = entries;\n        } else {\n          // not\n          res[field] = mergeAndedSelectors([matcher]);\n        }\n      } else {\n        var fieldMatchers = res[field] = res[field] || {};\n        Object.keys(matcher).forEach(function (operator) {\n          var value = matcher[operator];\n\n          if (operator === '$gt' || operator === '$gte') {\n            return mergeGtGte(operator, value, fieldMatchers);\n          } else if (operator === '$lt' || operator === '$lte') {\n            return mergeLtLte(operator, value, fieldMatchers);\n          } else if (operator === '$ne') {\n            return mergeNe(value, fieldMatchers);\n          } else if (operator === '$eq') {\n            return mergeEq(value, fieldMatchers);\n          } else if (operator === \"$regex\") {\n            return mergeRegex(value, fieldMatchers);\n          }\n          fieldMatchers[operator] = value;\n        });\n      }\n    });\n  });\n\n  return res;\n}\n\n\n\n// collapse logically equivalent gt/gte values\nfunction mergeGtGte(operator, value, fieldMatchers) {\n  if (typeof fieldMatchers.$eq !== 'undefined') {\n    return; // do nothing\n  }\n  if (typeof fieldMatchers.$gte !== 'undefined') {\n    if (operator === '$gte') {\n      if (value > fieldMatchers.$gte) { // more specificity\n        fieldMatchers.$gte = value;\n      }\n    } else { // operator === '$gt'\n      if (value >= fieldMatchers.$gte) { // more specificity\n        delete fieldMatchers.$gte;\n        fieldMatchers.$gt = value;\n      }\n    }\n  } else if (typeof fieldMatchers.$gt !== 'undefined') {\n    if (operator === '$gte') {\n      if (value > fieldMatchers.$gt) { // more specificity\n        delete fieldMatchers.$gt;\n        fieldMatchers.$gte = value;\n      }\n    } else { // operator === '$gt'\n      if (value > fieldMatchers.$gt) { // more specificity\n        fieldMatchers.$gt = value;\n      }\n    }\n  } else {\n    fieldMatchers[operator] = value;\n  }\n}\n\n// collapse logically equivalent lt/lte values\nfunction mergeLtLte(operator, value, fieldMatchers) {\n  if (typeof fieldMatchers.$eq !== 'undefined') {\n    return; // do nothing\n  }\n  if (typeof fieldMatchers.$lte !== 'undefined') {\n    if (operator === '$lte') {\n      if (value < fieldMatchers.$lte) { // more specificity\n        fieldMatchers.$lte = value;\n      }\n    } else { // operator === '$gt'\n      if (value <= fieldMatchers.$lte) { // more specificity\n        delete fieldMatchers.$lte;\n        fieldMatchers.$lt = value;\n      }\n    }\n  } else if (typeof fieldMatchers.$lt !== 'undefined') {\n    if (operator === '$lte') {\n      if (value < fieldMatchers.$lt) { // more specificity\n        delete fieldMatchers.$lt;\n        fieldMatchers.$lte = value;\n      }\n    } else { // operator === '$gt'\n      if (value < fieldMatchers.$lt) { // more specificity\n        fieldMatchers.$lt = value;\n      }\n    }\n  } else {\n    fieldMatchers[operator] = value;\n  }\n}\n\n// combine $ne values into one array\nfunction mergeNe(value, fieldMatchers) {\n  if ('$ne' in fieldMatchers) {\n    // there are many things this could \"not\" be\n    fieldMatchers.$ne.push(value);\n  } else { // doesn't exist yet\n    fieldMatchers.$ne = [value];\n  }\n}\n\n// add $eq into the mix\nfunction mergeEq(value, fieldMatchers) {\n  // these all have less specificity than the $eq\n  // TODO: check for user errors here\n  delete fieldMatchers.$gt;\n  delete fieldMatchers.$gte;\n  delete fieldMatchers.$lt;\n  delete fieldMatchers.$lte;\n  delete fieldMatchers.$ne;\n  fieldMatchers.$eq = value;\n}\n\n// combine $regex values into one array\nfunction mergeRegex(value, fieldMatchers) {\n  if ('$regex' in fieldMatchers) {\n    // a value could match multiple regexes\n    fieldMatchers.$regex.push(value);\n  } else { // doesn't exist yet\n    fieldMatchers.$regex = [value];\n  }\n}\n\n//#7458: execute function mergeAndedSelectors on nested $and\nfunction mergeAndedSelectorsNested(obj) {\n    for (var prop in obj) {\n        if (Array.isArray(obj)) {\n            for (var i in obj) {\n                if (obj[i]['$and']) {\n                    obj[i] = mergeAndedSelectors(obj[i]['$and']);\n                }\n            }\n        }\n        var value = obj[prop];\n        if (typeof value === 'object') {\n            mergeAndedSelectorsNested(value); // <- recursive call\n        }\n    }\n    return obj;\n}\n\n//#7458: determine id $and is present in selector (at any level)\nfunction isAndInSelector(obj, isAnd) {\n    for (var prop in obj) {\n        if (prop === '$and') {\n            isAnd = true;\n        }\n        var value = obj[prop];\n        if (typeof value === 'object') {\n            isAnd = isAndInSelector(value, isAnd); // <- recursive call\n        }\n    }\n    return isAnd;\n}\n\n//\n// normalize the selector\n//\nfunction massageSelector(input) {\n  var result = clone(input);\n\n  //#7458: if $and is present in selector (at any level) merge nested $and\n  if (isAndInSelector(result, false)) {\n    result = mergeAndedSelectorsNested(result);\n    if ('$and' in result) {\n      result = mergeAndedSelectors(result['$and']);\n    }\n  }\n\n  ['$or', '$nor'].forEach(function (orOrNor) {\n    if (orOrNor in result) {\n      // message each individual selector\n      // e.g. {foo: 'bar'} becomes {foo: {$eq: 'bar'}}\n      result[orOrNor].forEach(function (subSelector) {\n        var fields = Object.keys(subSelector);\n        for (var i = 0; i < fields.length; i++) {\n          var field = fields[i];\n          var matcher = subSelector[field];\n          if (typeof matcher !== 'object' || matcher === null) {\n            subSelector[field] = {$eq: matcher};\n          }\n        }\n      });\n    }\n  });\n\n  if ('$not' in result) {\n    //This feels a little like forcing, but it will work for now,\n    //I would like to come back to this and make the merging of selectors a little more generic\n    result['$not'] = mergeAndedSelectors([result['$not']]);\n  }\n\n  var fields = Object.keys(result);\n\n  for (var i = 0; i < fields.length; i++) {\n    var field = fields[i];\n    var matcher = result[field];\n\n    if (typeof matcher !== 'object' || matcher === null) {\n      matcher = {$eq: matcher};\n    }\n    result[field] = matcher;\n  }\n\n  normalizeArrayOperators(result);\n\n  return result;\n}\n\n//\n// The $ne and $regex values must be placed in an array because these operators can be used multiple times on the same field.\n// When $and is used, mergeAndedSelectors takes care of putting some of them into arrays, otherwise it's done here.\n//\nfunction normalizeArrayOperators(selector) {\n  Object.keys(selector).forEach(function (field) {\n    var matcher = selector[field];\n\n    if (Array.isArray(matcher)) {\n      matcher.forEach(function (matcherItem) {\n        if (matcherItem && typeof matcherItem === 'object') {\n          normalizeArrayOperators(matcherItem);\n        }\n      });\n    } else if (field === '$ne') {\n      selector.$ne = [matcher];\n    } else if (field === '$regex') {\n      selector.$regex = [matcher];\n    } else if (matcher && typeof matcher === 'object') {\n      normalizeArrayOperators(matcher);\n    }\n  });\n}\n\n// create a comparator based on the sort object\nfunction createFieldSorter(sort) {\n\n  function getFieldValuesAsArray(doc) {\n    return sort.map(function (sorting) {\n      var fieldName = getKey(sorting);\n      var parsedField = parseField(fieldName);\n      var docFieldValue = getFieldFromDoc(doc, parsedField);\n      return docFieldValue;\n    });\n  }\n\n  return function (aRow, bRow) {\n    var aFieldValues = getFieldValuesAsArray(aRow.doc);\n    var bFieldValues = getFieldValuesAsArray(bRow.doc);\n    var collation = collate(aFieldValues, bFieldValues);\n    if (collation !== 0) {\n      return collation;\n    }\n    // this is what mango seems to do\n    return compare(aRow.doc._id, bRow.doc._id);\n  };\n}\n\nfunction filterInMemoryFields(rows, requestDef, inMemoryFields) {\n  rows = rows.filter(function (row) {\n    return rowFilter(row.doc, requestDef.selector, inMemoryFields);\n  });\n\n  if (requestDef.sort) {\n    // in-memory sort\n    var fieldSorter = createFieldSorter(requestDef.sort);\n    rows = rows.sort(fieldSorter);\n    if (typeof requestDef.sort[0] !== 'string' &&\n        getValue(requestDef.sort[0]) === 'desc') {\n      rows = rows.reverse();\n    }\n  }\n\n  if ('limit' in requestDef || 'skip' in requestDef) {\n    // have to do the limit in-memory\n    var skip = requestDef.skip || 0;\n    var limit = ('limit' in requestDef ? requestDef.limit : rows.length) + skip;\n    rows = rows.slice(skip, limit);\n  }\n  return rows;\n}\n\nfunction rowFilter(doc, selector, inMemoryFields) {\n  return inMemoryFields.every(function (field) {\n    var matcher = selector[field];\n    var parsedField = parseField(field);\n    var docFieldValue = getFieldFromDoc(doc, parsedField);\n    if (isCombinationalField(field)) {\n      return matchCominationalSelector(field, matcher, doc);\n    }\n\n    return matchSelector(matcher, doc, parsedField, docFieldValue);\n  });\n}\n\nfunction matchSelector(matcher, doc, parsedField, docFieldValue) {\n  if (!matcher) {\n    // no filtering necessary; this field is just needed for sorting\n    return true;\n  }\n\n  // is matcher an object, if so continue recursion\n  if (typeof matcher === 'object') {\n    return Object.keys(matcher).every(function (maybeUserOperator) {\n      var userValue = matcher[ maybeUserOperator ];\n      // explicit operator\n      if (maybeUserOperator.indexOf(\"$\") === 0) {\n        return match(maybeUserOperator, doc, userValue, parsedField, docFieldValue);\n      } else {\n        var subParsedField = parseField(maybeUserOperator);\n\n        if (\n          docFieldValue === undefined &&\n          typeof userValue !== \"object\" &&\n          subParsedField.length > 0\n        ) {\n          // the field does not exist, return or getFieldFromDoc will throw\n          return false;\n        }\n\n        var subDocFieldValue = getFieldFromDoc(docFieldValue, subParsedField);\n\n        if (typeof userValue === \"object\") {\n          // field value is an object that might contain more operators\n          return matchSelector(userValue, doc, parsedField, subDocFieldValue);\n        }\n\n        // implicit operator\n        return match(\"$eq\", doc, userValue, subParsedField, subDocFieldValue);\n      }\n    });\n  }\n\n  // no more depth, No need to recurse further\n  return matcher === docFieldValue;\n}\n\nfunction matchCominationalSelector(field, matcher, doc) {\n\n  if (field === '$or') {\n    return matcher.some(function (orMatchers) {\n      return rowFilter(doc, orMatchers, Object.keys(orMatchers));\n    });\n  }\n\n  if (field === '$not') {\n    return !rowFilter(doc, matcher, Object.keys(matcher));\n  }\n\n  //`$nor`\n  return !matcher.find(function (orMatchers) {\n    return rowFilter(doc, orMatchers, Object.keys(orMatchers));\n  });\n\n}\n\nfunction match(userOperator, doc, userValue, parsedField, docFieldValue) {\n  if (!matchers[userOperator]) {\n    /* istanbul ignore next */\n    throw new Error('unknown operator \"' + userOperator +\n      '\" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, ' +\n      '$nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');\n  }\n  return matchers[userOperator](doc, userValue, parsedField, docFieldValue);\n}\n\nfunction fieldExists(docFieldValue) {\n  return typeof docFieldValue !== 'undefined' && docFieldValue !== null;\n}\n\nfunction fieldIsNotUndefined(docFieldValue) {\n  return typeof docFieldValue !== 'undefined';\n}\n\nfunction modField(docFieldValue, userValue) {\n  if (typeof docFieldValue !== \"number\" ||\n    parseInt(docFieldValue, 10) !== docFieldValue) {\n    return false;\n  }\n\n  var divisor = userValue[0];\n  var mod = userValue[1];\n\n  return docFieldValue % divisor === mod;\n}\n\nfunction arrayContainsValue(docFieldValue, userValue) {\n  return userValue.some(function (val) {\n    if (docFieldValue instanceof Array) {\n      return docFieldValue.some(function (docFieldValueItem) {\n        return collate(val, docFieldValueItem) === 0;\n      });\n    }\n\n    return collate(val, docFieldValue) === 0;\n  });\n}\n\nfunction arrayContainsAllValues(docFieldValue, userValue) {\n  return userValue.every(function (val) {\n    return docFieldValue.some(function (docFieldValueItem) {\n      return collate(val, docFieldValueItem) === 0;\n    });\n  });\n}\n\nfunction arraySize(docFieldValue, userValue) {\n  return docFieldValue.length === userValue;\n}\n\nfunction regexMatch(docFieldValue, userValue) {\n  var re = new RegExp(userValue);\n\n  return re.test(docFieldValue);\n}\n\nfunction typeMatch(docFieldValue, userValue) {\n\n  switch (userValue) {\n    case 'null':\n      return docFieldValue === null;\n    case 'boolean':\n      return typeof (docFieldValue) === 'boolean';\n    case 'number':\n      return typeof (docFieldValue) === 'number';\n    case 'string':\n      return typeof (docFieldValue) === 'string';\n    case 'array':\n      return docFieldValue instanceof Array;\n    case 'object':\n      return ({}).toString.call(docFieldValue) === '[object Object]';\n  }\n}\n\nvar matchers = {\n\n  '$elemMatch': function (doc, userValue, parsedField, docFieldValue) {\n    if (!Array.isArray(docFieldValue)) {\n      return false;\n    }\n\n    if (docFieldValue.length === 0) {\n      return false;\n    }\n\n    if (typeof docFieldValue[0] === 'object' &&  docFieldValue[0] !== null) {\n      return docFieldValue.some(function (val) {\n        return rowFilter(val, userValue, Object.keys(userValue));\n      });\n    }\n\n    return docFieldValue.some(function (val) {\n      return matchSelector(userValue, doc, parsedField, val);\n    });\n  },\n\n  '$allMatch': function (doc, userValue, parsedField, docFieldValue) {\n    if (!Array.isArray(docFieldValue)) {\n      return false;\n    }\n\n    /* istanbul ignore next */\n    if (docFieldValue.length === 0) {\n      return false;\n    }\n\n    if (typeof docFieldValue[0] === 'object' &&  docFieldValue[0] !== null) {\n      return docFieldValue.every(function (val) {\n        return rowFilter(val, userValue, Object.keys(userValue));\n      });\n    }\n\n    return docFieldValue.every(function (val) {\n      return matchSelector(userValue, doc, parsedField, val);\n    });\n  },\n\n  '$eq': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) === 0;\n  },\n\n  '$gte': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) >= 0;\n  },\n\n  '$gt': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) > 0;\n  },\n\n  '$lte': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) <= 0;\n  },\n\n  '$lt': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) < 0;\n  },\n\n  '$exists': function (doc, userValue, parsedField, docFieldValue) {\n    //a field that is null is still considered to exist\n    if (userValue) {\n      return fieldIsNotUndefined(docFieldValue);\n    }\n\n    return !fieldIsNotUndefined(docFieldValue);\n  },\n\n  '$mod': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) && modField(docFieldValue, userValue);\n  },\n\n  '$ne': function (doc, userValue, parsedField, docFieldValue) {\n    return userValue.every(function (neValue) {\n      return collate(docFieldValue, neValue) !== 0;\n    });\n  },\n  '$in': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) && arrayContainsValue(docFieldValue, userValue);\n  },\n\n  '$nin': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) && !arrayContainsValue(docFieldValue, userValue);\n  },\n\n  '$size': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) &&\n      Array.isArray(docFieldValue) &&\n      arraySize(docFieldValue, userValue);\n  },\n\n  '$all': function (doc, userValue, parsedField, docFieldValue) {\n    return Array.isArray(docFieldValue) && arrayContainsAllValues(docFieldValue, userValue);\n  },\n\n  '$regex': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) &&\n      typeof docFieldValue == \"string\" &&\n      userValue.every(function (regexValue) {\n        return regexMatch(docFieldValue, regexValue);\n      });\n  },\n\n  '$type': function (doc, userValue, parsedField, docFieldValue) {\n    return typeMatch(docFieldValue, userValue);\n  }\n};\n\n// return true if the given doc matches the supplied selector\nfunction matchesSelector(doc, selector) {\n  /* istanbul ignore if */\n  if (typeof selector !== 'object') {\n    // match the CouchDB error message\n    throw new Error('Selector error: expected a JSON object');\n  }\n\n  selector = massageSelector(selector);\n  var row = {\n    doc\n  };\n\n  var rowsMatched = filterInMemoryFields([row], { selector }, Object.keys(selector));\n  return rowsMatched && rowsMatched.length === 1;\n}\n\nexport { massageSelector, matchesSelector, filterInMemoryFields, createFieldSorter, rowFilter, isCombinationalField, getKey, getValue, getFieldFromDoc, setFieldInDoc, compare, parseField };\n", "import { createError, generateErrorFromResponse } from 'pouchdb-errors';\nimport { Headers } from 'pouchdb-fetch';\nimport abstractMapReduce from 'pouchdb-abstract-mapreduce';\nimport { stringMd5 } from 'pouchdb-md5';\nimport { collate } from 'pouchdb-collate';\nimport { getFieldFromDoc, setFieldInDoc, parseField, matchesSelector, getKey, getValue, massageSelector, compare, filterInMemoryFields } from 'pouchdb-selector-core';\nimport { clone, upsert, isRemote } from 'pouchdb-utils';\n\nconst nativeFlat = (...args) => args.flat(Infinity);\n\nconst polyFlat = (...args) => {\n  let res = [];\n  for (const subArr of args) {\n    if (Array.isArray(subArr)) {\n      res = res.concat(polyFlat(...subArr));\n    } else {\n      res.push(subArr);\n    }\n  }\n  return res;\n};\n\nconst flatten = typeof Array.prototype.flat === 'function'\n  ? nativeFlat\n  : polyFlat;\n\nfunction mergeObjects(arr) {\n  const res = {};\n  for (const element of arr) {\n    Object.assign(res, element);\n  }\n  return res;\n}\n\n// Selects a list of fields defined in dot notation from one doc\n// and copies them to a new doc. Like underscore _.pick but supports nesting.\nfunction pick(obj, arr) {\n  const res = {};\n  for (const field of arr) {\n    const parsedField = parseField(field);\n    const value = getFieldFromDoc(obj, parsedField);\n    if (typeof value !== 'undefined') {\n      setFieldInDoc(res, parsedField, value);\n    }\n  }\n  return res;\n}\n\n// e.g. ['a'], ['a', 'b'] is true, but ['b'], ['a', 'b'] is false\nfunction oneArrayIsSubArrayOfOther(left, right) {\n  for (let i = 0, len = Math.min(left.length, right.length); i < len; i++) {\n    if (left[i] !== right[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// e.g.['a', 'b', 'c'], ['a', 'b'] is false\nfunction oneArrayIsStrictSubArrayOfOther(left, right) {\n  if (left.length > right.length) {\n    return false;\n  }\n\n  return oneArrayIsSubArrayOfOther(left, right);\n}\n\n// same as above, but treat the left array as an unordered set\n// e.g. ['b', 'a'], ['a', 'b', 'c'] is true, but ['c'], ['a', 'b', 'c'] is false\nfunction oneSetIsSubArrayOfOther(left, right) {\n  left = left.slice();\n  for (const field of right) {\n    if (!left.length) {\n      break;\n    }\n    const leftIdx = left.indexOf(field);\n    if (leftIdx === -1) {\n      return false;\n    } else {\n      left.splice(leftIdx, 1);\n    }\n  }\n  return true;\n}\n\nfunction arrayToObject(arr) {\n  const res = {};\n  for (const field of arr) {\n    res[field] = true;\n  }\n  return res;\n}\n\nfunction max(arr, fun) {\n  let max = null;\n  let maxScore = -1;\n  for (const element of arr) {\n    const score = fun(element);\n    if (score > maxScore) {\n      maxScore = score;\n      max = element;\n    }\n  }\n  return max;\n}\n\nfunction arrayEquals(arr1, arr2) {\n  if (arr1.length !== arr2.length) {\n    return false;\n  }\n  for (let i = 0, len = arr1.length; i < len; i++) {\n    if (arr1[i] !== arr2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction uniq(arr) {\n  return Array.from(new Set(arr));\n}\n\n/**\n * Callbackifyable wrapper for async functions\n *\n * @template T, Args\n * @param {(...args: Args) => Promise<T>} fun\n * @returns {<CBArgs = [...Args, (err: any, value: T) => void], InnerArgs extends Args | CBArgs>(...innerArgs: InnerArgs) => InnerArgs extends CBArgs ? void : Promise<T>}\n *\n * @example\n * const fn = resolveToCallback(async () => { return 42; })\n * // with callback:\n * fn((err, value) => { ... })\n * // with await:\n * const value = await fn()\n */\nfunction resolveToCallback(fun) {\n  return function (...args) {\n    const maybeCallback = args[args.length - 1];\n    if (typeof maybeCallback === \"function\") {\n      const fulfilled = maybeCallback.bind(null, null);\n      const rejected = maybeCallback.bind(null);\n      fun.apply(this, args.slice(0, -1)).then(fulfilled, rejected);\n    } else {\n      return fun.apply(this, args);\n    }\n  };\n}\n\n// we restructure the supplied JSON considerably, because the official\n// Mango API is very particular about a lot of this stuff, but we like\n// to be liberal with what we accept in order to prevent mental\n// breakdowns in our users\nfunction massageCreateIndexRequest(requestDef) {\n  requestDef = clone(requestDef);\n\n  if (!requestDef.index) {\n    requestDef.index = {};\n  }\n\n  for (const key of ['type', 'name', 'ddoc']) {\n    if (requestDef.index[key]) {\n      requestDef[key] = requestDef.index[key];\n      delete requestDef.index[key];\n    }\n  }\n\n  if (requestDef.fields) {\n    requestDef.index.fields = requestDef.fields;\n    delete requestDef.fields;\n  }\n\n  if (!requestDef.type) {\n    requestDef.type = 'json';\n  }\n  return requestDef;\n}\n\nfunction isNonNullObject(value) {\n  return typeof value === 'object' && value !== null;\n}\n\n// throws if the user is using the wrong query field value type\nfunction checkFieldValueType(name, value, isHttp) {\n  let message = '';\n  let received = value;\n  let addReceived = true;\n  if (['$in', '$nin', '$or', '$and', '$mod', '$nor', '$all'].indexOf(name) !== -1) {\n    if (!Array.isArray(value)) {\n      message = 'Query operator ' + name + ' must be an array.';\n\n    }\n  }\n\n  if (['$not', '$elemMatch', '$allMatch'].indexOf(name) !== -1) {\n    if (!(!Array.isArray(value) && isNonNullObject(value))) {\n      message = 'Query operator ' + name + ' must be an object.';\n    }\n  }\n\n  if (name === '$mod' && Array.isArray(value)) {\n    if (value.length !== 2) {\n      message = 'Query operator $mod must be in the format [divisor, remainder], ' +\n        'where divisor and remainder are both integers.';\n    } else {\n      const divisor = value[0];\n      const mod = value[1];\n      if (divisor === 0) {\n        message = 'Query operator $mod\\'s divisor cannot be 0, cannot divide by zero.';\n        addReceived = false;\n      }\n      if (typeof divisor !== 'number' || parseInt(divisor, 10) !== divisor) {\n        message = 'Query operator $mod\\'s divisor is not an integer.';\n        received = divisor;\n      }\n      if (parseInt(mod, 10) !== mod) {\n        message = 'Query operator $mod\\'s remainder is not an integer.';\n        received = mod;\n      }\n    }\n  }\n  if (name === '$exists') {\n    if (typeof value !== 'boolean') {\n      message = 'Query operator $exists must be a boolean.';\n    }\n  }\n\n  if (name === '$type') {\n    const allowed = ['null', 'boolean', 'number', 'string', 'array', 'object'];\n    const allowedStr = '\"' + allowed.slice(0, allowed.length - 1).join('\", \"') + '\", or \"' + allowed[allowed.length - 1] + '\"';\n    if (typeof value !== 'string') {\n      message = 'Query operator $type must be a string. Supported values: ' + allowedStr + '.';\n    } else if (allowed.indexOf(value) == -1) {\n      message = 'Query operator $type must be a string. Supported values: ' + allowedStr + '.';\n    }\n  }\n\n  if (name === '$size') {\n    if (parseInt(value, 10) !== value) {\n      message = 'Query operator $size must be a integer.';\n    }\n  }\n\n  if (name === '$regex') {\n    if (typeof value !== 'string') {\n      if (isHttp) {\n        message = 'Query operator $regex must be a string.';\n      } else if (!(value instanceof RegExp)) {\n        message = 'Query operator $regex must be a string or an instance ' +\n          'of a javascript regular expression.';\n      }\n    }\n  }\n\n  if (message) {\n    if (addReceived) {\n      const type = received === null\n        ? ' '\n        : Array.isArray(received)\n          ? ' array'\n          : ' ' + typeof received;\n      const receivedStr = isNonNullObject(received)\n        ? JSON.stringify(received, null, '\\t')\n        : received;\n\n      message += ' Received' + type + ': ' + receivedStr;\n    }\n    throw new Error(message);\n  }\n}\n\nconst requireValidation = ['$all', '$allMatch', '$and', '$elemMatch', '$exists', '$in', '$mod', '$nin', '$nor', '$not', '$or', '$regex', '$size', '$type'];\nconst arrayTypeComparisonOperators = ['$in', '$nin', '$mod', '$all'];\nconst equalityOperators = ['$eq', '$gt', '$gte', '$lt', '$lte'];\n\n// recursively walks down the a query selector validating any operators\nfunction validateSelector(input, isHttp) {\n  if (Array.isArray(input)) {\n    for (const entry of input) {\n      if (isNonNullObject(entry)) {\n        validateSelector(entry, isHttp);\n      }\n    }\n  }\n  else {\n    for (const [key, value] of Object.entries(input)) {\n      if (requireValidation.indexOf(key) !== -1) {\n        checkFieldValueType(key, value, isHttp);\n      }\n      if (equalityOperators.indexOf(key) !== -1) {\n        // skip, explicit comparison operators can be anything\n        continue;\n      }\n      if (arrayTypeComparisonOperators.indexOf(key) !== -1) {\n        // skip, their values are already valid\n        continue;\n      }\n      if (isNonNullObject(value)) {\n        validateSelector(value, isHttp);\n      }\n    }\n  }\n}\n\nasync function dbFetch(db, path, opts) {\n  if (opts.body) {\n    opts.body = JSON.stringify(opts.body);\n    opts.headers = new Headers({ 'Content-type': 'application/json' });\n  }\n\n  const response = await db.fetch(path, opts);\n  const json = await response.json();\n  if (!response.ok) {\n    json.status = response.status;\n    const pouchError = createError(json);\n    throw generateErrorFromResponse(pouchError);\n  }\n  return json;\n}\n\nasync function createIndex(db, requestDef) {\n  return await dbFetch(db, '_index', {\n    method: 'POST',\n    body: massageCreateIndexRequest(requestDef)\n  });\n}\n\nasync function find(db, requestDef) {\n  validateSelector(requestDef.selector, true);\n  return await dbFetch(db, '_find', {\n    method: 'POST',\n    body: requestDef\n  });\n}\n\nasync function explain(db, requestDef) {\n  return await dbFetch(db, '_explain', {\n    method: 'POST',\n    body: requestDef\n  });\n}\n\nasync function getIndexes(db) {\n  return await dbFetch(db, '_index', {\n    method: 'GET'\n  });\n}\n\nasync function deleteIndex(db, indexDef) {\n  const ddoc = indexDef.ddoc;\n  const type = indexDef.type || 'json';\n  const name = indexDef.name;\n\n  if (!ddoc) {\n    throw new Error('you must provide an index\\'s ddoc');\n  }\n\n  if (!name) {\n    throw new Error('you must provide an index\\'s name');\n  }\n\n  const url = '_index/' + [ddoc, type, name].map(encodeURIComponent).join('/');\n\n  return await dbFetch(db, url, { method: 'DELETE' });\n}\n\n//\n// One thing about these mappers:\n//\n// Per the advice of John-David Dalton (http://youtu.be/NthmeLEhDDM),\n// what you want to do in this case is optimize for the smallest possible\n// function, since that's the thing that gets run over and over again.\n//\n// This code would be a lot simpler if all the if/elses were inside\n// the function, but it would also be a lot less performant.\n//\n\nfunction getDeepValue(value, path) {\n  for (const key of path) {\n    value = value[key];\n    if (value === undefined) {\n      return undefined;\n    }\n  }\n  return value;\n}\n\nfunction createDeepMultiMapper(fields, emit, selector) {\n  return function (doc) {\n    if (selector && !matchesSelector(doc, selector)) { return; }\n\n    const toEmit = [];\n    for (const field of fields) {\n      const value = getDeepValue(doc, parseField(field));\n      if (value === undefined) {\n        return;\n      }\n      toEmit.push(value);\n    }\n    emit(toEmit);\n  };\n}\n\nfunction createDeepSingleMapper(field, emit, selector) {\n  const parsedField = parseField(field);\n  return function (doc) {\n    if (selector && !matchesSelector(doc, selector)) { return; }\n\n    const value = getDeepValue(doc, parsedField);\n    if (value !== undefined) {\n      emit(value);\n    }\n  };\n}\n\nfunction createShallowSingleMapper(field, emit, selector) {\n  return function (doc) {\n    if (selector && !matchesSelector(doc, selector)) { return; }\n    emit(doc[field]);\n  };\n}\n\nfunction createShallowMultiMapper(fields, emit, selector) {\n  return function (doc) {\n    if (selector && !matchesSelector(doc, selector)) { return; }\n    const toEmit = fields.map(field => doc[field]);\n    emit(toEmit);\n  };\n}\n\nfunction checkShallow(fields) {\n  return fields.every((field) => field.indexOf('.') === -1);\n}\n\nfunction createMapper(fields, emit, selector) {\n  const isShallow = checkShallow(fields);\n  const isSingle = fields.length === 1;\n\n  // notice we try to optimize for the most common case,\n  // i.e. single shallow indexes\n  if (isShallow) {\n    if (isSingle) {\n      return createShallowSingleMapper(fields[0], emit, selector);\n    } else { // multi\n      return createShallowMultiMapper(fields, emit, selector);\n    }\n  } else { // deep\n    if (isSingle) {\n      return createDeepSingleMapper(fields[0], emit, selector);\n    } else { // multi\n      return createDeepMultiMapper(fields, emit, selector);\n    }\n  }\n}\n\nfunction mapper(mapFunDef, emit) {\n  // mapFunDef is a list of fields\n\n  const fields = Object.keys(mapFunDef.fields);\n  const partialSelector = mapFunDef.partial_filter_selector;\n\n  return createMapper(fields, emit, partialSelector);\n}\n\n/* istanbul ignore next */\nfunction reducer(/*reduceFunDef*/) {\n  throw new Error('reduce not supported');\n}\n\nfunction ddocValidator(ddoc, viewName) {\n  const view = ddoc.views[viewName];\n  // This doesn't actually need to be here apparently, but\n  // I feel safer keeping it.\n  /* istanbul ignore if */\n  if (!view.map || !view.map.fields) {\n    throw new Error('ddoc ' + ddoc._id + ' with view ' + viewName +\n      ' doesn\\'t have map.fields defined. ' +\n      'maybe it wasn\\'t created by this plugin?');\n  }\n}\n\nconst abstractMapper = abstractMapReduce(\n  /* localDocName */ 'indexes',\n  mapper,\n  reducer,\n  ddocValidator\n);\n\nfunction abstractMapper$1 (db) {\n  if (db._customFindAbstractMapper) {\n    return {\n      // Calls the _customFindAbstractMapper, but with a third argument:\n      // the standard findAbstractMapper query/viewCleanup.\n      // This allows the indexeddb adapter to support partial_filter_selector.\n      query: function addQueryFallback(signature, opts) {\n        const fallback = abstractMapper.query.bind(this);\n        return db._customFindAbstractMapper.query.call(this, signature, opts, fallback);\n      },\n      viewCleanup: function addViewCleanupFallback() {\n        const fallback = abstractMapper.viewCleanup.bind(this);\n        return db._customFindAbstractMapper.viewCleanup.call(this, fallback);\n      }\n    };\n  }\n  return abstractMapper;\n}\n\n// normalize the \"sort\" value\nfunction massageSort(sort) {\n  if (!Array.isArray(sort)) {\n    throw new Error('invalid sort json - should be an array');\n  }\n  return sort.map(function (sorting) {\n    if (typeof sorting === 'string') {\n      const obj = {};\n      obj[sorting] = 'asc';\n      return obj;\n    } else {\n      return sorting;\n    }\n  });\n}\n\nconst ddocIdPrefix = /^_design\\//;\nfunction massageUseIndex(useIndex) {\n  let cleanedUseIndex = [];\n  if (typeof useIndex === 'string') {\n    cleanedUseIndex.push(useIndex);\n  } else {\n    cleanedUseIndex = useIndex;\n  }\n\n  return cleanedUseIndex.map(function (name) {\n    return name.replace(ddocIdPrefix, '');\n  });\n}\n\nfunction massageIndexDef(indexDef) {\n  indexDef.fields = indexDef.fields.map(function (field) {\n    if (typeof field === 'string') {\n      const obj = {};\n      obj[field] = 'asc';\n      return obj;\n    }\n    return field;\n  });\n  if (indexDef.partial_filter_selector) {\n    indexDef.partial_filter_selector = massageSelector(\n      indexDef.partial_filter_selector\n    );\n  }\n  return indexDef;\n}\n\nfunction getKeyFromDoc(doc, index) {\n  return index.def.fields.map((obj) => {\n    const field = getKey(obj);\n    return getFieldFromDoc(doc, parseField(field));\n  });\n}\n\n// have to do this manually because REASONS. I don't know why\n// CouchDB didn't implement inclusive_start\nfunction filterInclusiveStart(rows, targetValue, index) {\n  const indexFields = index.def.fields;\n  let startAt = 0;\n  for (const row of rows) {\n    // shave off any docs at the beginning that are <= the\n    // target value\n\n    let docKey = getKeyFromDoc(row.doc, index);\n    if (indexFields.length === 1) {\n      docKey = docKey[0]; // only one field, not multi-field\n    } else { // more than one field in index\n      // in the case where e.g. the user is searching {$gt: {a: 1}}\n      // but the index is [a, b], then we need to shorten the doc key\n      while (docKey.length > targetValue.length) {\n        docKey.pop();\n      }\n    }\n    //ABS as we just looking for values that don't match\n    if (Math.abs(collate(docKey, targetValue)) > 0) {\n      // no need to filter any further; we're past the key\n      break;\n    }\n    ++startAt;\n  }\n  return startAt > 0 ? rows.slice(startAt) : rows;\n}\n\nfunction reverseOptions(opts) {\n  const newOpts = clone(opts);\n  delete newOpts.startkey;\n  delete newOpts.endkey;\n  delete newOpts.inclusive_start;\n  delete newOpts.inclusive_end;\n\n  if ('endkey' in opts) {\n    newOpts.startkey = opts.endkey;\n  }\n  if ('startkey' in opts) {\n    newOpts.endkey = opts.startkey;\n  }\n  if ('inclusive_start' in opts) {\n    newOpts.inclusive_end = opts.inclusive_start;\n  }\n  if ('inclusive_end' in opts) {\n    newOpts.inclusive_start = opts.inclusive_end;\n  }\n  return newOpts;\n}\n\nfunction validateIndex(index) {\n  const ascFields = index.fields.filter(function (field) {\n    return getValue(field) === 'asc';\n  });\n  if (ascFields.length !== 0 && ascFields.length !== index.fields.length) {\n    throw new Error('unsupported mixed sorting');\n  }\n}\n\nfunction validateSort(requestDef, index) {\n  if (index.defaultUsed && requestDef.sort) {\n    const noneIdSorts = requestDef.sort.filter(function (sortItem) {\n      return Object.keys(sortItem)[0] !== '_id';\n    }).map(function (sortItem) {\n      return Object.keys(sortItem)[0];\n    });\n\n    if (noneIdSorts.length > 0) {\n      throw new Error('Cannot sort on field(s) \"' + noneIdSorts.join(',') +\n        '\" when using the default index');\n    }\n  }\n\n  if (index.defaultUsed) {\n    return;\n  }\n}\n\nfunction validateFindRequest(requestDef) {\n  if (typeof requestDef.selector !== 'object') {\n    throw new Error('you must provide a selector when you find()');\n  }\n\n  /*var selectors = requestDef.selector['$and'] || [requestDef.selector];\n  for (var i = 0; i < selectors.length; i++) {\n    var selector = selectors[i];\n    var keys = Object.keys(selector);\n    if (keys.length === 0) {\n      throw new Error('invalid empty selector');\n    }\n    //var selection = selector[keys[0]];\n    /*if (Object.keys(selection).length !== 1) {\n      throw new Error('invalid selector: ' + JSON.stringify(selection) +\n        ' - it must have exactly one key/value');\n    }\n  }*/\n}\n\n// determine the maximum number of fields\n// we're going to need to query, e.g. if the user\n// has selection ['a'] and sorting ['a', 'b'], then we\n// need to use the longer of the two: ['a', 'b']\nfunction getUserFields(selector, sort) {\n  const selectorFields = Object.keys(selector);\n  const sortFields = sort ? sort.map(getKey) : [];\n  let userFields;\n  if (selectorFields.length >= sortFields.length) {\n    userFields = selectorFields;\n  } else {\n    userFields = sortFields;\n  }\n\n  if (sortFields.length === 0) {\n    return {\n      fields: userFields\n    };\n  }\n\n  // sort according to the user's preferred sorting\n  userFields = userFields.sort(function (left, right) {\n    let leftIdx = sortFields.indexOf(left);\n    if (leftIdx === -1) {\n      leftIdx = Number.MAX_VALUE;\n    }\n    let rightIdx = sortFields.indexOf(right);\n    if (rightIdx === -1) {\n      rightIdx = Number.MAX_VALUE;\n    }\n    return leftIdx < rightIdx ? -1 : leftIdx > rightIdx ? 1 : 0;\n  });\n\n  return {\n    fields: userFields,\n    sortOrder: sort.map(getKey)\n  };\n}\n\nasync function createIndex$1(db, requestDef) {\n  requestDef = massageCreateIndexRequest(requestDef);\n  const originalIndexDef = clone(requestDef.index);\n  requestDef.index = massageIndexDef(requestDef.index);\n\n  validateIndex(requestDef.index);\n\n  // calculating md5 is expensive - memoize and only\n  // run if required\n  let md5;\n  function getMd5() {\n    return md5 || (md5 = stringMd5(JSON.stringify(requestDef)));\n  }\n\n  const viewName = requestDef.name || ('idx-' + getMd5());\n\n  const ddocName = requestDef.ddoc || ('idx-' + getMd5());\n  const ddocId = '_design/' + ddocName;\n\n  let hasInvalidLanguage = false;\n  let viewExists = false;\n\n  function updateDdoc(doc) {\n    if (doc._rev && doc.language !== 'query') {\n      hasInvalidLanguage = true;\n    }\n    doc.language = 'query';\n    doc.views = doc.views || {};\n\n    viewExists = !!doc.views[viewName];\n\n    if (viewExists) {\n      return false;\n    }\n\n    doc.views[viewName] = {\n      map: {\n        fields: mergeObjects(requestDef.index.fields),\n        partial_filter_selector: requestDef.index.partial_filter_selector\n      },\n      reduce: '_count',\n      options: {\n        def: originalIndexDef\n      }\n    };\n\n    return doc;\n  }\n\n  db.constructor.emit('debug', ['find', 'creating index', ddocId]);\n\n  await upsert(db, ddocId, updateDdoc);\n\n  if (hasInvalidLanguage) {\n    throw new Error('invalid language for ddoc with id \"' +\n      ddocId +\n      '\" (should be \"query\")');\n  }\n\n  // kick off a build\n  // TODO: abstract-pouchdb-mapreduce should support auto-updating\n  // TODO: should also use update_after, but pouchdb/pouchdb#3415 blocks me\n  const signature = ddocName + '/' + viewName;\n  await abstractMapper$1(db).query.call(db, signature, {\n    limit: 0,\n    reduce: false\n  });\n  return {\n    id: ddocId,\n    name: viewName,\n    result: viewExists ? 'exists' : 'created'\n  };\n}\n\nasync function getIndexes$1(db) {\n  // just search through all the design docs and filter in-memory.\n  // hopefully there aren't that many ddocs.\n  const allDocsRes = await db.allDocs({\n    startkey: '_design/',\n    endkey: '_design/\\uffff',\n    include_docs: true\n  });\n  const res = {\n    indexes: [{\n      ddoc: null,\n      name: '_all_docs',\n      type: 'special',\n      def: {\n        fields: [{ _id: 'asc' }]\n      }\n    }]\n  };\n\n  res.indexes = flatten(res.indexes, allDocsRes.rows.filter(function (row) {\n    return row.doc.language === 'query';\n  }).map(function (row) {\n    const viewNames = row.doc.views !== undefined ? Object.keys(row.doc.views) : [];\n\n    return viewNames.map(function (viewName) {\n      const view = row.doc.views[viewName];\n      return {\n        ddoc: row.id,\n        name: viewName,\n        type: 'json',\n        def: massageIndexDef(view.options.def)\n      };\n    });\n  }));\n\n  // these are sorted by view name for some reason\n  res.indexes.sort(function (left, right) {\n    return compare(left.name, right.name);\n  });\n  res.total_rows = res.indexes.length;\n  return res;\n}\n\n// couchdb lowest collation value\nconst COLLATE_LO = null;\n\n// couchdb highest collation value (TODO: well not really, but close enough amirite)\nconst COLLATE_HI = { \"\\uffff\": {} };\n\nconst SHORT_CIRCUIT_QUERY = {\n  queryOpts: { limit: 0, startkey: COLLATE_HI, endkey: COLLATE_LO },\n  inMemoryFields: [],\n};\n\n// couchdb second-lowest collation value\n\nfunction checkFieldInIndex(index, field) {\n  return index.def.fields\n    .some((key) => getKey(key) === field);\n}\n\n// so when you do e.g. $eq/$eq, we can do it entirely in the database.\n// but when you do e.g. $gt/$eq, the first part can be done\n// in the database, but the second part has to be done in-memory,\n// because $gt has forced us to lose precision.\n// so that's what this determines\nfunction userOperatorLosesPrecision(selector, field) {\n  const matcher = selector[field];\n  const userOperator = getKey(matcher);\n\n  return userOperator !== '$eq';\n}\n\n// sort the user fields by their position in the index,\n// if they're in the index\nfunction sortFieldsByIndex(userFields, index) {\n  const indexFields = index.def.fields.map(getKey);\n\n  return userFields.slice().sort(function (a, b) {\n    let aIdx = indexFields.indexOf(a);\n    let bIdx = indexFields.indexOf(b);\n    if (aIdx === -1) {\n      aIdx = Number.MAX_VALUE;\n    }\n    if (bIdx === -1) {\n      bIdx = Number.MAX_VALUE;\n    }\n    return compare(aIdx, bIdx);\n  });\n}\n\n// first pass to try to find fields that will need to be sorted in-memory\nfunction getBasicInMemoryFields(index, selector, userFields) {\n  userFields = sortFieldsByIndex(userFields, index);\n\n  // check if any of the user selectors lose precision\n  let needToFilterInMemory = false;\n  for (let i = 0, len = userFields.length; i < len; i++) {\n    const field = userFields[i];\n    if (needToFilterInMemory || !checkFieldInIndex(index, field)) {\n      return userFields.slice(i);\n    }\n    if (i < len - 1 && userOperatorLosesPrecision(selector, field)) {\n      needToFilterInMemory = true;\n    }\n  }\n  return [];\n}\n\nfunction getInMemoryFieldsFromNe(selector) {\n  const fields = [];\n  for (const [field, matcher] of Object.entries(selector)) {\n    for (const operator of Object.keys(matcher)) {\n      if (operator === '$ne') {\n        fields.push(field);\n      }\n    }\n  }\n  return fields;\n}\n\nfunction getInMemoryFields(coreInMemoryFields, index, selector, userFields) {\n  const result = flatten(\n    // in-memory fields reported as necessary by the query planner\n    coreInMemoryFields,\n    // combine with another pass that checks for any we may have missed\n    getBasicInMemoryFields(index, selector, userFields),\n    // combine with another pass that checks for $ne's\n    getInMemoryFieldsFromNe(selector)\n  );\n\n  return sortFieldsByIndex(uniq(result), index);\n}\n\n// check that at least one field in the user's query is represented\n// in the index. order matters in the case of sorts\nfunction checkIndexFieldsMatch(indexFields, sortOrder, fields) {\n  if (sortOrder) {\n    // array has to be a strict subarray of index array. furthermore,\n    // the sortOrder fields need to all be represented in the index\n    const sortMatches = oneArrayIsStrictSubArrayOfOther(sortOrder, indexFields);\n    const selectorMatches = oneArrayIsSubArrayOfOther(fields, indexFields);\n\n    return sortMatches && selectorMatches;\n  }\n\n  // all of the user's specified fields still need to be\n  // on the left side of the index array, although the order\n  // doesn't matter\n  return oneSetIsSubArrayOfOther(fields, indexFields);\n}\n\nconst logicalMatchers = ['$eq', '$gt', '$gte', '$lt', '$lte'];\nfunction isNonLogicalMatcher(matcher) {\n  return logicalMatchers.indexOf(matcher) === -1;\n}\n\n// check all the index fields for usages of '$ne'\n// e.g. if the user queries {foo: {$ne: 'foo'}, bar: {$eq: 'bar'}},\n// then we can neither use an index on ['foo'] nor an index on\n// ['foo', 'bar'], but we can use an index on ['bar'] or ['bar', 'foo']\nfunction checkFieldsLogicallySound(indexFields, selector) {\n  const firstField = indexFields[0];\n  const matcher = selector[firstField];\n\n  if (typeof matcher === 'undefined') {\n    /* istanbul ignore next */\n    return true;\n  }\n\n  const isInvalidNe = Object.keys(matcher).length === 1 &&\n    getKey(matcher) === '$ne';\n\n  return !isInvalidNe;\n}\n\nfunction checkIndexMatches(index, sortOrder, fields, selector) {\n  const indexFields = index.def.fields.map(getKey);\n  const fieldsMatch = checkIndexFieldsMatch(indexFields, sortOrder, fields);\n\n  if (!fieldsMatch) {\n    return false;\n  }\n\n  return checkFieldsLogicallySound(indexFields, selector);\n}\n\n//\n// the algorithm is very simple:\n// take all the fields the user supplies, and if those fields\n// are a strict subset of the fields in some index,\n// then use that index\n//\nfunction findMatchingIndexes(selector, userFields, sortOrder, indexes) {\n  return indexes.filter(function (index) {\n    return checkIndexMatches(index, sortOrder, userFields, selector);\n  });\n}\n\n// find the best index, i.e. the one that matches the most fields\n// in the user's query\nfunction findBestMatchingIndex(selector, userFields, sortOrder, indexes, useIndex) {\n  const matchingIndexes = findMatchingIndexes(selector, userFields, sortOrder, indexes);\n\n  if (matchingIndexes.length === 0) {\n    if (useIndex) {\n      throw {\n        error: \"no_usable_index\",\n        message: \"There is no index available for this selector.\"\n      };\n    }\n    // return `all_docs` as a default index;\n    // I'm assuming that _all_docs is always first\n    const defaultIndex = indexes[0];\n    defaultIndex.defaultUsed = true;\n    return defaultIndex;\n  }\n  if (matchingIndexes.length === 1 && !useIndex) {\n    return matchingIndexes[0];\n  }\n\n  const userFieldsMap = arrayToObject(userFields);\n\n  function scoreIndex(index) {\n    const indexFields = index.def.fields.map(getKey);\n    let score = 0;\n    for (const indexField of indexFields) {\n      if (userFieldsMap[indexField]) {\n        score++;\n      }\n    }\n    return score;\n  }\n\n  if (useIndex) {\n    const useIndexDdoc = '_design/' + useIndex[0];\n    const useIndexName = useIndex.length === 2 ? useIndex[1] : false;\n    const index = matchingIndexes.find(function (index) {\n      if (useIndexName && index.ddoc === useIndexDdoc && useIndexName === index.name) {\n        return true;\n      }\n\n      if (index.ddoc === useIndexDdoc) {\n        /* istanbul ignore next */\n        return true;\n      }\n\n      return false;\n    });\n\n    if (!index) {\n      throw {\n        error: \"unknown_error\",\n        message: \"Could not find that index or could not use that index for the query\"\n      };\n    }\n    return index;\n  }\n\n  return max(matchingIndexes, scoreIndex);\n}\n\nfunction getSingleFieldQueryOptsFor(userOperator, userValue) {\n  switch (userOperator) {\n    case '$eq':\n      return { key: userValue };\n    case '$lte':\n      return { endkey: userValue };\n    case '$gte':\n      return { startkey: userValue };\n    case '$lt':\n      return {\n        endkey: userValue,\n        inclusive_end: false\n      };\n    case '$gt':\n      return {\n        startkey: userValue,\n        inclusive_start: false\n      };\n  }\n\n  return {\n    startkey: COLLATE_LO\n  };\n}\n\nfunction getSingleFieldCoreQueryPlan(selector, index) {\n  const field = getKey(index.def.fields[0]);\n  //ignoring this because the test to exercise the branch is skipped at the moment\n  /* istanbul ignore next */\n  const matcher = selector[field] || {};\n  const inMemoryFields = [];\n  const userOperators = Object.keys(matcher);\n\n  let combinedOpts;\n\n  for (const userOperator of userOperators) {\n    if (isNonLogicalMatcher(userOperator)) {\n      inMemoryFields.push(field);\n    }\n\n    const userValue = matcher[userOperator];\n    const newQueryOpts = getSingleFieldQueryOptsFor(userOperator, userValue);\n\n    if (combinedOpts) {\n      combinedOpts = mergeObjects([combinedOpts, newQueryOpts]);\n    } else {\n      combinedOpts = newQueryOpts;\n    }\n  }\n\n  return {\n    queryOpts: combinedOpts,\n    inMemoryFields\n  };\n}\n\nfunction getMultiFieldCoreQueryPlan(userOperator, userValue) {\n  switch (userOperator) {\n    case '$eq':\n      return {\n        startkey: userValue,\n        endkey: userValue\n      };\n    case '$lte':\n      return {\n        endkey: userValue\n      };\n    case '$gte':\n      return {\n        startkey: userValue\n      };\n    case '$lt':\n      return {\n        endkey: userValue,\n        inclusive_end: false\n      };\n    case '$gt':\n      return {\n        startkey: userValue,\n        inclusive_start: false\n      };\n  }\n}\n\nfunction getMultiFieldQueryOpts(selector, index) {\n  const indexFields = index.def.fields.map(getKey);\n\n  let inMemoryFields = [];\n  const startkey = [];\n  const endkey = [];\n  let inclusiveStart;\n  let inclusiveEnd;\n\n  function finish(i) {\n\n    if (inclusiveStart !== false) {\n      startkey.push(COLLATE_LO);\n    }\n    if (inclusiveEnd !== false) {\n      endkey.push(COLLATE_HI);\n    }\n    // keep track of the fields where we lost specificity,\n    // and therefore need to filter in-memory\n    inMemoryFields = indexFields.slice(i);\n  }\n\n  for (let i = 0, len = indexFields.length; i < len; i++) {\n    const indexField = indexFields[i];\n    const matcher = selector[indexField];\n\n    if (!matcher || !Object.keys(matcher).length) { // fewer fields in user query than in index\n      finish(i);\n      break;\n    } else if (Object.keys(matcher).some(isNonLogicalMatcher)) { // non-logical are ignored\n      finish(i);\n      break;\n    } else if (i > 0) {\n      const usingGtlt = (\n        '$gt' in matcher || '$gte' in matcher ||\n        '$lt' in matcher || '$lte' in matcher);\n      const previousKeys = Object.keys(selector[indexFields[i - 1]]);\n      const previousWasEq = arrayEquals(previousKeys, ['$eq']);\n      const previousWasSame = arrayEquals(previousKeys, Object.keys(matcher));\n      const gtltLostSpecificity = usingGtlt && !previousWasEq && !previousWasSame;\n      if (gtltLostSpecificity) {\n        finish(i);\n        break;\n      }\n    }\n\n    const userOperators = Object.keys(matcher);\n    let combinedOpts = null;\n\n    for (const userOperator of userOperators) {\n      const userValue = matcher[userOperator];\n      const newOpts = getMultiFieldCoreQueryPlan(userOperator, userValue);\n\n      if (combinedOpts) {\n        combinedOpts = mergeObjects([combinedOpts, newOpts]);\n      } else {\n        combinedOpts = newOpts;\n      }\n    }\n\n    startkey.push('startkey' in combinedOpts ? combinedOpts.startkey : COLLATE_LO);\n    endkey.push('endkey' in combinedOpts ? combinedOpts.endkey : COLLATE_HI);\n    if ('inclusive_start' in combinedOpts) {\n      inclusiveStart = combinedOpts.inclusive_start;\n    }\n    if ('inclusive_end' in combinedOpts) {\n      inclusiveEnd = combinedOpts.inclusive_end;\n    }\n  }\n\n  const res = {\n    startkey,\n    endkey\n  };\n\n  if (typeof inclusiveStart !== 'undefined') {\n    res.inclusive_start = inclusiveStart;\n  }\n  if (typeof inclusiveEnd !== 'undefined') {\n    res.inclusive_end = inclusiveEnd;\n  }\n\n  return {\n    queryOpts: res,\n    inMemoryFields\n  };\n}\n\nfunction shouldShortCircuit(selector) {\n  // We have a field to select from, but not a valid value\n  // this should result in a short circuited query\n  // just like the http adapter (couchdb) and mongodb\n  // see tests for issue #7810\n\n  // @todo Use 'Object.values' when Node.js v6 support is dropped.\n  const values = Object.keys(selector).map(function (key) {\n    return selector[key];\n  });\n  return values.some(function (val) {\n    return typeof val === 'object' && Object.keys(val).length === 0;\n  });\n}\n\nfunction getDefaultQueryPlan(selector) {\n  //using default index, so all fields need to be done in memory\n  return {\n    queryOpts: { startkey: null },\n    inMemoryFields: [Object.keys(selector)]\n  };\n}\n\nfunction getCoreQueryPlan(selector, index) {\n  if (index.defaultUsed) {\n    return getDefaultQueryPlan(selector, index);\n  }\n\n  if (index.def.fields.length === 1) {\n    // one field in index, so the value was indexed as a singleton\n    return getSingleFieldCoreQueryPlan(selector, index);\n  }\n  // else index has multiple fields, so the value was indexed as an array\n  return getMultiFieldQueryOpts(selector, index);\n}\n\nfunction planQuery(request, indexes) {\n  const selector = request.selector;\n  const sort = request.sort;\n\n  if (shouldShortCircuit(selector)) {\n    return Object.assign({}, SHORT_CIRCUIT_QUERY, { index: indexes[0] });\n  }\n\n  const userFieldsRes = getUserFields(selector, sort);\n\n  const userFields = userFieldsRes.fields;\n  const sortOrder = userFieldsRes.sortOrder;\n  const index = findBestMatchingIndex(selector, userFields, sortOrder, indexes, request.use_index);\n\n  const coreQueryPlan = getCoreQueryPlan(selector, index);\n  const queryOpts = coreQueryPlan.queryOpts;\n  const coreInMemoryFields = coreQueryPlan.inMemoryFields;\n\n  const inMemoryFields = getInMemoryFields(coreInMemoryFields, index, selector, userFields);\n\n  return {\n    queryOpts,\n    index,\n    inMemoryFields\n  };\n}\n\nfunction indexToSignature(index) {\n  // remove '_design/'\n  return index.ddoc.substring(8) + '/' + index.name;\n}\n\nasync function doAllDocs(db, originalOpts) {\n  const opts = clone(originalOpts);\n\n  // CouchDB responds in weird ways when you provide a non-string to _id;\n  // we mimic the behavior for consistency. See issue66 tests for details.\n  if (opts.descending) {\n    if ('endkey' in opts && typeof opts.endkey !== 'string') {\n      opts.endkey = '';\n    }\n    if ('startkey' in opts && typeof opts.startkey !== 'string') {\n      opts.limit = 0;\n    }\n  } else {\n    if ('startkey' in opts && typeof opts.startkey !== 'string') {\n      opts.startkey = '';\n    }\n    if ('endkey' in opts && typeof opts.endkey !== 'string') {\n      opts.limit = 0;\n    }\n  }\n  if ('key' in opts && typeof opts.key !== 'string') {\n    opts.limit = 0;\n  }\n\n  if (opts.limit > 0 && opts.indexes_count) {\n    // brute force and quite naive impl.\n    // amp up the limit with the amount of (indexes) design docs\n    // or is this too naive? How about skip?\n    opts.original_limit = opts.limit;\n    opts.limit += opts.indexes_count;\n  }\n\n  const res = await db.allDocs(opts);\n  // filter out any design docs that _all_docs might return\n  res.rows = res.rows.filter(function (row) {\n    return !/^_design\\//.test(row.id);\n  });\n  // put back original limit\n  if (opts.original_limit) {\n    opts.limit = opts.original_limit;\n  }\n  // enforce the rows to respect the given limit\n  res.rows = res.rows.slice(0, opts.limit);\n  return res;\n}\n\nasync function queryAllOrIndex(db, opts, indexToUse) {\n  if (indexToUse.name === '_all_docs') {\n    return doAllDocs(db, opts);\n  }\n  return abstractMapper$1(db).query.call(db, indexToSignature(indexToUse), opts);\n}\n\nasync function find$1(db, requestDef, explain) {\n  if (requestDef.selector) {\n    // must be validated before massaging\n    validateSelector(requestDef.selector, false);\n    requestDef.selector = massageSelector(requestDef.selector);\n  }\n\n  if (requestDef.sort) {\n    requestDef.sort = massageSort(requestDef.sort);\n  }\n\n  if (requestDef.use_index) {\n    requestDef.use_index = massageUseIndex(requestDef.use_index);\n  }\n\n  if (!('limit' in requestDef)) {\n    // Match the default limit of CouchDB\n    requestDef.limit = 25;\n  }\n\n  validateFindRequest(requestDef);\n\n  const getIndexesRes = await getIndexes$1(db);\n\n  db.constructor.emit('debug', ['find', 'planning query', requestDef]);\n  const queryPlan = planQuery(requestDef, getIndexesRes.indexes);\n  db.constructor.emit('debug', ['find', 'query plan', queryPlan]);\n\n  const indexToUse = queryPlan.index;\n\n  validateSort(requestDef, indexToUse);\n\n  let opts = Object.assign({\n    include_docs: true,\n    reduce: false,\n    // Add amount of index for doAllDocs to use (related to issue #7810)\n    indexes_count: getIndexesRes.total_rows,\n  }, queryPlan.queryOpts);\n\n  if ('startkey' in opts && 'endkey' in opts &&\n    collate(opts.startkey, opts.endkey) > 0) {\n    // can't possibly return any results, startkey > endkey\n    /* istanbul ignore next */\n    return { docs: [] };\n  }\n\n  const isDescending = requestDef.sort &&\n    typeof requestDef.sort[0] !== 'string' &&\n    getValue(requestDef.sort[0]) === 'desc';\n\n  if (isDescending) {\n    // either all descending or all ascending\n    opts.descending = true;\n    opts = reverseOptions(opts);\n  }\n\n  if (!queryPlan.inMemoryFields.length) {\n    // no in-memory filtering necessary, so we can let the\n    // database do the limit/skip for us\n    opts.limit = requestDef.limit;\n    if ('skip' in requestDef) {\n      opts.skip = requestDef.skip;\n    }\n  }\n\n  if (explain) {\n    return Promise.resolve(queryPlan, opts);\n  }\n\n  const res = await queryAllOrIndex(db, opts, indexToUse);\n\n\n  if (opts.inclusive_start === false) {\n    // may have to manually filter the first one,\n    // since couchdb has no true inclusive_start option\n    res.rows = filterInclusiveStart(res.rows, opts.startkey, indexToUse);\n  }\n\n  if (queryPlan.inMemoryFields.length) {\n    // need to filter some stuff in-memory\n    res.rows = filterInMemoryFields(res.rows, requestDef, queryPlan.inMemoryFields);\n  }\n\n  const resp = {\n    docs: res.rows.map(function (row) {\n      const doc = row.doc;\n      if (requestDef.fields) {\n        return pick(doc, requestDef.fields);\n      }\n      return doc;\n    })\n  };\n\n  if (indexToUse.defaultUsed) {\n    resp.warning = 'No matching index found, create an index to optimize query time.';\n  }\n\n  return resp;\n}\n\nasync function explain$1(db, requestDef) {\n  const queryPlan = await find$1(db, requestDef, true);\n\n  return {\n    dbname: db.name,\n    index: queryPlan.index,\n    selector: requestDef.selector,\n    range: {\n      start_key: queryPlan.queryOpts.startkey,\n      end_key: queryPlan.queryOpts.endkey,\n    },\n    opts: {\n      use_index: requestDef.use_index || [],\n      bookmark: \"nil\", //hardcoded to match CouchDB since its not supported,\n      limit: requestDef.limit,\n      skip: requestDef.skip,\n      sort: requestDef.sort || {},\n      fields: requestDef.fields,\n      conflicts: false, //hardcoded to match CouchDB since its not supported,\n      r: [49], // hardcoded to match CouchDB since its not support\n    },\n    limit: requestDef.limit,\n    skip: requestDef.skip || 0,\n    fields: requestDef.fields,\n  };\n}\n\nasync function deleteIndex$1(db, index) {\n\n  if (!index.ddoc) {\n    throw new Error('you must supply an index.ddoc when deleting');\n  }\n\n  if (!index.name) {\n    throw new Error('you must supply an index.name when deleting');\n  }\n\n  const docId = index.ddoc;\n  const viewName = index.name;\n\n  function deltaFun(doc) {\n    if (Object.keys(doc.views).length === 1 && doc.views[viewName]) {\n      // only one view in this ddoc, delete the whole ddoc\n      return {_id: docId, _deleted: true};\n    }\n    // more than one view here, just remove the view\n    delete doc.views[viewName];\n    return doc;\n  }\n\n  await upsert(db, docId, deltaFun);\n  await abstractMapper$1(db).viewCleanup.apply(db);\n  return { ok: true };\n}\n\nconst plugin = {};\nplugin.createIndex = resolveToCallback(async function (requestDef) {\n  if (typeof requestDef !== 'object') {\n    throw new Error('you must provide an index to create');\n  }\n\n  const createIndex$$1 = isRemote(this) ?\n    createIndex : createIndex$1;\n  return createIndex$$1(this, requestDef);\n});\n\nplugin.find = resolveToCallback(async function (requestDef) {\n  if (typeof requestDef !== 'object') {\n    throw new Error('you must provide search parameters to find()');\n  }\n\n  const find$$1 = isRemote(this) ? find : find$1;\n  return find$$1(this, requestDef);\n});\n\nplugin.explain = resolveToCallback(async function (requestDef) {\n  if (typeof requestDef !== 'object') {\n    throw new Error('you must provide search parameters to explain()');\n  }\n\n  const find$$1 = isRemote(this) ? explain : explain$1;\n  return find$$1(this, requestDef);\n});\n\nplugin.getIndexes = resolveToCallback(async function () {\n  const getIndexes$$1 = isRemote(this) ? getIndexes : getIndexes$1;\n  return getIndexes$$1(this);\n});\n\nplugin.deleteIndex = resolveToCallback(async function (indexDef) {\n  if (typeof indexDef !== 'object') {\n    throw new Error('you must provide an index to delete');\n  }\n\n  const deleteIndex$$1 = isRemote(this) ?\n    deleteIndex : deleteIndex$1;\n  return deleteIndex$$1(this, indexDef);\n});\n\nexport default plugin;\n"], "mappings": ";;;;;;;;;;AAAA,IAAM,aAAN,cAAyB,MAAM;AAAA,EAC7B,YAAY,QAAQ,OAAO,QAAQ;AACjC,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,UAAU;AAAA,MACpB,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAEA,IAAI,eAAe,IAAI,WAAW,KAAK,gBAAgB,gCAAgC;AACvF,IAAI,oBAAoB,IAAI,WAAW,KAAK,eAAe,6BAA6B;AACxF,IAAI,cAAc,IAAI,WAAW,KAAK,aAAa,SAAS;AAC5D,IAAI,eAAe,IAAI,WAAW,KAAK,YAAY,0BAA0B;AAC7E,IAAI,aAAa,IAAI,WAAW,KAAK,eAAe,iCAAiC;AACrF,IAAI,aAAa,IAAI,WAAW,KAAK,cAAc,0BAA0B;AAC7E,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,uDAAuD;AAC5G,IAAI,WAAW,IAAI,WAAW,KAAK,uBAAuB,mBAAmB;AAC7E,IAAI,gBAAgB,IAAI,WAAW,KAAK,iBAAiB,uCAAuC;AAChG,IAAI,UAAU,IAAI,WAAW,KAAK,UAAU,gCAAgC;AAC5E,IAAI,kBAAkB,IAAI,WAAW,KAAK,mBAAmB,qBAAqB;AAClF,IAAI,oBAAoB,IAAI,WAAW,KAAK,qBAAqB,iCAAiC;AAClG,IAAI,iBAAiB,IAAI,WAAW,KAAK,kBAAkB,6BAA6B;AACxF,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,kCAAkC;AACvF,IAAI,gBAAgB,IAAI,WAAW,KAAK,eAAe,gCAAgC;AACvF,IAAI,aAAa,IAAI,WAAW,KAAK,aAAa,oBAAoB;AACtE,IAAI,YAAY,IAAI,WAAW,KAAK,uBAAuB,SAAS;AACpE,IAAI,YAAY,IAAI,WAAW,KAAK,oBAAoB,SAAS;AACjE,IAAI,YAAY,IAAI,WAAW,KAAK,yBAAyB,SAAS;AACtE,IAAI,YAAY,IAAI,WAAW,KAAK,aAAa,sDAAsD;AACvG,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,oBAAoB;AACzE,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,6DAA6D;AAClH,IAAI,eAAe,IAAI,WAAW,KAAK,gBAAgB,6CAA8C;AACrG,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,yBAAyB;AAE9E,SAAS,YAAY,OAAO,QAAQ;AAClC,WAAS,iBAAiBA,SAAQ;AAGhC,QAAI,QAAQ,OAAO,oBAAoB,KAAK;AAC5C,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,UAAI,OAAO,MAAM,MAAM,CAAC,CAAC,MAAM,YAAY;AACzC,aAAK,MAAM,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC,CAAC;AAAA,MACjC;AAAA,IACF;AAEA,QAAI,KAAK,UAAU,QAAW;AAC5B,WAAK,QAAS,IAAI,MAAM,EAAG;AAAA,IAC7B;AAEA,QAAIA,YAAW,QAAW;AACxB,WAAK,SAASA;AAAA,IAChB;AAAA,EACF;AACA,mBAAiB,YAAY,WAAW;AACxC,SAAO,IAAI,iBAAiB,MAAM;AACpC;AAEA,SAAS,0BAA0B,KAAK;AAEtC,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,OAAO;AACX,UAAM;AACN,QAAI,OAAO;AAAA,EACb;AAEA,MAAI,WAAW,OAAO,IAAI,UAAU,YAAY;AAC9C,QAAI,OAAO;AACX,QAAI,SAAS;AAAA,EACf;AAEA,MAAI,EAAE,UAAU,MAAM;AACpB,QAAI,OAAO,IAAI,SAAS;AAAA,EAC1B;AAEA,MAAI,EAAE,YAAY,MAAM;AACtB,QAAI,SAAS;AAAA,EACf;AAEA,MAAI,EAAE,aAAa,MAAM;AACvB,QAAI,UAAU,IAAI,WAAW,IAAI;AAAA,EACnC;AAEA,MAAI,EAAE,WAAW,MAAM;AACrB,QAAI,QAAS,IAAI,MAAM,EAAG;AAAA,EAC5B;AAEA,SAAO;AACT;;;AChGA,IAAI,IAAI;;;ACDR,IAAI,WAAW,SAAU,KAAK;AAC5B,SAAO,KAAK,GAAG;AACjB;AASA,SAAS,WAAW,OAAO,YAAY;AAErC,UAAQ,SAAS,CAAC;AAClB,eAAa,cAAc,CAAC;AAC5B,MAAI;AACF,WAAO,IAAI,KAAK,OAAO,UAAU;AAAA,EACnC,SAAS,GAAG;AACV,QAAI,EAAE,SAAS,aAAa;AAC1B,YAAM;AAAA,IACR;AACA,QAAI,UAAU,OAAO,gBAAgB,cAAc,cACrC,OAAO,kBAAkB,cAAc,gBACvC,OAAO,mBAAmB,cAAc,iBACxC;AACd,QAAI,UAAU,IAAI,QAAQ;AAC1B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,cAAQ,OAAO,MAAM,CAAC,CAAC;AAAA,IACzB;AACA,WAAO,QAAQ,QAAQ,WAAW,IAAI;AAAA,EACxC;AACF;AAIA,SAAS,0BAA0B,KAAK;AACtC,MAAI,SAAS,IAAI;AACjB,MAAI,MAAM,IAAI,YAAY,MAAM;AAChC,MAAI,MAAM,IAAI,WAAW,GAAG;AAC5B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,QAAI,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,EAC3B;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,WAAW,MAAM;AAC3C,SAAO,WAAW,CAAC,0BAA0B,SAAS,CAAC,GAAG,EAAC,KAAI,CAAC;AAClE;AAEA,SAAS,aAAa,KAAK,MAAM;AAC/B,SAAO,mBAAmB,SAAS,GAAG,GAAG,IAAI;AAC/C;;;ACnDA,SAAS,IAAI,KAAK,SAAS,YAAY;AACrC,MAAI,UAAU;AACd,MAAI,eAAe,aAAa,IAAI;AAEpC,SAAO,QAAQ,SAAS,cAAc;AACpC,eAAW;AAAA,EACb;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,KAAK,SAAS,YAAY;AACzC,MAAI,UAAU,IAAI,KAAK,SAAS,UAAU;AAC1C,SAAO,UAAU;AACnB;AAEA,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,IAAI,MAAM;AAEV,SAAS,QAAQ,GAAG,GAAG;AAErB,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,CAAC;AAClB,MAAI,aAAa,CAAC;AAElB,MAAI,KAAK,eAAe,CAAC;AACzB,MAAI,KAAK,eAAe,CAAC;AACzB,MAAK,KAAK,OAAQ,GAAG;AACnB,WAAO,KAAK;AAAA,EACd;AACA,UAAQ,OAAO,GAAG;AAAA,IAChB,KAAK;AACH,aAAO,IAAI;AAAA,IACb,KAAK;AACH,aAAO,IAAI,IAAI,KAAK;AAAA,IACtB,KAAK;AACH,aAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,MAAM,QAAQ,CAAC,IAAI,aAAa,GAAG,CAAC,IAAI,cAAc,GAAG,CAAC;AACnE;AAIA,SAAS,aAAa,KAAK;AACzB,UAAQ,OAAO,KAAK;AAAA,IAClB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,UAAI,QAAQ,YAAY,QAAQ,aAAa,MAAM,GAAG,GAAG;AACvD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,KAAK;AACH,UAAI,UAAU;AACd,UAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,YAAI,MAAM,IAAI;AACd,cAAM,IAAI,MAAM,GAAG;AACnB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,CAAC,IAAI,aAAa,QAAQ,CAAC,CAAC;AAAA,QAClC;AAAA,MAEF,WAAW,eAAe,MAAM;AAC9B,eAAO,IAAI,OAAO;AAAA,MACpB,WAAW,QAAQ,MAAM;AACvB,cAAM,CAAC;AACP,iBAAS,KAAK,SAAS;AACrB,cAAI,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACpD,gBAAI,MAAM,QAAQ,CAAC;AACnB,gBAAI,OAAO,QAAQ,aAAa;AAC9B,kBAAI,CAAC,IAAI,aAAa,GAAG;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,EACJ;AACA,SAAO;AACT;AAEA,SAAS,SAAS,KAAK;AACrB,MAAI,QAAQ,MAAM;AAChB,YAAQ,OAAO,KAAK;AAAA,MAClB,KAAK;AACH,eAAO,MAAM,IAAI;AAAA,MACnB,KAAK;AACH,eAAO,qBAAqB,GAAG;AAAA,MACjC,KAAK;AAOH,eAAO,IACJ,QAAQ,WAAW,IAAc,EACjC,QAAQ,WAAW,IAAc,EACjC,QAAQ,WAAW,IAAc;AAAA;AAAA,MAEtC,KAAK;AACH,YAAI,UAAU,MAAM,QAAQ,GAAG;AAC/B,YAAI,MAAM,UAAU,MAAM,OAAO,KAAK,GAAG;AACzC,YAAI,IAAI;AACR,YAAI,MAAM,IAAI;AACd,YAAI,SAAS;AACb,YAAI,SAAS;AACX,iBAAO,EAAE,IAAI,KAAK;AAChB,sBAAU,kBAAkB,IAAI,CAAC,CAAC;AAAA,UACpC;AAAA,QACF,OAAO;AACL,iBAAO,EAAE,IAAI,KAAK;AAChB,gBAAI,SAAS,IAAI,CAAC;AAClB,sBAAU,kBAAkB,MAAM,IAC9B,kBAAkB,IAAI,MAAM,CAAC;AAAA,UACnC;AAAA,QACF;AACA,eAAO;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,kBAAkB,KAAK;AAC9B,MAAI,OAAO;AACX,QAAM,aAAa,GAAG;AACtB,SAAO,eAAe,GAAG,IAAI,MAAM,SAAS,GAAG,IAAI;AACrD;AAEA,SAAS,YAAY,KAAK,GAAG;AAC3B,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI,OAAO,IAAI,CAAC,MAAM;AACtB,MAAI,MAAM;AACR,UAAM;AACN;AAAA,EACF,OAAO;AACL,QAAI,MAAM,IAAI,CAAC,MAAM;AACrB;AACA,QAAI,cAAc;AAClB,QAAI,cAAc,IAAI,UAAU,GAAG,IAAI,gBAAgB;AACvD,QAAI,YAAY,SAAS,aAAa,EAAE,IAAI;AAE5C,QAAI,KAAK;AACP,kBAAY,CAAC;AAAA,IACf;AACA,SAAK;AACL,WAAO,MAAM;AACX,UAAI,KAAK,IAAI,CAAC;AACd,UAAI,OAAO,MAAU;AACnB;AAAA,MACF,OAAO;AACL,uBAAe;AAAA,MACjB;AACA;AAAA,IACF;AACA,kBAAc,YAAY,MAAM,GAAG;AACnC,QAAI,YAAY,WAAW,GAAG;AAC5B,YAAM,SAAS,aAAa,EAAE;AAAA,IAChC,OAAO;AAEL,YAAM,WAAW,YAAY,CAAC,IAAI,MAAM,YAAY,CAAC,CAAC;AAAA,IACxD;AAEA,QAAI,KAAK;AACP,YAAM,MAAM;AAAA,IACd;AAEA,QAAI,cAAc,GAAG;AAInB,YAAM,WAAW,MAAM,MAAM,SAAS;AAAA,IACxC;AAAA,EACF;AACA,SAAO,EAAC,KAAK,QAAS,IAAI,YAAW;AACvC;AAIA,SAAS,IAAI,OAAO,WAAW;AAC7B,MAAI,MAAM,MAAM,IAAI;AAEpB,MAAI,UAAU,QAAQ;AACpB,QAAI,kBAAkB,UAAU,UAAU,SAAS,CAAC;AACpD,QAAI,QAAQ,gBAAgB,SAAS;AAEnC,gBAAU,IAAI;AACd,wBAAkB,UAAU,UAAU,SAAS,CAAC;AAAA,IAClD;AACA,QAAI,UAAU,gBAAgB;AAC9B,QAAI,mBAAmB,gBAAgB;AACvC,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAQ,KAAK,GAAG;AAAA,IAClB,WAAW,qBAAqB,MAAM,SAAS,GAAG;AAChD,UAAI,MAAM,MAAM,IAAI;AACpB,cAAQ,GAAG,IAAI;AAAA,IACjB,OAAO;AACL,YAAM,KAAK,GAAG;AAAA,IAChB;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,KAAK;AACjC,MAAI,QAAQ,CAAC;AACb,MAAI,YAAY,CAAC;AACjB,MAAI,IAAI;AAGR,SAAO,MAAM;AACX,QAAIC,kBAAiB,IAAI,GAAG;AAC5B,QAAIA,oBAAmB,MAAU;AAC/B,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO,MAAM,IAAI;AAAA,MACnB,OAAO;AACL,YAAI,OAAO,SAAS;AACpB;AAAA,MACF;AAAA,IACF;AACA,YAAQA,iBAAgB;AAAA,MACtB,KAAK;AACH,cAAM,KAAK,IAAI;AACf;AAAA,MACF,KAAK;AACH,cAAM,KAAK,IAAI,CAAC,MAAM,GAAG;AACzB;AACA;AAAA,MACF,KAAK;AACH,YAAI,YAAY,YAAY,KAAK,CAAC;AAClC,cAAM,KAAK,UAAU,GAAG;AACxB,aAAK,UAAU;AACf;AAAA,MACF,KAAK;AACH,YAAI,YAAY;AAEhB,eAAO,MAAM;AACX,cAAI,KAAK,IAAI,CAAC;AACd,cAAI,OAAO,MAAU;AACnB;AAAA,UACF;AACA,uBAAa;AACb;AAAA,QACF;AAIA,oBAAY,UAAU,QAAQ,iBAAiB,IAAQ,EACpD,QAAQ,iBAAiB,GAAQ,EACjC,QAAQ,iBAAiB,GAAQ;AAEpC,cAAM,KAAK,SAAS;AACpB;AAAA,MACF,KAAK;AACH,YAAI,eAAe,EAAE,SAAS,CAAC,GAAG,OAAO,MAAM,OAAO;AACtD,cAAM,KAAK,aAAa,OAAO;AAC/B,kBAAU,KAAK,YAAY;AAC3B;AAAA,MACF,KAAK;AACH,YAAI,aAAa,EAAE,SAAS,CAAC,GAAG,OAAO,MAAM,OAAO;AACpD,cAAM,KAAK,WAAW,OAAO;AAC7B,kBAAU,KAAK,UAAU;AACzB;AAAA;AAAA,MAEF;AACE,cAAM,IAAI;AAAA,UACR,8DACEA;AAAA,QAAc;AAAA,IACtB;AAAA,EACF;AACF;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,MAAM,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM;AACrC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,OAAO,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC7B,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAQ,EAAE,WAAW,EAAE,SAAU,IAC9B,EAAE,SAAS,EAAE,SAAU,IAAI;AAChC;AACA,SAAS,cAAc,GAAG,GAAG;AAI3B,SAAQ,MAAM,IAAK,IAAM,IAAI,IAAK,IAAI;AACxC;AACA,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,KAAK,OAAO,KAAK,CAAC,GAAG,KAAK,OAAO,KAAK,CAAC;AAC3C,MAAI,MAAM,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM;AACvC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAE5B,QAAI,OAAO,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC/B,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AACjC,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AAAA,EAEF;AACA,SAAQ,GAAG,WAAW,GAAG,SAAU,IAChC,GAAG,SAAS,GAAG,SAAU,IAAI;AAClC;AAKA,SAAS,eAAe,GAAG;AACzB,MAAI,KAAK,CAAC,WAAW,UAAU,UAAU,QAAQ;AACjD,MAAI,MAAM,GAAG,QAAQ,OAAO,CAAC;AAE7B,MAAI,CAAC,KAAK;AACR,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT;AACA,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO,MAAM,IAAK,MAAM,IAAM,MAAM;AAAA,EACtC;AAEA,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO;AAAA,EACT;AACF;AAOA,SAAS,qBAAqB,KAAK;AAEjC,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AAIA,MAAI,YAAY,IAAI,cAAc,EAAE,MAAM,MAAM;AAChD,MAAI,YAAY,SAAS,UAAU,CAAC,GAAG,EAAE;AAEzC,MAAI,MAAM,MAAM;AAEhB,MAAI,SAAS,MAAM,MAAM;AAIzB,MAAI,oBAAqB,MAAM,CAAC,YAAY,aAAa;AACzD,MAAI,YAAY,QAAS,iBAAkB,SAAS,GAAG,KAAK,gBAAgB;AAE5E,YAAU,MAAM;AAGhB,MAAI,SAAS,KAAK,IAAI,WAAW,UAAU,CAAC,CAAC,CAAC;AAE9C,MAAI,KAAK;AACP,aAAS,KAAK;AAAA,EAChB;AAEA,MAAI,YAAY,OAAO,QAAQ,EAAE;AAGjC,cAAY,UAAU,QAAQ,UAAU,EAAE;AAE1C,YAAU,MAAM;AAEhB,SAAO;AACT;;;ACtXA,oBAAyB;;;ACAzB,uBAAgB;AAEhB,IAAI,mBAAmB,KAAK,gBAAgB,KAAK;AA6DjD,SAAS,UAAU,QAAQ;AACzB,SAAO,iBAAAC,QAAI,KAAK,MAAM;AACxB;;;AD7DA,SAAS,eAAe,QAAQ;AAC9B,SAAQ,OAAO,gBAAgB,eAAe,kBAAkB,eAC7D,OAAO,SAAS,eAAe,kBAAkB;AACtD;AAOA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,kBAAkB,cACrB,OAAO,MAAM,CAAC,IACd,OAAO,MAAM,GAAG,OAAO,MAAM,OAAO,IAAI;AAC9C;AAMA,IAAI,eAAe,SAAS,UAAU;AACtC,IAAI,mBAAmB,aAAa,KAAK,MAAM;AAE/C,SAAS,cAAc,OAAO;AAC5B,MAAI,QAAQ,OAAO,eAAe,KAAK;AAEvC,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM;AACjB,SAAQ,OAAO,QAAQ,cACrB,gBAAgB,QAAQ,aAAa,KAAK,IAAI,KAAK;AACvD;AAEA,SAAS,MAAM,QAAQ;AACrB,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,gBAAY,CAAC;AACb,SAAK,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAU,CAAC,IAAI,MAAM,OAAO,CAAC,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAIA,MAAI,kBAAkB,QAAQ,SAAS,MAAM,GAAG;AAC9C,WAAO,OAAO,YAAY;AAAA,EAC5B;AAEA,MAAI,eAAe,MAAM,GAAG;AAC1B,WAAO,kBAAkB,MAAM;AAAA,EACjC;AAEA,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,cAAY,CAAC;AACb,OAAK,KAAK,QAAQ;AAEhB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,CAAC,GAAG;AACnD,UAAI,QAAQ,MAAM,OAAO,CAAC,CAAC;AAC3B,UAAI,OAAO,UAAU,aAAa;AAChC,kBAAU,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAqPA,IAAI;AAEJ,IAAI;AACF,eAAa,QAAQ,6BAA6B,CAAC;AACnD,aAAW,CAAC,CAAC,aAAa,QAAQ,2BAA2B;AAC/D,SAAS,GAAG;AACV,aAAW;AACb;AAMA,IAAM,WAAW,OAAO,mBAAmB,aACvC,iBACA,SAASC,UAAS,IAAI;AACtB,UAAQ,QAAQ,EAAE,KAAK,EAAE;AAC3B;AA6EF,SAAS,eAAe,QAAQ;AAE9B,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,MAAM,MAAM,YAAY;AAC3E,QAAI,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAClD,YAAQ,MAAM,EAAE,MAAM,SAAS,IAAI;AAAA,EACrC;AACF;AAqFA,SAAS,IAAI;AAAC;AAEd,IAAI,UAAU,EAAE;AAChB,IAAI;AAIJ,IAAI,SAAS;AACX,QAAM,SAAU,KAAK;AACnB,WAAO,IAAI;AAAA,EACb;AACF,OAAO;AACL,QAAM,SAAU,KAAK;AACnB,QAAIC,SAAQ,IAAI,SAAS,EAAE,MAAM,gCAAgC;AACjE,QAAIA,UAASA,OAAM,CAAC,GAAG;AACrB,aAAOA,OAAM,CAAC;AAAA,IAChB,OACK;AACH,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAyBA,SAAS,SAAS,IAAI;AACpB,MAAI,OAAO,GAAG,YAAY,WAAW;AACnC,WAAO,GAAG;AAAA,EACZ;AAEA,MAAI,OAAO,GAAG,SAAS,YAAY;AACjC;AAAA,MAAe;AAAA,MACb;AAAA,IAC6B;AAC/B,WAAO,GAAG,KAAK,MAAM;AAAA,EACvB;AAEA,SAAO;AACT;AAgFA,SAAS,OAAO,IAAI,OAAO,SAAS;AAClC,SAAO,GAAG,IAAI,KAAK,EAChB,MAAM,SAAU,KAAK;AAEpB,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM;AAAA,IACR;AACA,WAAO,CAAC;AAAA,EACV,CAAC,EACA,KAAK,SAAU,KAAK;AAEnB,QAAI,SAAS,IAAI;AACjB,QAAI,SAAS,QAAQ,GAAG;AAExB,QAAI,CAAC,QAAQ;AAGX,aAAO,EAAC,SAAS,OAAO,KAAK,OAAM;AAAA,IACrC;AAIA,WAAO,MAAM;AACb,WAAO,OAAO;AACd,WAAO,UAAU,IAAI,QAAQ,OAAO;AAAA,EACtC,CAAC;AACL;AAEA,SAAS,UAAU,IAAI,KAAK,SAAS;AACnC,SAAO,GAAG,IAAI,GAAG,EAAE,KAAK,SAAUC,MAAK;AACrC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,KAAKA,KAAI;AAAA,IACX;AAAA,EACF,GAAG,SAAU,KAAK;AAEhB,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM;AAAA,IACR;AACA,WAAO,OAAO,IAAI,IAAI,KAAK,OAAO;AAAA,EACpC,CAAC;AACH;;;AEjrBA,IAAM,kBAAN,MAAM,yBAAwB,MAAM;AAAA,EAClC,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI;AACF,YAAM,kBAAkB,MAAM,gBAAe;AAAA,IAC/C,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,IAAM,gBAAN,MAAM,uBAAsB,MAAM;AAAA,EAChC,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI;AACF,YAAM,kBAAkB,MAAM,cAAa;AAAA,IAC7C,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,IAAM,eAAN,MAAM,sBAAqB,MAAM;AAAA,EAC/B,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI;AACF,YAAM,kBAAkB,MAAM,aAAY;AAAA,IAC5C,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,SAAS,iBAAiB,SAAS,UAAU;AAC3C,MAAI,UAAU;AACZ,YAAQ,KAAK,SAAUC,MAAK;AAC1B,eAAS,WAAY;AACnB,iBAAS,MAAMA,IAAG;AAAA,MACpB,CAAC;AAAA,IACH,GAAG,SAAU,QAAQ;AACnB,eAAS,WAAY;AACnB,iBAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,SAAS,YAAY,KAAK;AACxB,SAAO,YAAa,MAAM;AACxB,QAAI,KAAK,KAAK,IAAI;AAClB,QAAI,UAAU,IAAI,MAAM,MAAM,IAAI;AAClC,QAAI,OAAO,OAAO,YAAY;AAC5B,uBAAiB,SAAS,EAAE;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,IAAI,SAAS,qBAAqB;AACzC,SAAO,QAAQ,KAAK,SAAUA,MAAK;AACjC,WAAO,oBAAoB,EAAE,KAAK,WAAY;AAC5C,aAAOA;AAAA,IACT,CAAC;AAAA,EACH,GAAG,SAAU,QAAQ;AACnB,WAAO,oBAAoB,EAAE,KAAK,WAAY;AAC5C,YAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,cAAc,OAAO,gBAAgB;AAC5C,SAAO,WAAY;AACjB,QAAI,OAAO;AACX,QAAI,OAAO;AACX,WAAO,MAAM,IAAI,WAAY;AAC3B,aAAO,eAAe,MAAM,MAAM,IAAI;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAIA,SAAS,KAAK,KAAK;AACjB,MAAI,SAAS,IAAI,IAAI,GAAG;AACxB,MAAI,SAAS,IAAI,MAAM,OAAO,IAAI;AAClC,MAAI,QAAQ;AACZ,SAAO,QAAQ,SAAU,OAAO;AAC9B,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,KAAK;AAC3B,MAAI,SAAS,IAAI,MAAM,IAAI,IAAI;AAC/B,MAAI,QAAQ;AACZ,MAAI,QAAQ,SAAU,OAAO,KAAK;AAChC,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,CAAC;AACD,SAAO;AACT;;;AChGA,IAAM,YAAN,MAAgB;AAAA,EACd,cAAc;AACZ,SAAK,UAAU,QAAQ,QAAQ;AAAA,EACjC;AAAA,EAEA,IAAI,gBAAgB;AAClB,SAAK,UAAU,KAAK,QAEjB,MAAM,MAAM;AAAA,IAAE,CAAC,EACf,KAAK,MAAM,eAAe,CAAC;AAC9B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AACF;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAGA,UAAQ,OAAO,OAAO;AAAA,IACpB,KAAK;AAEH,aAAO,MAAM,SAAS;AAAA,IACxB,KAAK;AAEH,aAAO,MAAM,SAAS;AAAA,IACxB;AAEE,aAAO,KAAK,UAAU,KAAK;AAAA,EAC/B;AACF;AAGA,SAAS,oBAAoB,QAAQ,WAAW;AAE9C,SAAO,UAAU,MAAM,IAAI,UAAU,SAAS,IAAI;AACpD;AAEA,SAAe,WAAW,UAAU,UAAU,QAAQ,WAAW,WAAW,cAAc;AAAA;AACxF,UAAM,gBAAgB,oBAAoB,QAAQ,SAAS;AAE3D,QAAI;AACJ,QAAI,CAAC,WAAW;AAEd,oBAAc,SAAS,eAAe,SAAS,gBAAgB,CAAC;AAChE,UAAI,YAAY,aAAa,GAAG;AAC9B,eAAO,YAAY,aAAa;AAAA,MAClC;AAAA,IACF;AAEA,UAAM,iBAAiB,SAAS,KAAK,EAAE,KAAK,SAAgB,MAAM;AAAA;AAChE,cAAM,YAAY,KAAK,UAAU,cAChC,YAAY,SAAS,UAAU,aAAa;AAI7C,iBAAS,aAAa,KAAK;AACzB,cAAI,QAAQ,IAAI,SAAS,CAAC;AAC1B,cAAI,eAAe;AACnB,cAAI,aAAa,QAAQ,GAAG,MAAM,IAAI;AACpC,2BAAe,WAAW,MAAM;AAAA,UAClC;AACA,gBAAM,SAAS,IAAI,MAAM,YAAY,IAAI,IAAI,MAAM,YAAY,KAAK,CAAC;AAErE,cAAI,OAAO,SAAS,GAAG;AACrB;AAAA,UACF;AACA,iBAAO,SAAS,IAAI;AACpB,iBAAO;AAAA,QACT;AACA,cAAM,OAAO,UAAU,YAAY,cAAc,YAAY;AAC7D,cAAMC,OAAM,MAAM,SAAS,0BAA0B,SAAS;AAC9D,cAAM,KAAKA,KAAI;AACf,WAAG,kBAAkB;AACrB,cAAM,OAAO;AAAA,UACX,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,SAAS,SAAS;AAAA,UAClB;AAAA,UACA;AAAA,QACF;AAEA,YAAI;AACJ,YAAI;AACF,uBAAa,MAAM,KAAK,GAAG,IAAI,gBAAgB;AAAA,QACjD,SAAS,KAAK;AAEZ,cAAI,IAAI,WAAW,KAAK;AACtB,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,aAAK,MAAM,aAAa,WAAW,MAAM;AACzC,YAAI,aAAa;AACf,eAAK,GAAG,KAAK,aAAa,WAAY;AACpC,mBAAO,YAAY,aAAa;AAAA,UAClC,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AAAA,KAAC;AAED,QAAI,aAAa;AACf,kBAAY,aAAa,IAAI;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA;AAEA,IAAM,mBAAmB,CAAC;AAC1B,IAAM,gBAAgB,IAAI,UAAU;AACpC,IAAM,qBAAqB;AAE3B,SAAS,cAAc,MAAM;AAG3B,SAAO,KAAK,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG;AACjE;AAEA,SAAS,SAAS,SAAS;AAGzB,SAAO,QAAQ,WAAW,KAAK,MAAM,KAAK,QAAQ,CAAC,EAAE,GAAG;AAC1D;AAEA,SAAS,UAAU,IAAI,GAAG,MAAM;AAC9B,MAAI;AACF,OAAG,KAAK,SAAS,CAAC;AAAA,EACpB,SAAS,KAAK;AACZ;AAAA,MAAe;AAAA,MACb;AAAA,IAG+C;AACjD,mBAAe,SAAS,GAAG,IAAI;AAAA,EACjC;AACF;AA4BA,SAAS,wBAAwB,cAAcC,SAAQC,UAASC,gBAAe;AAE7E,WAAS,OAAO,IAAI,KAAK,KAAK;AAG5B,QAAI;AACF,UAAI,GAAG;AAAA,IACT,SAAS,GAAG;AACV,gBAAU,IAAI,GAAG,EAAC,KAAK,IAAG,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,WAAS,UAAU,IAAI,KAAK,MAAM,QAAQ,UAAU;AAKlD,QAAI;AACF,aAAO,EAAC,QAAS,IAAI,MAAM,QAAQ,QAAQ,EAAC;AAAA,IAC9C,SAAS,GAAG;AACV,gBAAU,IAAI,GAAG,EAAC,KAAK,MAAM,QAAQ,SAAQ,CAAC;AAC9C,aAAO,EAAC,OAAO,EAAC;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,mBAAmB,GAAG,GAAG;AAChC,UAAM,aAAa,QAAQ,EAAE,KAAK,EAAE,GAAG;AACvC,WAAO,eAAe,IAAI,aAAa,QAAQ,EAAE,OAAO,EAAE,KAAK;AAAA,EACjE;AAEA,WAAS,aAAa,SAAS,OAAO,MAAM;AAC1C,WAAO,QAAQ;AACf,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAAA,IACzC,WAAW,OAAO,GAAG;AACnB,aAAO,QAAQ,MAAM,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAEA,WAAS,WAAW,KAAK;AACvB,UAAM,MAAM,IAAI;AAGhB,UAAM,QAAS,OAAO,OAAO,QAAQ,YAAY,IAAI,OAAQ,IAAI;AACjE,WAAO;AAAA,EACT;AAEA,WAAS,8BAA8BH,MAAK;AAC1C,eAAW,OAAOA,KAAI,MAAM;AAC1B,YAAM,OAAO,IAAI,OAAO,IAAI,IAAI;AAChC,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,iBAAW,YAAY,OAAO,KAAK,IAAI,GAAG;AACxC,cAAM,MAAM,KAAK,QAAQ;AACzB,aAAK,QAAQ,EAAE,OAAO,aAA2B,IAAI,MAAM,IAAI,YAAY;AAAA,MAC7E;AAAA,IACF;AAAA,EACF;AAEA,WAAS,uBAAuB,MAAM;AACpC,WAAO,SAAUA,MAAK;AACpB,UAAI,KAAK,gBAAgB,KAAK,eAAe,KAAK,QAAQ;AACxD,sCAA8BA,IAAG;AAAA,MACnC;AACA,aAAOA;AAAA,IACT;AAAA,EACF;AAEA,WAAS,aAAa,WAAW,MAAM,QAAQ,QAAQ;AAErD,QAAI,MAAM,KAAK,SAAS;AACxB,QAAI,OAAO,QAAQ,aAAa;AAC9B,UAAI,QAAQ;AACV,cAAM,mBAAmB,KAAK,UAAU,GAAG,CAAC;AAAA,MAC9C;AACA,aAAO,KAAK,YAAY,MAAM,GAAG;AAAA,IACnC;AAAA,EACF;AAEA,WAAS,cAAc,kBAAkB;AACvC,QAAI,OAAO,qBAAqB,aAAa;AAC3C,YAAM,WAAW,OAAO,gBAAgB;AAExC,UAAI,CAAC,MAAM,QAAQ,KAAK,aAAa,SAAS,kBAAkB,EAAE,GAAG;AACnE,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,WAAS,cAAc,MAAM;AAC3B,SAAK,cAAc,cAAc,KAAK,WAAW;AACjD,SAAK,QAAQ,cAAc,KAAK,KAAK;AACrC,SAAK,OAAO,cAAc,KAAK,IAAI;AACnC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,QAAQ;AACV,UAAI,OAAO,WAAW,UAAU;AAC9B,eAAQ,IAAI,gBAAgB,+BAA+B,MAAM,GAAG;AAAA,MACtE;AACA,UAAI,SAAS,GAAG;AACd,eAAO,IAAI,gBAAgB,wCAAwC,MAAM,GAAG;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AAEA,WAAS,qBAAqB,SAAS,KAAK;AAC1C,UAAM,eAAe,QAAQ,aAAa,WAAW;AACrD,UAAM,aAAa,QAAQ,aAAa,aAAa;AAErD,QAAI,OAAO,QAAQ,YAAY,MAAM,eACnC,OAAO,QAAQ,UAAU,MAAM,eAC/B,QAAQ,QAAQ,YAAY,GAAG,QAAQ,UAAU,CAAC,IAAI,GAAG;AACzD,YAAM,IAAI,gBAAgB,iGACuC;AAAA,IACnE,WAAW,IAAI,UAAU,QAAQ,WAAW,OAAO;AACjD,UAAI,QAAQ,cAAc;AACxB,cAAM,IAAI,gBAAgB,2CAA2C;AAAA,MACvE,WAAW,QAAQ,QAAQ,QAAQ,KAAK,SAAS,KAC/C,CAAC,QAAQ,SAAS,CAAC,QAAQ,aAAa;AACxC,cAAM,IAAI,gBAAgB,2DACT;AAAA,MACnB;AAAA,IACF;AACA,eAAW,cAAc,CAAC,eAAe,SAAS,MAAM,GAAG;AACzD,YAAM,QAAQ,qBAAqB,QAAQ,UAAU,CAAC;AACtD,UAAI,OAAO;AACT,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,WAAe,UAAU,IAAI,KAAK,MAAM;AAAA;AAEtC,UAAI,SAAS,CAAC;AACd,UAAI;AACJ,UAAI,SAAS;AACb,UAAI;AAMJ,mBAAa,UAAU,MAAM,MAAM;AACnC,mBAAa,gBAAgB,MAAM,MAAM;AACzC,mBAAa,eAAe,MAAM,MAAM;AACxC,mBAAa,SAAS,MAAM,MAAM;AAClC,mBAAa,cAAc,MAAM,MAAM;AACvC,mBAAa,SAAS,MAAM,MAAM;AAClC,mBAAa,eAAe,MAAM,MAAM;AACxC,mBAAa,QAAQ,MAAM,MAAM;AACjC,mBAAa,SAAS,MAAM,MAAM;AAClC,mBAAa,aAAa,MAAM,MAAM;AACtC,mBAAa,YAAY,MAAM,QAAQ,IAAI;AAC3C,mBAAa,aAAa,MAAM,QAAQ,IAAI;AAC5C,mBAAa,UAAU,MAAM,QAAQ,IAAI;AACzC,mBAAa,WAAW,MAAM,QAAQ,IAAI;AAC1C,mBAAa,iBAAiB,MAAM,MAAM;AAC1C,mBAAa,OAAO,MAAM,QAAQ,IAAI;AACtC,mBAAa,cAAc,MAAM,MAAM;AAGvC,eAAS,OAAO,KAAK,GAAG;AACxB,eAAS,WAAW,KAAK,KAAK,MAAM;AAIpC,UAAI,OAAO,KAAK,SAAS,aAAa;AACpC,cAAM,iBAAiB;AAIvB,cAAM,eAAe,QAAQ,mBAAmB,KAAK,UAAU,KAAK,IAAI,CAAC,CAAC;AAC1E,YAAI,aAAa,SAAS,OAAO,SAAS,KAAK,gBAAgB;AAG7D,qBAAW,OAAO,CAAC,MAAM,MAAM,MAAM,OAAO;AAAA,QAC9C,OAAO;AACL,mBAAS;AACT,cAAI,OAAO,QAAQ,UAAU;AAC3B,mBAAO,EAAC,MAAM,KAAK,KAAI;AAAA,UACzB,OAAO;AACL,gBAAI,OAAO,KAAK;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAGA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,QAAQ,cAAc,GAAG;AAE/B,cAAMI,YAAW,MAAM,GAAG,MAAM,aAAa,MAAM,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI,QAAQ;AAAA,UACrF,SAAS,IAAI,EAAQ,EAAC,gBAAgB,mBAAkB,CAAC;AAAA,UACzD;AAAA,UACA,MAAM,KAAK,UAAU,IAAI;AAAA,QAC3B,CAAC;AACD,aAAKA,UAAS;AAEd,cAAMC,UAAS,MAAMD,UAAS,KAAK;AAEnC,YAAI,CAAC,IAAI;AACP,UAAAC,QAAO,SAASD,UAAS;AACzB,gBAAM,0BAA0BC,OAAM;AAAA,QACxC;AAGA,mBAAW,OAAOA,QAAO,MAAM;AAE7B,cAAI,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI,MAAM,UAAU,wBAAwB;AAC9E,kBAAM,IAAI,MAAM,IAAI,MAAM;AAAA,UAC5B;AAAA,QACF;AAEA,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,kBAAQA,OAAM;AAAA,QAChB,CAAC,EAAE,KAAK,uBAAuB,IAAI,CAAC;AAAA,MACtC;AAGA,aAAO,QAAQ,CAAC;AAChB,iBAAW,OAAO,OAAO,KAAK,GAAG,GAAG;AAClC,YAAI,MAAM,QAAQ,IAAI,GAAG,CAAC,GAAG;AAC3B,eAAK,GAAG,IAAI,IAAI,GAAG;AAAA,QACrB,OAAO;AACL,eAAK,GAAG,IAAI,IAAI,GAAG,EAAE,SAAS;AAAA,QAChC;AAAA,MACF;AAEA,YAAM,WAAW,MAAM,GAAG,MAAM,eAAe,QAAQ;AAAA,QACrD,SAAS,IAAI,EAAQ,EAAC,gBAAgB,mBAAkB,CAAC;AAAA,QACzD,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B,CAAC;AAED,WAAK,SAAS;AAEd,YAAM,SAAS,MAAM,SAAS,KAAK;AACnC,UAAI,CAAC,IAAI;AACP,eAAO,SAAS,SAAS;AACzB,cAAM,0BAA0B,MAAM;AAAA,MACxC;AAEA,aAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,gBAAQ,MAAM;AAAA,MAChB,CAAC,EAAE,KAAK,uBAAuB,IAAI,CAAC;AAAA,IACtC;AAAA;AAKA,WAAS,YAAY,IAAI,KAAK,MAAM;AAClC,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,SAAG,OAAO,KAAK,MAAM,SAAU,KAAKL,MAAK;AACvC,YAAI,KAAK;AACP,iBAAO,OAAO,GAAG;AAAA,QACnB;AACA,gBAAQA,IAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAKA,WAAS,kBAAkB,IAAI;AAC7B,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,SAAG,aAAa,SAAU,KAAKA,MAAK;AAClC,YAAI,KAAK;AACP,iBAAO,OAAO,GAAG;AAAA,QACnB;AACA,gBAAQA,IAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,WAAS,WAAW,OAAO;AACzB,WAAO,SAAU,QAAQ;AAEvB,UAAI,OAAO,WAAW,KAAK;AACzB,eAAO;AAAA,MACT,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAKA,WAAe,iBAAiB,OAAO,MAAM,yBAAyB;AAAA;AACpE,YAAM,YAAY,gBAAgB;AAClC,YAAM,iBAAiB,EAAC,KAAK,WAAW,MAAM,CAAC,EAAC;AAChD,YAAM,UAAU,wBAAwB,IAAI,KAAK;AACjD,YAAM,2BAA2B,QAAQ,CAAC;AAC1C,YAAM,UAAU,QAAQ,CAAC;AAEzB,eAAS,aAAa;AACpB,YAAI,SAAS,OAAO,GAAG;AAGrB,iBAAO,QAAQ,QAAQ,cAAc;AAAA,QACvC;AACA,eAAO,KAAK,GAAG,IAAI,SAAS,EAAE,MAAM,WAAW,cAAc,CAAC;AAAA,MAChE;AAEA,eAAS,gBAAgBM,UAAS;AAChC,YAAI,CAACA,SAAQ,KAAK,QAAQ;AAExB,iBAAO,QAAQ,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC;AAAA,QACnC;AACA,eAAO,KAAK,GAAG,QAAQ;AAAA,UACrB,MAAMA,SAAQ;AAAA,UACd,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAEA,eAAS,oBAAoBA,UAAS,WAAW;AAC/C,cAAM,SAAS,CAAC;AAChB,cAAM,UAAU,oBAAI,IAAI;AAExB,mBAAW,OAAO,UAAU,MAAM;AAChC,gBAAM,MAAM,IAAI;AAChB,cAAI,CAAC,KAAK;AACR;AAAA,UACF;AACA,iBAAO,KAAK,GAAG;AACf,kBAAQ,IAAI,IAAI,GAAG;AACnB,cAAI,WAAW,CAAC,yBAAyB,IAAI,IAAI,GAAG;AACpD,cAAI,CAAC,IAAI,UAAU;AACjB,kBAAM,WAAW,yBAAyB,IAAI,IAAI,GAAG;AACrD,gBAAI,WAAW,UAAU;AACvB,kBAAI,QAAQ,SAAS;AAAA,YACvB;AAAA,UACF;AAAA,QACF;AACA,cAAM,UAAU,eAAe,wBAAwB;AACvD,mBAAW,OAAO,SAAS;AACzB,cAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AAErB,kBAAM,QAAQ;AAAA,cACZ,KAAK;AAAA,YACP;AACA,kBAAM,WAAW,yBAAyB,IAAI,GAAG;AACjD,gBAAI,WAAW,UAAU;AACvB,oBAAM,QAAQ,SAAS;AAAA,YACzB;AACA,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AACA,QAAAA,SAAQ,OAAO,KAAK,QAAQ,OAAOA,SAAQ,IAAI,CAAC;AAChD,eAAO,KAAKA,QAAO;AAEnB,eAAO;AAAA,MACT;AAEA,YAAM,UAAU,MAAM,WAAW;AACjC,YAAM,eAAe,MAAM,gBAAgB,OAAO;AAClD,aAAO,oBAAoB,SAAS,YAAY;AAAA,IAClD;AAAA;AAEA,WAAS,eAAe,MAAM;AAG5B,WAAO,KAAK,SAAS,IAAI,eAAe,EAAE,KAAK,SAAUN,MAAK;AAC5D,YAAM,WAAWA,KAAI;AACrB,aAAO,KAAK,GAAG,IAAI,iBAAiB,EAAE,KAAK,SAAUA,MAAK;AACxD,eAAOA,KAAI;AAAA,MACb,CAAC,EACA,MAAM,WAAW,MAAS,CAAC,EAC3B,KAAK,SAAU,KAAK;AACnB,eAAO,KAAK,GAAG,IAAI;AAAA,UACjB,KAAK;AAAA,UACL,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,UAAI,IAAI,WAAW,KAAK;AACtB,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAIA,WAAS,cAAc,MAAM,yBAAyB,KAAK;AACzD,QAAI,WAAW;AACf,WAAO,KAAK,GAAG,IAAI,QAAQ,EACxB,MAAM,WAAW,EAAC,KAAK,UAAU,KAAK,EAAC,CAAC,CAAC,EACzC,KAAK,SAAU,YAAY;AAC1B,UAAI,SAAS,eAAe,uBAAuB;AACnD,aAAO,QAAQ,IAAI,OAAO,IAAI,SAAU,OAAO;AAC7C,eAAO,iBAAiB,OAAO,MAAM,uBAAuB;AAAA,MAC9D,CAAC,CAAC,EAAE,KAAK,SAAU,qBAAqB;AACtC,YAAI,gBAAgB,oBAAoB,KAAK;AAC7C,mBAAW,MAAM;AACjB,sBAAc,KAAK,UAAU;AAE7B,eAAO,KAAK,GAAG,SAAS,EAAC,MAAO,cAAa,CAAC;AAAA,MAChD,CAAC,EAGE,KAAK,MAAM,eAAe,IAAI,CAAC;AAAA,IACpC,CAAC;AAAA,EACL;AAEA,WAAS,SAAS,MAAM;AACtB,UAAM,WAAW,OAAO,SAAS,WAAW,OAAO,KAAK;AACxD,QAAI,QAAQ,iBAAiB,QAAQ;AACrC,QAAI,CAAC,OAAO;AACV,cAAQ,iBAAiB,QAAQ,IAAI,IAAI,UAAU;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAEA,WAAe,WAAW,MAAM,MAAM;AAAA;AACpC,aAAO,cAAc,SAAS,IAAI,GAAG,WAAY;AAC/C,eAAO,kBAAkB,MAAM,IAAI;AAAA,MACrC,CAAC,EAAE;AAAA,IACL;AAAA;AAEA,WAAe,kBAAkB,MAAM,MAAM;AAAA;AAE3C,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,eAAS,KAAK,KAAK,OAAO;AACxB,cAAM,SAAS,EAAC,IAAI,IAAI,KAAK,KAAK,aAAa,GAAG,EAAC;AAGnD,YAAI,OAAO,UAAU,eAAe,UAAU,MAAM;AAClD,iBAAO,QAAQ,aAAa,KAAK;AAAA,QACnC;AACA,mBAAW,KAAK,MAAM;AAAA,MACxB;AAEA,YAAM,SAASC,QAAO,KAAK,QAAQ,IAAI;AAEvC,UAAI,aAAa,KAAK,OAAO;AAE7B,eAAS,aAAa;AACpB,eAAO,KAAK,SAAS,KAAK,EAAE,KAAK,SAAU,MAAM;AAC/C,mBAAS,KAAK,SAAS,YAAY,IAAI;AAAA,YACrC,MAAM;AAAA,YACN,aAAa,KAAK,aAAa;AAAA,UACjC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,eAAS,cAAc,yBAAyB,KAAK;AACnD,eAAO,WAAY;AACjB,iBAAO,cAAc,MAAM,yBAAyB,GAAG;AAAA,QACzD;AAAA,MACF;AAEA,UAAI,eAAe;AACnB,YAAM,WAAW;AAAA,QACf,MAAM,KAAK;AAAA,QACX;AAAA,MACF;AACA,WAAK,SAAS,KAAK,YAAY,QAAQ;AAEvC,YAAM,QAAQ,IAAI,UAAU;AAE5B,eAAe,mBAAmB;AAAA;AAChC,gBAAM,WAAW,MAAM,KAAK,SAAS,QAAQ;AAAA,YAC3C,aAAa;AAAA,YACb,WAAW;AAAA,YACX,cAAc;AAAA,YACd,OAAO;AAAA,YACP,OAAO;AAAA,YACP,OAAO,KAAK;AAAA,UACd,CAAC;AACD,gBAAM,SAAS,MAAM,gBAAgB;AACrC,iBAAO,aAAa,UAAU,MAAM;AAAA,QACtC;AAAA;AAEA,eAAS,kBAAkB;AACzB,eAAO,KAAK,GAAG,IAAI,iBAAiB,EAAE,KAAK,SAAUD,MAAK;AACxD,iBAAOA,KAAI;AAAA,QACb,CAAC,EACA,MAAM,WAAW,EAAE,CAAC,EACpB,KAAK,SAAU,UAAU;AACxB,iBAAO,KAAK,SAAS,IAAI,eAAe,EAAE,KAAK,SAAUA,MAAK;AAC5D,kBAAM,eAAeA,KAAI,OAAO,OAAO,SAAU,OAAO,OAAO;AAC7D,qBAAO,QAAQ;AAAA,YACjB,CAAC,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK;AAE7B,kBAAM,eAAe,aAAa,OAAO,SAAU,OAAO,OAAO;AAC/D,qBAAO,aAAa,QAAQ,KAAK,MAAM;AAAA,YACzC,CAAC;AAED,mBAAO,QAAQ,IAAI,aAAa,IAAI,SAAU,OAAO;AACnD,qBAAO,KAAK,SAAS,IAAI,KAAK,EAAE,KAAK,SAAUO,MAAK;AAClD,uBAAO,EAAE,OAAO,KAAAA,KAAI;AAAA,cACtB,CAAC,EACA,MAAM,WAAW,EAAE,MAAM,CAAC,CAAC;AAAA,YAC9B,CAAC,CAAC;AAAA,UACJ,CAAC,EACA,MAAM,WAAW,CAAC,CAAC,CAAC;AAAA,QACvB,CAAC;AAAA,MACH;AAEA,eAAS,aAAa,UAAU,QAAQ;AACtC,cAAM,UAAU,SAAS;AACzB,YAAI,CAAC,QAAQ,UAAU,CAAC,OAAO,QAAQ;AACrC;AAAA,QACF;AAEA,mBAAW,SAAS,QAAQ;AAC1B,gBAAM,QAAQ,QAAQ,UAAU,SAAU,QAAQ;AAChD,mBAAO,OAAO,OAAO,MAAM;AAAA,UAC7B,CAAC;AACD,cAAI,QAAQ,GAAG;AAEb,kBAAM,QAAQ;AAAA,cACZ,KAAK,MAAM;AAAA,cACX,KAAK;AAAA,gBACH,KAAK,MAAM;AAAA,gBACX,UAAU;AAAA,cACZ;AAAA,cACA,SAAS,CAAC;AAAA,YACZ;AAEA,gBAAI,MAAM,KAAK;AAEb,oBAAM,MAAM,MAAM;AAClB,oBAAM,QAAQ,KAAK,EAAE,KAAK,MAAM,IAAI,KAAK,CAAC;AAAA,YAC5C;AAEA,oBAAQ,KAAK,KAAK;AAAA,UACpB;AAAA,QACF;AAEA,cAAM,0BAA0B,8BAA8B,OAAO;AAErE,cAAM,IAAI,cAAc,yBAAyB,UAAU,CAAC;AAE5D,uBAAe,eAAe,QAAQ;AACtC,cAAMC,YAAW;AAAA,UACf,MAAM,KAAK;AAAA,UACX,UAAU,SAAS;AAAA,UACnB,eAAe,QAAQ;AAAA,UACvB;AAAA,QACF;AACA,aAAK,SAAS,KAAK,YAAYA,SAAQ;AACvC,aAAK,SAAS,YAAY,OAAO,QAAQ,EAAC,iBAAiB,aAAY,CAAC;AAExE,YAAI,QAAQ,SAAS,KAAK,oBAAoB;AAC5C;AAAA,QACF;AACA,eAAO,iBAAiB;AAAA,MAC1B;AAEA,eAAS,8BAA8B,SAAS;AAC9C,cAAM,0BAA0B,oBAAI,IAAI;AACxC,mBAAW,UAAU,SAAS;AAC5B,cAAI,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK;AAC7B,yBAAa,CAAC;AACd,kBAAM,OAAO;AAEb,gBAAI,CAAC,IAAI,UAAU;AACjB,qBAAO,KAAK,UAAU,QAAQ,GAAG;AAAA,YACnC;AACA,uBAAW,KAAK,kBAAkB;AAElC,kBAAM,2BAA2B,+BAA+B,UAAU;AAC1E,oCAAwB,IAAI,OAAO,IAAI,KAAK;AAAA,cAC1C;AAAA,cACA,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AACA,uBAAa,OAAO;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AAEA,eAAS,+BAA+BC,aAAY;AAClD,cAAM,2BAA2B,oBAAI,IAAI;AACzC,YAAI;AACJ,iBAAS,IAAI,GAAG,MAAMA,YAAW,QAAQ,IAAI,KAAK,KAAK;AACrD,gBAAM,kBAAkBA,YAAW,CAAC;AACpC,gBAAM,aAAa,CAAC,gBAAgB,KAAK,gBAAgB,EAAE;AAC3D,cAAI,IAAI,KAAK,QAAQ,gBAAgB,KAAK,OAAO,MAAM,GAAG;AACxD,uBAAW,KAAK,CAAC;AAAA,UACnB;AACA,mCAAyB,IAAI,kBAAkB,UAAU,GAAG,eAAe;AAC3E,oBAAU,gBAAgB;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAEA,UAAI;AACF,cAAM,WAAW;AACjB,cAAM,iBAAiB;AACvB,cAAM,MAAM,OAAO;AACnB,aAAK,MAAM;AACX,aAAK,SAAS,YAAY,OAAO,MAAM;AAAA,MACzC,SAAS,OAAO;AACd,aAAK,SAAS,YAAY,OAAO,QAAQ,KAAK;AAAA,MAChD;AAAA,IACF;AAAA;AAEA,WAAS,WAAW,MAAM,SAAS,SAAS;AAC1C,QAAI,QAAQ,gBAAgB,GAAG;AAC7B,aAAO,QAAQ;AAAA,IACjB;AAEA,UAAM,cAAc,QAAQ,SAAS,QAAQ;AAC7C,UAAM,YAAYP,SAAQ,KAAK,SAAS;AACxC,UAAM,SAAS,CAAC;AAChB,UAAM,MAAM,MAAM,QAAQ,WAAW,IACjC,OAAO,oBACP,QAAQ;AAEZ,eAAW,UAAU,SAAS;AAC5B,YAAM,OAAO,OAAO,OAAO,SAAS,CAAC;AACrC,UAAI,WAAW,cAAc,OAAO,MAAM;AAG1C,UAAI,eAAe,MAAM,QAAQ,QAAQ,GAAG;AAC1C,mBAAW,SAAS,MAAM,GAAG,GAAG;AAAA,MAClC;AAEA,UAAI,QAAQ,QAAQ,KAAK,UAAU,QAAQ,MAAM,GAAG;AAClD,aAAK,KAAK,KAAK,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AACtC,aAAK,OAAO,KAAK,OAAO,KAAK;AAC7B;AAAA,MACF;AACA,aAAO,KAAK;AAAA,QACV,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AAAA,QAC9B,QAAQ,CAAC,OAAO,KAAK;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,cAAU,CAAC;AACX,eAAW,SAAS,QAAQ;AAC1B,YAAM,YAAY,UAAU,KAAK,UAAU,WAAW,MAAM,MAAM,MAAM,QAAQ,KAAK;AACrF,UAAI,UAAU,SAAS,UAAU,iBAAiB,cAAc;AAE9D,cAAM,UAAU;AAAA,MAClB;AACA,cAAQ,KAAK;AAAA;AAAA,QAEX,OAAO,UAAU,QAAQ,OAAO,UAAU;AAAA,QAC1C,KAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH;AAEA,WAAO,EAAE,MAAM,aAAa,SAAS,QAAQ,OAAO,QAAQ,IAAI,EAAE;AAAA,EACpE;AAEA,WAAS,UAAU,MAAM,MAAM;AAC7B,WAAO,cAAc,SAAS,IAAI,GAAG,WAAY;AAC/C,aAAO,iBAAiB,MAAM,IAAI;AAAA,IACpC,CAAC,EAAE;AAAA,EACL;AAEA,WAAe,iBAAiB,MAAM,MAAM;AAAA;AAC1C,UAAI;AACJ,YAAM,eAAe,KAAK,aAAa,KAAK,WAAW;AACvD,YAAM,OAAO,KAAK,QAAQ;AAC1B,UAAI,OAAO,KAAK,SAAS,eAAe,CAAC,KAAK,KAAK,QAAQ;AAEzD,aAAK,QAAQ;AACb,eAAO,KAAK;AAAA,MACd;AAEA,eAAe,cAAc,UAAU;AAAA;AACrC,mBAAS,eAAe;AACxB,gBAAMF,OAAM,MAAM,KAAK,GAAG,QAAQ,QAAQ;AAC1C,sBAAYA,KAAI;AAEhB,iBAAOA,KAAI,KAAK,IAAI,SAAU,QAAQ;AAKpC,gBAAI,WAAW,OAAO,OAAO,OAAO,OAAO,IAAI,UAAU,YACvD,OAAO,IAAI,UAAU,MAAM;AAC3B,oBAAM,OAAO,OAAO,KAAK,OAAO,IAAI,KAAK,EAAE,KAAK;AAGhD,oBAAM,eAAe,CAAC,MAAM,OAAO,OAAO;AAC1C,kBAAI,EAAE,OAAO,gBAAgB,OAAO,eAAe;AACjD,uBAAO,OAAO,IAAI;AAAA,cACpB;AAAA,YACF;AAEA,kBAAM,oBAAoB,qBAAqB,OAAO,IAAI,GAAG;AAC7D,mBAAO;AAAA,cACL,KAAK,kBAAkB,CAAC;AAAA,cACxB,IAAI,kBAAkB,CAAC;AAAA,cACvB,OAAQ,WAAW,OAAO,MAAM,OAAO,IAAI,QAAQ;AAAA,YACrD;AAAA,UACF,CAAC;AAAA,QACH;AAAA;AAEA,eAAe,kBAAkB,MAAM;AAAA;AACrC,cAAI;AACJ,cAAI,cAAc;AAChB,2BAAe,WAAW,MAAM,MAAM,IAAI;AAAA,UAC5C,WAAW,OAAO,KAAK,SAAS,aAAa;AAC3C,2BAAe;AAAA,cACb,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR;AAAA,YACF;AAAA,UACF,OAAO;AAEL,2BAAe;AAAA,cACb,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,MAAM,aAAa,MAAK,KAAK,OAAM,KAAK,IAAI;AAAA,YAC9C;AAAA,UACF;AAEA,cAAI,KAAK,YAAY;AACnB,yBAAa,aAAa,KAAK;AAAA,UACjC;AACA,cAAI,KAAK,cAAc;AACrB,kBAAM,SAAS,KAAK,KAAK,IAAI,UAAU,CAAC;AAExC,kBAAM,aAAa,MAAM,KAAK,SAAS,QAAQ;AAAA,cAC7C,MAAM;AAAA,cACN,cAAc;AAAA,cACd,WAAW,KAAK;AAAA,cAChB,aAAa,KAAK;AAAA,cAClB,QAAQ,KAAK;AAAA,YACf,CAAC;AACD,kBAAM,eAAe,oBAAI,IAAI;AAC7B,uBAAW,OAAO,WAAW,MAAM;AACjC,2BAAa,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA,YAClC;AACA,uBAAW,OAAO,MAAM;AACtB,oBAAM,QAAQ,WAAW,GAAG;AAC5B,oBAAM,MAAM,aAAa,IAAI,KAAK;AAClC,kBAAI,KAAK;AACP,oBAAI,MAAM;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA;AAEA,UAAI,OAAO,KAAK,SAAS,aAAa;AACpC,cAAM,OAAO,KAAK;AAClB,cAAM,gBAAgB,KAAK,IAAI,SAAU,KAAK;AAC5C,gBAAM,WAAW;AAAA,YACf,UAAW,kBAAkB,CAAC,GAAG,CAAC;AAAA,YAClC,QAAW,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AAAA,UACxC;AAEA,cAAI,KAAK,YAAY;AACnB,qBAAS,aAAa;AAAA,UACxB;AACA,iBAAO,cAAc,QAAQ;AAAA,QAC/B,CAAC;AACD,cAAM,SAAS,MAAM,QAAQ,IAAI,aAAa;AAC9C,cAAM,kBAAkB,OAAO,KAAK;AACpC,eAAO,kBAAkB,eAAe;AAAA,MAC1C,OAAO;AACL,cAAM,WAAW;AAAA,UACf,YAAa,KAAK;AAAA,QACpB;AAEA,YAAI,KAAK,YAAY;AACnB,mBAAS,aAAa;AAAA,QACxB;AACA,YAAI;AACJ,YAAI;AACJ,YAAI,eAAe,MAAM;AACvB,qBAAW,KAAK;AAAA,QAClB;AACA,YAAI,cAAc,MAAM;AACtB,qBAAW,KAAK;AAAA,QAClB;AACA,YAAI,aAAa,MAAM;AACrB,mBAAS,KAAK;AAAA,QAChB;AACA,YAAI,YAAY,MAAM;AACpB,mBAAS,KAAK;AAAA,QAChB;AACA,YAAI,OAAO,aAAa,aAAa;AACnC,mBAAS,WAAW,KAAK,aACvB,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,IAChC,kBAAkB,CAAC,QAAQ,CAAC;AAAA,QAChC;AACA,YAAI,OAAO,WAAW,aAAa;AACjC,cAAI,eAAe,KAAK,kBAAkB;AAC1C,cAAI,KAAK,YAAY;AACnB,2BAAe,CAAC;AAAA,UAClB;AAEA,mBAAS,SAAS;AAAA,YAChB,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM;AAAA,UAAC;AAAA,QAC1C;AACA,YAAI,OAAO,KAAK,QAAQ,aAAa;AACnC,gBAAM,WAAW,kBAAkB,CAAC,KAAK,GAAG,CAAC;AAC7C,gBAAM,SAAS,kBAAkB,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAC/C,cAAI,SAAS,YAAY;AACvB,qBAAS,SAAS;AAClB,qBAAS,WAAW;AAAA,UACtB,OAAO;AACL,qBAAS,WAAW;AACpB,qBAAS,SAAS;AAAA,UACpB;AAAA,QACF;AACA,YAAI,CAAC,cAAc;AACjB,cAAI,OAAO,KAAK,UAAU,UAAU;AAClC,qBAAS,QAAQ,KAAK;AAAA,UACxB;AACA,mBAAS,OAAO;AAAA,QAClB;AAEA,cAAM,SAAS,MAAM,cAAc,QAAQ;AAC3C,eAAO,kBAAkB,MAAM;AAAA,MACjC;AAAA,IACF;AAAA;AAEA,WAAe,gBAAgB,IAAI;AAAA;AACjC,YAAM,WAAW,MAAM,GAAG,MAAM,iBAAiB;AAAA,QAC/C,SAAS,IAAI,EAAQ,EAAC,gBAAgB,mBAAkB,CAAC;AAAA,QACzD,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA;AAEA,WAAe,iBAAiB,IAAI;AAAA;AAClC,UAAI;AACF,cAAM,UAAU,MAAM,GAAG,IAAI,YAAY,YAAY;AACrD,cAAM,cAAc,oBAAI,IAAI;AAE5B,mBAAW,gBAAgB,OAAO,KAAK,QAAQ,KAAK,GAAG;AACrD,gBAAM,QAAQ,cAAc,YAAY;AACxC,gBAAM,gBAAgB,aAAa,MAAM,CAAC;AAC1C,gBAAM,WAAW,MAAM,CAAC;AACxB,cAAI,QAAQ,YAAY,IAAI,aAAa;AACzC,cAAI,CAAC,OAAO;AACV,oBAAQ,oBAAI,IAAI;AAChB,wBAAY,IAAI,eAAe,KAAK;AAAA,UACtC;AACA,gBAAM,IAAI,QAAQ;AAAA,QACpB;AACA,cAAM,OAAO;AAAA,UACX,MAAO,eAAe,WAAW;AAAA,UACjC,cAAe;AAAA,QACjB;AAEA,cAAMA,OAAM,MAAM,GAAG,QAAQ,IAAI;AACjC,cAAM,gBAAgB,CAAC;AACvB,mBAAW,OAAOA,KAAI,MAAM;AAC1B,gBAAM,WAAW,IAAI,IAAI,UAAU,CAAC;AACpC,qBAAW,YAAY,YAAY,IAAI,IAAI,GAAG,GAAG;AAC/C,gBAAI,eAAe,WAAW,MAAM;AAEpC,gBAAI,CAAC,QAAQ,MAAM,YAAY,GAAG;AAGhC,6BAAe;AAAA,YACjB;AACA,kBAAM,cAAc,OAAO,KAAK,QAAQ,MAAM,YAAY,CAAC;AAE3D,kBAAM,eAAe,IAAI,OAAO,IAAI,IAAI,SACtC,IAAI,IAAI,MAAM,QAAQ;AACxB,uBAAW,cAAc,aAAa;AACpC,4BAAc,UAAU,IAAI,cAAc,UAAU,KAAK;AAAA,YAC3D;AAAA,UACF;AAAA,QACF;AAEA,cAAM,cAAc,OAAO,KAAK,aAAa,EAC1C,OAAO,SAAU,YAAY;AAAE,iBAAO,CAAC,cAAc,UAAU;AAAA,QAAG,CAAC;AAEtE,cAAM,kBAAkB,YAAY,IAAI,SAAU,YAAY;AAC5D,iBAAO,cAAc,SAAS,UAAU,GAAG,WAAY;AACrD,mBAAO,IAAI,GAAG,YAAY,YAAY,GAAG,MAAM,EAAE,QAAQ;AAAA,UAC3D,CAAC,EAAE;AAAA,QACL,CAAC;AAED,eAAO,QAAQ,IAAI,eAAe,EAAE,KAAK,WAAY;AACnD,iBAAO,EAAC,IAAI,KAAI;AAAA,QAClB,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,YAAI,IAAI,WAAW,KAAK;AACtB,iBAAO,EAAC,IAAI,KAAI;AAAA,QAClB,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA;AAEA,WAAe,cAAc,IAAI,KAAK,MAAM;AAAA;AAE1C,UAAI,OAAO,GAAG,WAAW,YAAY;AACnC,eAAO,YAAY,IAAI,KAAK,IAAI;AAAA,MAClC;AACA,UAAI,SAAS,EAAE,GAAG;AAChB,eAAO,UAAU,IAAI,KAAK,IAAI;AAAA,MAChC;AAEA,YAAM,iBAAiB;AAAA,QACrB,oBAAoB,GAAG,OAAO,kCAAkC;AAAA,MAClE;AAEA,UAAI,OAAO,QAAQ,UAAU;AAE3B,6BAAqB,MAAM,GAAG;AAE9B,sBAAc,IAAI,WAAkB;AAAA;AAClC,kBAAM,OAAO,MAAM;AAAA;AAAA,cACF;AAAA;AAAA,cACA;AAAA;AAAA,cACF,IAAI;AAAA;AAAA,cACD,IAAI;AAAA;AAAA,cACJ;AAAA;AAAA,cACG;AAAA,YAAY;AAEjC,mBAAO;AAAA,cAAI,WAAW,MAAM,cAAc,EAAE;AAAA,gBAC1C,WAAY;AAAE,yBAAO,UAAU,MAAM,IAAI;AAAA,gBAAG;AAAA,cAAC;AAAA,cAC7C,WAAY;AAAE,uBAAO,KAAK,GAAG,QAAQ;AAAA,cAAG;AAAA,YAC1C;AAAA,UACF;AAAA,SAAC;AACD,eAAO,cAAc,OAAO;AAAA,MAC9B,OAAO;AAEL,cAAM,eAAe;AACrB,cAAM,QAAQ,cAAc,YAAY;AACxC,cAAM,gBAAgB,MAAM,CAAC;AAC7B,cAAM,WAAW,MAAM,CAAC;AAExB,cAAM,MAAM,MAAM,GAAG,IAAI,aAAa,aAAa;AACnD,cAAM,IAAI,SAAS,IAAI,MAAM,QAAQ;AAErC,YAAI,CAAC,KAAK;AAER,gBAAM,IAAI,cAAc,QAAQ,IAAI,GAAG,sBAAsB,QAAQ,EAAE;AAAA,QACzE;AAEA,QAAAG,eAAc,KAAK,QAAQ;AAC3B,6BAAqB,MAAM,GAAG;AAE9B,cAAM,OAAO,MAAM;AAAA;AAAA,UACF;AAAA;AAAA,UACA;AAAA;AAAA,UACF,IAAI;AAAA;AAAA,UACD,IAAI;AAAA;AAAA,UACJ;AAAA;AAAA,UACG;AAAA,QAAY;AAEjC,YAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,gBAAgB;AACxD,cAAI,KAAK,UAAU,gBAAgB;AACjC,qBAAS,WAAY;AACnB,yBAAW,MAAM,cAAc;AAAA,YACjC,CAAC;AAAA,UACH;AACA,iBAAO,UAAU,MAAM,IAAI;AAAA,QAC7B,OAAO;AACL,gBAAM,WAAW,MAAM,cAAc;AACrC,iBAAO,UAAU,MAAM,IAAI;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA;AAEA,WAAS,cAAc,KAAK,MAAM,UAAU;AAC1C,UAAM,KAAK;AACX,QAAI,OAAO,SAAS,YAAY;AAC9B,iBAAW;AACX,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,cAAc,IAAI,IAAI,CAAC;AAErC,QAAI,OAAO,QAAQ,YAAY;AAC7B,YAAM,EAAC,KAAM,IAAG;AAAA,IAClB;AAEA,UAAM,UAAU,QAAQ,QAAQ,EAAE,KAAK,WAAY;AACjD,aAAO,cAAc,IAAI,KAAK,IAAI;AAAA,IACpC,CAAC;AACD,qBAAiB,SAAS,QAAQ;AAClC,WAAO;AAAA,EACT;AAEA,QAAM,sBAAsB,YAAY,WAAY;AAClD,UAAM,KAAK;AAEX,QAAI,OAAO,GAAG,iBAAiB,YAAY;AACzC,aAAO,kBAAkB,EAAE;AAAA,IAC7B;AACA,QAAI,SAAS,EAAE,GAAG;AAChB,aAAO,gBAAgB,EAAE;AAAA,IAC3B;AACA,WAAO,iBAAiB,EAAE;AAAA,EAC5B,CAAC;AAED,SAAO;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AACF;AAEA,IAAO,mBAAQ;;;AChqCf,SAAS,gBAAgB,KAAK,aAAa;AACzC,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,QAAI,MAAM,YAAY,CAAC;AACvB,YAAQ,MAAM,GAAG;AACjB,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,KAAK,aAAa,OAAO;AAC9C,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,MAAI,GAAG,KAAK;AACxD,QAAI,OAAO,YAAY,CAAC;AACxB,UAAM,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AAAA,EAClC;AACA,MAAI,YAAY,MAAI,CAAC,CAAC,IAAI;AAC5B;AAEA,SAAS,QAAQ,MAAM,OAAO;AAC5B,SAAO,OAAO,QAAQ,KAAK,OAAO,QAAQ,IAAI;AAChD;AAGA,SAAS,WAAW,WAAW;AAE7B,MAAI,SAAS,CAAC;AACd,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,KAAK;AACpD,QAAI,KAAK,UAAU,CAAC;AACpB,QAAI,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,SAAS,OAAO,OAAO,OAAO,MAAM;AAEpE,gBAAU,QAAQ,UAAU,GAAG,QAAQ,SAAS,CAAC,IAAI;AAAA,IACvD,WAAW,OAAO,KAAK;AAErB,aAAO,KAAK,OAAO;AACnB,gBAAU;AAAA,IACZ,OAAO;AACL,iBAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO,KAAK,OAAO;AACnB,SAAO;AACT;AAEA,IAAI,oBAAoB,CAAC,OAAO,QAAQ,MAAM;AAC9C,SAAS,qBAAqB,OAAO;AACnC,SAAO,kBAAkB,QAAQ,KAAK,IAAI;AAC5C;AAEA,SAAS,OAAO,KAAK;AACnB,SAAO,OAAO,KAAK,GAAG,EAAE,CAAC;AAC3B;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,IAAI,OAAO,GAAG,CAAC;AACxB;AAIA,SAAS,oBAAoB,WAAW;AAKtC,MAAIO,OAAM,CAAC;AACX,MAAI,QAAQ,EAAC,KAAK,MAAM,MAAM,KAAI;AAElC,YAAU,QAAQ,SAAU,UAAU;AACpC,WAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,OAAO;AAC7C,UAAI,UAAU,SAAS,KAAK;AAC5B,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,EAAC,KAAK,QAAO;AAAA,MACzB;AAEA,UAAI,qBAAqB,KAAK,GAAG;AAE/B,YAAI,mBAAmB,OAAO;AAC5B,cAAI,MAAM,KAAK,GAAG;AAChB,kBAAM,KAAK,IAAI;AACf,YAAAA,KAAI,KAAK,IAAI;AACb;AAAA,UACF;AAEA,cAAI,UAAU,CAAC;AACf,UAAAA,KAAI,KAAK,EAAE,QAAQ,SAAU,UAAU;AACrC,mBAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,KAAK;AAC1C,kBAAI,IAAI,QAAQ,GAAG;AACnB,kBAAI,UAAU,KAAK,IAAI,OAAO,KAAK,QAAQ,EAAE,QAAQ,OAAO,KAAK,CAAC,EAAE,MAAM;AAC1E,kBAAI,SAAS,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAC9C,kBAAI,OAAO,KAAK,MAAM,EAAE,UAAU,SAAS;AAIzC;AAAA,cACF;AACA,sBAAQ,KAAK,MAAM;AAAA,YACrB,CAAC;AAAA,UACH,CAAC;AACD,UAAAA,KAAI,KAAK,IAAI;AAAA,QACf,OAAO;AAEL,UAAAA,KAAI,KAAK,IAAI,oBAAoB,CAAC,OAAO,CAAC;AAAA,QAC5C;AAAA,MACF,OAAO;AACL,YAAI,gBAAgBA,KAAI,KAAK,IAAIA,KAAI,KAAK,KAAK,CAAC;AAChD,eAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,UAAU;AAC/C,cAAI,QAAQ,QAAQ,QAAQ;AAE5B,cAAI,aAAa,SAAS,aAAa,QAAQ;AAC7C,mBAAO,WAAW,UAAU,OAAO,aAAa;AAAA,UAClD,WAAW,aAAa,SAAS,aAAa,QAAQ;AACpD,mBAAO,WAAW,UAAU,OAAO,aAAa;AAAA,UAClD,WAAW,aAAa,OAAO;AAC7B,mBAAO,QAAQ,OAAO,aAAa;AAAA,UACrC,WAAW,aAAa,OAAO;AAC7B,mBAAO,QAAQ,OAAO,aAAa;AAAA,UACrC,WAAW,aAAa,UAAU;AAChC,mBAAO,WAAW,OAAO,aAAa;AAAA,UACxC;AACA,wBAAc,QAAQ,IAAI;AAAA,QAC5B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,SAAOA;AACT;AAKA,SAAS,WAAW,UAAU,OAAO,eAAe;AAClD,MAAI,OAAO,cAAc,QAAQ,aAAa;AAC5C;AAAA,EACF;AACA,MAAI,OAAO,cAAc,SAAS,aAAa;AAC7C,QAAI,aAAa,QAAQ;AACvB,UAAI,QAAQ,cAAc,MAAM;AAC9B,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,SAAS,cAAc,MAAM;AAC/B,eAAO,cAAc;AACrB,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF,WAAW,OAAO,cAAc,QAAQ,aAAa;AACnD,QAAI,aAAa,QAAQ;AACvB,UAAI,QAAQ,cAAc,KAAK;AAC7B,eAAO,cAAc;AACrB,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,QAAQ,cAAc,KAAK;AAC7B,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF,OAAO;AACL,kBAAc,QAAQ,IAAI;AAAA,EAC5B;AACF;AAGA,SAAS,WAAW,UAAU,OAAO,eAAe;AAClD,MAAI,OAAO,cAAc,QAAQ,aAAa;AAC5C;AAAA,EACF;AACA,MAAI,OAAO,cAAc,SAAS,aAAa;AAC7C,QAAI,aAAa,QAAQ;AACvB,UAAI,QAAQ,cAAc,MAAM;AAC9B,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,SAAS,cAAc,MAAM;AAC/B,eAAO,cAAc;AACrB,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF,WAAW,OAAO,cAAc,QAAQ,aAAa;AACnD,QAAI,aAAa,QAAQ;AACvB,UAAI,QAAQ,cAAc,KAAK;AAC7B,eAAO,cAAc;AACrB,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,QAAQ,cAAc,KAAK;AAC7B,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF,OAAO;AACL,kBAAc,QAAQ,IAAI;AAAA,EAC5B;AACF;AAGA,SAAS,QAAQ,OAAO,eAAe;AACrC,MAAI,SAAS,eAAe;AAE1B,kBAAc,IAAI,KAAK,KAAK;AAAA,EAC9B,OAAO;AACL,kBAAc,MAAM,CAAC,KAAK;AAAA,EAC5B;AACF;AAGA,SAAS,QAAQ,OAAO,eAAe;AAGrC,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,gBAAc,MAAM;AACtB;AAGA,SAAS,WAAW,OAAO,eAAe;AACxC,MAAI,YAAY,eAAe;AAE7B,kBAAc,OAAO,KAAK,KAAK;AAAA,EACjC,OAAO;AACL,kBAAc,SAAS,CAAC,KAAK;AAAA,EAC/B;AACF;AAGA,SAAS,0BAA0B,KAAK;AACpC,WAAS,QAAQ,KAAK;AAClB,QAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,eAAS,KAAK,KAAK;AACf,YAAI,IAAI,CAAC,EAAE,MAAM,GAAG;AAChB,cAAI,CAAC,IAAI,oBAAoB,IAAI,CAAC,EAAE,MAAM,CAAC;AAAA,QAC/C;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,QAAQ,IAAI,IAAI;AACpB,QAAI,OAAO,UAAU,UAAU;AAC3B,gCAA0B,KAAK;AAAA,IACnC;AAAA,EACJ;AACA,SAAO;AACX;AAGA,SAAS,gBAAgB,KAAK,OAAO;AACjC,WAAS,QAAQ,KAAK;AAClB,QAAI,SAAS,QAAQ;AACjB,cAAQ;AAAA,IACZ;AACA,QAAI,QAAQ,IAAI,IAAI;AACpB,QAAI,OAAO,UAAU,UAAU;AAC3B,cAAQ,gBAAgB,OAAO,KAAK;AAAA,IACxC;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,SAAS,MAAM,KAAK;AAGxB,MAAI,gBAAgB,QAAQ,KAAK,GAAG;AAClC,aAAS,0BAA0B,MAAM;AACzC,QAAI,UAAU,QAAQ;AACpB,eAAS,oBAAoB,OAAO,MAAM,CAAC;AAAA,IAC7C;AAAA,EACF;AAEA,GAAC,OAAO,MAAM,EAAE,QAAQ,SAAU,SAAS;AACzC,QAAI,WAAW,QAAQ;AAGrB,aAAO,OAAO,EAAE,QAAQ,SAAU,aAAa;AAC7C,YAAIC,UAAS,OAAO,KAAK,WAAW;AACpC,iBAASC,KAAI,GAAGA,KAAID,QAAO,QAAQC,MAAK;AACtC,cAAIC,SAAQF,QAAOC,EAAC;AACpB,cAAIE,WAAU,YAAYD,MAAK;AAC/B,cAAI,OAAOC,aAAY,YAAYA,aAAY,MAAM;AACnD,wBAAYD,MAAK,IAAI,EAAC,KAAKC,SAAO;AAAA,UACpC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,MAAI,UAAU,QAAQ;AAGpB,WAAO,MAAM,IAAI,oBAAoB,CAAC,OAAO,MAAM,CAAC,CAAC;AAAA,EACvD;AAEA,MAAI,SAAS,OAAO,KAAK,MAAM;AAE/B,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,QAAQ,OAAO,CAAC;AACpB,QAAI,UAAU,OAAO,KAAK;AAE1B,QAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACnD,gBAAU,EAAC,KAAK,QAAO;AAAA,IACzB;AACA,WAAO,KAAK,IAAI;AAAA,EAClB;AAEA,0BAAwB,MAAM;AAE9B,SAAO;AACT;AAMA,SAAS,wBAAwB,UAAU;AACzC,SAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,OAAO;AAC7C,QAAI,UAAU,SAAS,KAAK;AAE5B,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAQ,QAAQ,SAAU,aAAa;AACrC,YAAI,eAAe,OAAO,gBAAgB,UAAU;AAClD,kCAAwB,WAAW;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH,WAAW,UAAU,OAAO;AAC1B,eAAS,MAAM,CAAC,OAAO;AAAA,IACzB,WAAW,UAAU,UAAU;AAC7B,eAAS,SAAS,CAAC,OAAO;AAAA,IAC5B,WAAW,WAAW,OAAO,YAAY,UAAU;AACjD,8BAAwB,OAAO;AAAA,IACjC;AAAA,EACF,CAAC;AACH;AAGA,SAAS,kBAAkB,MAAM;AAE/B,WAAS,sBAAsB,KAAK;AAClC,WAAO,KAAK,IAAI,SAAU,SAAS;AACjC,UAAI,YAAY,OAAO,OAAO;AAC9B,UAAI,cAAc,WAAW,SAAS;AACtC,UAAI,gBAAgB,gBAAgB,KAAK,WAAW;AACpD,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,SAAO,SAAU,MAAM,MAAM;AAC3B,QAAI,eAAe,sBAAsB,KAAK,GAAG;AACjD,QAAI,eAAe,sBAAsB,KAAK,GAAG;AACjD,QAAI,YAAY,QAAQ,cAAc,YAAY;AAClD,QAAI,cAAc,GAAG;AACnB,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG;AAAA,EAC3C;AACF;AAEA,SAAS,qBAAqB,MAAM,YAAY,gBAAgB;AAC9D,SAAO,KAAK,OAAO,SAAU,KAAK;AAChC,WAAO,UAAU,IAAI,KAAK,WAAW,UAAU,cAAc;AAAA,EAC/D,CAAC;AAED,MAAI,WAAW,MAAM;AAEnB,QAAI,cAAc,kBAAkB,WAAW,IAAI;AACnD,WAAO,KAAK,KAAK,WAAW;AAC5B,QAAI,OAAO,WAAW,KAAK,CAAC,MAAM,YAC9B,SAAS,WAAW,KAAK,CAAC,CAAC,MAAM,QAAQ;AAC3C,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,EACF;AAEA,MAAI,WAAW,cAAc,UAAU,YAAY;AAEjD,QAAI,OAAO,WAAW,QAAQ;AAC9B,QAAI,SAAS,WAAW,aAAa,WAAW,QAAQ,KAAK,UAAU;AACvE,WAAO,KAAK,MAAM,MAAM,KAAK;AAAA,EAC/B;AACA,SAAO;AACT;AAEA,SAAS,UAAU,KAAK,UAAU,gBAAgB;AAChD,SAAO,eAAe,MAAM,SAAU,OAAO;AAC3C,QAAI,UAAU,SAAS,KAAK;AAC5B,QAAI,cAAc,WAAW,KAAK;AAClC,QAAI,gBAAgB,gBAAgB,KAAK,WAAW;AACpD,QAAI,qBAAqB,KAAK,GAAG;AAC/B,aAAO,0BAA0B,OAAO,SAAS,GAAG;AAAA,IACtD;AAEA,WAAO,cAAc,SAAS,KAAK,aAAa,aAAa;AAAA,EAC/D,CAAC;AACH;AAEA,SAAS,cAAc,SAAS,KAAK,aAAa,eAAe;AAC/D,MAAI,CAAC,SAAS;AAEZ,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO,OAAO,KAAK,OAAO,EAAE,MAAM,SAAU,mBAAmB;AAC7D,UAAI,YAAY,QAAS,iBAAkB;AAE3C,UAAI,kBAAkB,QAAQ,GAAG,MAAM,GAAG;AACxC,eAAO,MAAM,mBAAmB,KAAK,WAAW,aAAa,aAAa;AAAA,MAC5E,OAAO;AACL,YAAI,iBAAiB,WAAW,iBAAiB;AAEjD,YACE,kBAAkB,UAClB,OAAO,cAAc,YACrB,eAAe,SAAS,GACxB;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,gBAAgB,eAAe,cAAc;AAEpE,YAAI,OAAO,cAAc,UAAU;AAEjC,iBAAO,cAAc,WAAW,KAAK,aAAa,gBAAgB;AAAA,QACpE;AAGA,eAAO,MAAM,OAAO,KAAK,WAAW,gBAAgB,gBAAgB;AAAA,MACtE;AAAA,IACF,CAAC;AAAA,EACH;AAGA,SAAO,YAAY;AACrB;AAEA,SAAS,0BAA0B,OAAO,SAAS,KAAK;AAEtD,MAAI,UAAU,OAAO;AACnB,WAAO,QAAQ,KAAK,SAAU,YAAY;AACxC,aAAO,UAAU,KAAK,YAAY,OAAO,KAAK,UAAU,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH;AAEA,MAAI,UAAU,QAAQ;AACpB,WAAO,CAAC,UAAU,KAAK,SAAS,OAAO,KAAK,OAAO,CAAC;AAAA,EACtD;AAGA,SAAO,CAAC,QAAQ,KAAK,SAAU,YAAY;AACzC,WAAO,UAAU,KAAK,YAAY,OAAO,KAAK,UAAU,CAAC;AAAA,EAC3D,CAAC;AAEH;AAEA,SAAS,MAAM,cAAc,KAAK,WAAW,aAAa,eAAe;AACvE,MAAI,CAAC,SAAS,YAAY,GAAG;AAE3B,UAAM,IAAI,MAAM,uBAAuB,eACrC,oIACiE;AAAA,EACrE;AACA,SAAO,SAAS,YAAY,EAAE,KAAK,WAAW,aAAa,aAAa;AAC1E;AAEA,SAAS,YAAY,eAAe;AAClC,SAAO,OAAO,kBAAkB,eAAe,kBAAkB;AACnE;AAEA,SAAS,oBAAoB,eAAe;AAC1C,SAAO,OAAO,kBAAkB;AAClC;AAEA,SAAS,SAAS,eAAe,WAAW;AAC1C,MAAI,OAAO,kBAAkB,YAC3B,SAAS,eAAe,EAAE,MAAM,eAAe;AAC/C,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,UAAU,CAAC;AACzB,MAAI,MAAM,UAAU,CAAC;AAErB,SAAO,gBAAgB,YAAY;AACrC;AAEA,SAAS,mBAAmB,eAAe,WAAW;AACpD,SAAO,UAAU,KAAK,SAAU,KAAK;AACnC,QAAI,yBAAyB,OAAO;AAClC,aAAO,cAAc,KAAK,SAAU,mBAAmB;AACrD,eAAO,QAAQ,KAAK,iBAAiB,MAAM;AAAA,MAC7C,CAAC;AAAA,IACH;AAEA,WAAO,QAAQ,KAAK,aAAa,MAAM;AAAA,EACzC,CAAC;AACH;AAEA,SAAS,uBAAuB,eAAe,WAAW;AACxD,SAAO,UAAU,MAAM,SAAU,KAAK;AACpC,WAAO,cAAc,KAAK,SAAU,mBAAmB;AACrD,aAAO,QAAQ,KAAK,iBAAiB,MAAM;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,UAAU,eAAe,WAAW;AAC3C,SAAO,cAAc,WAAW;AAClC;AAEA,SAAS,WAAW,eAAe,WAAW;AAC5C,MAAI,KAAK,IAAI,OAAO,SAAS;AAE7B,SAAO,GAAG,KAAK,aAAa;AAC9B;AAEA,SAAS,UAAU,eAAe,WAAW;AAE3C,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,aAAO,kBAAkB;AAAA,IAC3B,KAAK;AACH,aAAO,OAAQ,kBAAmB;AAAA,IACpC,KAAK;AACH,aAAO,OAAQ,kBAAmB;AAAA,IACpC,KAAK;AACH,aAAO,OAAQ,kBAAmB;AAAA,IACpC,KAAK;AACH,aAAO,yBAAyB;AAAA,IAClC,KAAK;AACH,aAAQ,CAAC,EAAG,SAAS,KAAK,aAAa,MAAM;AAAA,EACjD;AACF;AAEA,IAAI,WAAW;AAAA,EAEb,cAAc,SAAU,KAAK,WAAW,aAAa,eAAe;AAClE,QAAI,CAAC,MAAM,QAAQ,aAAa,GAAG;AACjC,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,cAAc,CAAC,MAAM,YAAa,cAAc,CAAC,MAAM,MAAM;AACtE,aAAO,cAAc,KAAK,SAAU,KAAK;AACvC,eAAO,UAAU,KAAK,WAAW,OAAO,KAAK,SAAS,CAAC;AAAA,MACzD,CAAC;AAAA,IACH;AAEA,WAAO,cAAc,KAAK,SAAU,KAAK;AACvC,aAAO,cAAc,WAAW,KAAK,aAAa,GAAG;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,aAAa,SAAU,KAAK,WAAW,aAAa,eAAe;AACjE,QAAI,CAAC,MAAM,QAAQ,aAAa,GAAG;AACjC,aAAO;AAAA,IACT;AAGA,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,cAAc,CAAC,MAAM,YAAa,cAAc,CAAC,MAAM,MAAM;AACtE,aAAO,cAAc,MAAM,SAAU,KAAK;AACxC,eAAO,UAAU,KAAK,WAAW,OAAO,KAAK,SAAS,CAAC;AAAA,MACzD,CAAC;AAAA,IACH;AAEA,WAAO,cAAc,MAAM,SAAU,KAAK;AACxC,aAAO,cAAc,WAAW,KAAK,aAAa,GAAG;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,MAAM;AAAA,EACrF;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,KAAK;AAAA,EACpF;AAAA,EAEA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,IAAI;AAAA,EACnF;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,KAAK;AAAA,EACpF;AAAA,EAEA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,IAAI;AAAA,EACnF;AAAA,EAEA,WAAW,SAAU,KAAK,WAAW,aAAa,eAAe;AAE/D,QAAI,WAAW;AACb,aAAO,oBAAoB,aAAa;AAAA,IAC1C;AAEA,WAAO,CAAC,oBAAoB,aAAa;AAAA,EAC3C;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,YAAY,aAAa,KAAK,SAAS,eAAe,SAAS;AAAA,EACxE;AAAA,EAEA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,UAAU,MAAM,SAAU,SAAS;AACxC,aAAO,QAAQ,eAAe,OAAO,MAAM;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,YAAY,aAAa,KAAK,mBAAmB,eAAe,SAAS;AAAA,EAClF;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,YAAY,aAAa,KAAK,CAAC,mBAAmB,eAAe,SAAS;AAAA,EACnF;AAAA,EAEA,SAAS,SAAU,KAAK,WAAW,aAAa,eAAe;AAC7D,WAAO,YAAY,aAAa,KAC9B,MAAM,QAAQ,aAAa,KAC3B,UAAU,eAAe,SAAS;AAAA,EACtC;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,MAAM,QAAQ,aAAa,KAAK,uBAAuB,eAAe,SAAS;AAAA,EACxF;AAAA,EAEA,UAAU,SAAU,KAAK,WAAW,aAAa,eAAe;AAC9D,WAAO,YAAY,aAAa,KAC9B,OAAO,iBAAiB,YACxB,UAAU,MAAM,SAAU,YAAY;AACpC,aAAO,WAAW,eAAe,UAAU;AAAA,IAC7C,CAAC;AAAA,EACL;AAAA,EAEA,SAAS,SAAU,KAAK,WAAW,aAAa,eAAe;AAC7D,WAAO,UAAU,eAAe,SAAS;AAAA,EAC3C;AACF;AAGA,SAAS,gBAAgB,KAAK,UAAU;AAEtC,MAAI,OAAO,aAAa,UAAU;AAEhC,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC1D;AAEA,aAAW,gBAAgB,QAAQ;AACnC,MAAI,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,cAAc,qBAAqB,CAAC,GAAG,GAAG,EAAE,SAAS,GAAG,OAAO,KAAK,QAAQ,CAAC;AACjF,SAAO,eAAe,YAAY,WAAW;AAC/C;;;ACtpBA,IAAM,aAAa,IAAI,SAAS,KAAK,KAAK,QAAQ;AAElD,IAAM,WAAW,IAAI,SAAS;AAC5B,MAAIC,OAAM,CAAC;AACX,aAAW,UAAU,MAAM;AACzB,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,MAAAA,OAAMA,KAAI,OAAO,SAAS,GAAG,MAAM,CAAC;AAAA,IACtC,OAAO;AACL,MAAAA,KAAI,KAAK,MAAM;AAAA,IACjB;AAAA,EACF;AACA,SAAOA;AACT;AAEA,IAAM,UAAU,OAAO,MAAM,UAAU,SAAS,aAC5C,aACA;AAEJ,SAAS,aAAa,KAAK;AACzB,QAAMA,OAAM,CAAC;AACb,aAAW,WAAW,KAAK;AACzB,WAAO,OAAOA,MAAK,OAAO;AAAA,EAC5B;AACA,SAAOA;AACT;AAIA,SAAS,KAAK,KAAK,KAAK;AACtB,QAAMA,OAAM,CAAC;AACb,aAAW,SAAS,KAAK;AACvB,UAAM,cAAc,WAAW,KAAK;AACpC,UAAM,QAAQ,gBAAgB,KAAK,WAAW;AAC9C,QAAI,OAAO,UAAU,aAAa;AAChC,oBAAcA,MAAK,aAAa,KAAK;AAAA,IACvC;AAAA,EACF;AACA,SAAOA;AACT;AAGA,SAAS,0BAA0B,MAAM,OAAO;AAC9C,WAAS,IAAI,GAAG,MAAM,KAAK,IAAI,KAAK,QAAQ,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK;AACvE,QAAI,KAAK,CAAC,MAAM,MAAM,CAAC,GAAG;AACxB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,gCAAgC,MAAM,OAAO;AACpD,MAAI,KAAK,SAAS,MAAM,QAAQ;AAC9B,WAAO;AAAA,EACT;AAEA,SAAO,0BAA0B,MAAM,KAAK;AAC9C;AAIA,SAAS,wBAAwB,MAAM,OAAO;AAC5C,SAAO,KAAK,MAAM;AAClB,aAAW,SAAS,OAAO;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,UAAM,UAAU,KAAK,QAAQ,KAAK;AAClC,QAAI,YAAY,IAAI;AAClB,aAAO;AAAA,IACT,OAAO;AACL,WAAK,OAAO,SAAS,CAAC;AAAA,IACxB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,KAAK;AAC1B,QAAMA,OAAM,CAAC;AACb,aAAW,SAAS,KAAK;AACvB,IAAAA,KAAI,KAAK,IAAI;AAAA,EACf;AACA,SAAOA;AACT;AAEA,SAAS,IAAI,KAAK,KAAK;AACrB,MAAIC,OAAM;AACV,MAAI,WAAW;AACf,aAAW,WAAW,KAAK;AACzB,UAAM,QAAQ,IAAI,OAAO;AACzB,QAAI,QAAQ,UAAU;AACpB,iBAAW;AACX,MAAAA,OAAM;AAAA,IACR;AAAA,EACF;AACA,SAAOA;AACT;AAEA,SAAS,YAAY,MAAM,MAAM;AAC/B,MAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAASC,MAAK,KAAK;AACjB,SAAO,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC;AAChC;AAgBA,SAAS,kBAAkB,KAAK;AAC9B,SAAO,YAAa,MAAM;AACxB,UAAM,gBAAgB,KAAK,KAAK,SAAS,CAAC;AAC1C,QAAI,OAAO,kBAAkB,YAAY;AACvC,YAAM,YAAY,cAAc,KAAK,MAAM,IAAI;AAC/C,YAAM,WAAW,cAAc,KAAK,IAAI;AACxC,UAAI,MAAM,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,WAAW,QAAQ;AAAA,IAC7D,OAAO;AACL,aAAO,IAAI,MAAM,MAAM,IAAI;AAAA,IAC7B;AAAA,EACF;AACF;AAMA,SAAS,0BAA0B,YAAY;AAC7C,eAAa,MAAM,UAAU;AAE7B,MAAI,CAAC,WAAW,OAAO;AACrB,eAAW,QAAQ,CAAC;AAAA,EACtB;AAEA,aAAW,OAAO,CAAC,QAAQ,QAAQ,MAAM,GAAG;AAC1C,QAAI,WAAW,MAAM,GAAG,GAAG;AACzB,iBAAW,GAAG,IAAI,WAAW,MAAM,GAAG;AACtC,aAAO,WAAW,MAAM,GAAG;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,WAAW,QAAQ;AACrB,eAAW,MAAM,SAAS,WAAW;AACrC,WAAO,WAAW;AAAA,EACpB;AAEA,MAAI,CAAC,WAAW,MAAM;AACpB,eAAW,OAAO;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,OAAO,UAAU,YAAY,UAAU;AAChD;AAGA,SAAS,oBAAoB,MAAM,OAAO,QAAQ;AAChD,MAAI,UAAU;AACd,MAAI,WAAW;AACf,MAAI,cAAc;AAClB,MAAI,CAAC,OAAO,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,MAAM,EAAE,QAAQ,IAAI,MAAM,IAAI;AAC/E,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAU,oBAAoB,OAAO;AAAA,IAEvC;AAAA,EACF;AAEA,MAAI,CAAC,QAAQ,cAAc,WAAW,EAAE,QAAQ,IAAI,MAAM,IAAI;AAC5D,QAAI,EAAE,CAAC,MAAM,QAAQ,KAAK,KAAK,gBAAgB,KAAK,IAAI;AACtD,gBAAU,oBAAoB,OAAO;AAAA,IACvC;AAAA,EACF;AAEA,MAAI,SAAS,UAAU,MAAM,QAAQ,KAAK,GAAG;AAC3C,QAAI,MAAM,WAAW,GAAG;AACtB,gBAAU;AAAA,IAEZ,OAAO;AACL,YAAM,UAAU,MAAM,CAAC;AACvB,YAAM,MAAM,MAAM,CAAC;AACnB,UAAI,YAAY,GAAG;AACjB,kBAAU;AACV,sBAAc;AAAA,MAChB;AACA,UAAI,OAAO,YAAY,YAAY,SAAS,SAAS,EAAE,MAAM,SAAS;AACpE,kBAAU;AACV,mBAAW;AAAA,MACb;AACA,UAAI,SAAS,KAAK,EAAE,MAAM,KAAK;AAC7B,kBAAU;AACV,mBAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS,WAAW;AACtB,QAAI,OAAO,UAAU,WAAW;AAC9B,gBAAU;AAAA,IACZ;AAAA,EACF;AAEA,MAAI,SAAS,SAAS;AACpB,UAAM,UAAU,CAAC,QAAQ,WAAW,UAAU,UAAU,SAAS,QAAQ;AACzE,UAAM,aAAa,MAAM,QAAQ,MAAM,GAAG,QAAQ,SAAS,CAAC,EAAE,KAAK,MAAM,IAAI,YAAY,QAAQ,QAAQ,SAAS,CAAC,IAAI;AACvH,QAAI,OAAO,UAAU,UAAU;AAC7B,gBAAU,8DAA8D,aAAa;AAAA,IACvF,WAAW,QAAQ,QAAQ,KAAK,KAAK,IAAI;AACvC,gBAAU,8DAA8D,aAAa;AAAA,IACvF;AAAA,EACF;AAEA,MAAI,SAAS,SAAS;AACpB,QAAI,SAAS,OAAO,EAAE,MAAM,OAAO;AACjC,gBAAU;AAAA,IACZ;AAAA,EACF;AAEA,MAAI,SAAS,UAAU;AACrB,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,QAAQ;AACV,kBAAU;AAAA,MACZ,WAAW,EAAE,iBAAiB,SAAS;AACrC,kBAAU;AAAA,MAEZ;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS;AACX,QAAI,aAAa;AACf,YAAM,OAAO,aAAa,OACtB,MACA,MAAM,QAAQ,QAAQ,IACpB,WACA,MAAM,OAAO;AACnB,YAAM,cAAc,gBAAgB,QAAQ,IACxC,KAAK,UAAU,UAAU,MAAM,GAAI,IACnC;AAEJ,iBAAW,cAAc,OAAO,OAAO;AAAA,IACzC;AACA,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACF;AAEA,IAAM,oBAAoB,CAAC,QAAQ,aAAa,QAAQ,cAAc,WAAW,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,UAAU,SAAS,OAAO;AACzJ,IAAM,+BAA+B,CAAC,OAAO,QAAQ,QAAQ,MAAM;AACnE,IAAM,oBAAoB,CAAC,OAAO,OAAO,QAAQ,OAAO,MAAM;AAG9D,SAAS,iBAAiB,OAAO,QAAQ;AACvC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,eAAW,SAAS,OAAO;AACzB,UAAI,gBAAgB,KAAK,GAAG;AAC1B,yBAAiB,OAAO,MAAM;AAAA,MAChC;AAAA,IACF;AAAA,EACF,OACK;AACH,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAChD,UAAI,kBAAkB,QAAQ,GAAG,MAAM,IAAI;AACzC,4BAAoB,KAAK,OAAO,MAAM;AAAA,MACxC;AACA,UAAI,kBAAkB,QAAQ,GAAG,MAAM,IAAI;AAEzC;AAAA,MACF;AACA,UAAI,6BAA6B,QAAQ,GAAG,MAAM,IAAI;AAEpD;AAAA,MACF;AACA,UAAI,gBAAgB,KAAK,GAAG;AAC1B,yBAAiB,OAAO,MAAM;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAe,QAAQ,IAAI,MAAM,MAAM;AAAA;AACrC,QAAI,KAAK,MAAM;AACb,WAAK,OAAO,KAAK,UAAU,KAAK,IAAI;AACpC,WAAK,UAAU,IAAI,EAAQ,EAAE,gBAAgB,mBAAmB,CAAC;AAAA,IACnE;AAEA,UAAM,WAAW,MAAM,GAAG,MAAM,MAAM,IAAI;AAC1C,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,QAAI,CAAC,SAAS,IAAI;AAChB,WAAK,SAAS,SAAS;AACvB,YAAM,aAAa,YAAY,IAAI;AACnC,YAAM,0BAA0B,UAAU;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA;AAEA,SAAe,YAAY,IAAI,YAAY;AAAA;AACzC,WAAO,MAAM,QAAQ,IAAI,UAAU;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,0BAA0B,UAAU;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA;AAEA,SAAe,KAAK,IAAI,YAAY;AAAA;AAClC,qBAAiB,WAAW,UAAU,IAAI;AAC1C,WAAO,MAAM,QAAQ,IAAI,SAAS;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAEA,SAAe,QAAQ,IAAI,YAAY;AAAA;AACrC,WAAO,MAAM,QAAQ,IAAI,YAAY;AAAA,MACnC,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAEA,SAAe,WAAW,IAAI;AAAA;AAC5B,WAAO,MAAM,QAAQ,IAAI,UAAU;AAAA,MACjC,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAAA;AAEA,SAAe,YAAY,IAAI,UAAU;AAAA;AACvC,UAAM,OAAO,SAAS;AACtB,UAAM,OAAO,SAAS,QAAQ;AAC9B,UAAM,OAAO,SAAS;AAEtB,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,kCAAmC;AAAA,IACrD;AAEA,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,kCAAmC;AAAA,IACrD;AAEA,UAAM,MAAM,YAAY,CAAC,MAAM,MAAM,IAAI,EAAE,IAAI,kBAAkB,EAAE,KAAK,GAAG;AAE3E,WAAO,MAAM,QAAQ,IAAI,KAAK,EAAE,QAAQ,SAAS,CAAC;AAAA,EACpD;AAAA;AAaA,SAAS,aAAa,OAAO,MAAM;AACjC,aAAW,OAAO,MAAM;AACtB,YAAQ,MAAM,GAAG;AACjB,QAAI,UAAU,QAAW;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,QAAQ,MAAM,UAAU;AACrD,SAAO,SAAU,KAAK;AACpB,QAAI,YAAY,CAAC,gBAAgB,KAAK,QAAQ,GAAG;AAAE;AAAA,IAAQ;AAE3D,UAAM,SAAS,CAAC;AAChB,eAAW,SAAS,QAAQ;AAC1B,YAAM,QAAQ,aAAa,KAAK,WAAW,KAAK,CAAC;AACjD,UAAI,UAAU,QAAW;AACvB;AAAA,MACF;AACA,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,SAAK,MAAM;AAAA,EACb;AACF;AAEA,SAAS,uBAAuB,OAAO,MAAM,UAAU;AACrD,QAAM,cAAc,WAAW,KAAK;AACpC,SAAO,SAAU,KAAK;AACpB,QAAI,YAAY,CAAC,gBAAgB,KAAK,QAAQ,GAAG;AAAE;AAAA,IAAQ;AAE3D,UAAM,QAAQ,aAAa,KAAK,WAAW;AAC3C,QAAI,UAAU,QAAW;AACvB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,0BAA0B,OAAO,MAAM,UAAU;AACxD,SAAO,SAAU,KAAK;AACpB,QAAI,YAAY,CAAC,gBAAgB,KAAK,QAAQ,GAAG;AAAE;AAAA,IAAQ;AAC3D,SAAK,IAAI,KAAK,CAAC;AAAA,EACjB;AACF;AAEA,SAAS,yBAAyB,QAAQ,MAAM,UAAU;AACxD,SAAO,SAAU,KAAK;AACpB,QAAI,YAAY,CAAC,gBAAgB,KAAK,QAAQ,GAAG;AAAE;AAAA,IAAQ;AAC3D,UAAM,SAAS,OAAO,IAAI,WAAS,IAAI,KAAK,CAAC;AAC7C,SAAK,MAAM;AAAA,EACb;AACF;AAEA,SAAS,aAAa,QAAQ;AAC5B,SAAO,OAAO,MAAM,CAAC,UAAU,MAAM,QAAQ,GAAG,MAAM,EAAE;AAC1D;AAEA,SAAS,aAAa,QAAQ,MAAM,UAAU;AAC5C,QAAM,YAAY,aAAa,MAAM;AACrC,QAAM,WAAW,OAAO,WAAW;AAInC,MAAI,WAAW;AACb,QAAI,UAAU;AACZ,aAAO,0BAA0B,OAAO,CAAC,GAAG,MAAM,QAAQ;AAAA,IAC5D,OAAO;AACL,aAAO,yBAAyB,QAAQ,MAAM,QAAQ;AAAA,IACxD;AAAA,EACF,OAAO;AACL,QAAI,UAAU;AACZ,aAAO,uBAAuB,OAAO,CAAC,GAAG,MAAM,QAAQ;AAAA,IACzD,OAAO;AACL,aAAO,sBAAsB,QAAQ,MAAM,QAAQ;AAAA,IACrD;AAAA,EACF;AACF;AAEA,SAAS,OAAO,WAAW,MAAM;AAG/B,QAAM,SAAS,OAAO,KAAK,UAAU,MAAM;AAC3C,QAAM,kBAAkB,UAAU;AAElC,SAAO,aAAa,QAAQ,MAAM,eAAe;AACnD;AAGA,SAAS,UAA0B;AACjC,QAAM,IAAI,MAAM,sBAAsB;AACxC;AAEA,SAAS,cAAc,MAAM,UAAU;AACrC,QAAM,OAAO,KAAK,MAAM,QAAQ;AAIhC,MAAI,CAAC,KAAK,OAAO,CAAC,KAAK,IAAI,QAAQ;AACjC,UAAM,IAAI,MAAM,UAAU,KAAK,MAAM,gBAAgB,WACnD,2EAC0C;AAAA,EAC9C;AACF;AAEA,IAAM,iBAAiB;AAAA;AAAA,EACF;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,iBAAkB,IAAI;AAC7B,MAAI,GAAG,2BAA2B;AAChC,WAAO;AAAA;AAAA;AAAA;AAAA,MAIL,OAAO,SAAS,iBAAiB,WAAW,MAAM;AAChD,cAAM,WAAW,eAAe,MAAM,KAAK,IAAI;AAC/C,eAAO,GAAG,0BAA0B,MAAM,KAAK,MAAM,WAAW,MAAM,QAAQ;AAAA,MAChF;AAAA,MACA,aAAa,SAAS,yBAAyB;AAC7C,cAAM,WAAW,eAAe,YAAY,KAAK,IAAI;AACrD,eAAO,GAAG,0BAA0B,YAAY,KAAK,MAAM,QAAQ;AAAA,MACrE;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,YAAY,MAAM;AACzB,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC1D;AACA,SAAO,KAAK,IAAI,SAAU,SAAS;AACjC,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,MAAM,CAAC;AACb,UAAI,OAAO,IAAI;AACf,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AAEA,IAAM,eAAe;AACrB,SAAS,gBAAgB,UAAU;AACjC,MAAI,kBAAkB,CAAC;AACvB,MAAI,OAAO,aAAa,UAAU;AAChC,oBAAgB,KAAK,QAAQ;AAAA,EAC/B,OAAO;AACL,sBAAkB;AAAA,EACpB;AAEA,SAAO,gBAAgB,IAAI,SAAU,MAAM;AACzC,WAAO,KAAK,QAAQ,cAAc,EAAE;AAAA,EACtC,CAAC;AACH;AAEA,SAAS,gBAAgB,UAAU;AACjC,WAAS,SAAS,SAAS,OAAO,IAAI,SAAU,OAAO;AACrD,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,MAAM,CAAC;AACb,UAAI,KAAK,IAAI;AACb,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AACD,MAAI,SAAS,yBAAyB;AACpC,aAAS,0BAA0B;AAAA,MACjC,SAAS;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,KAAK,OAAO;AACjC,SAAO,MAAM,IAAI,OAAO,IAAI,CAAC,QAAQ;AACnC,UAAM,QAAQ,OAAO,GAAG;AACxB,WAAO,gBAAgB,KAAK,WAAW,KAAK,CAAC;AAAA,EAC/C,CAAC;AACH;AAIA,SAAS,qBAAqB,MAAM,aAAa,OAAO;AACtD,QAAM,cAAc,MAAM,IAAI;AAC9B,MAAI,UAAU;AACd,aAAW,OAAO,MAAM;AAItB,QAAI,SAAS,cAAc,IAAI,KAAK,KAAK;AACzC,QAAI,YAAY,WAAW,GAAG;AAC5B,eAAS,OAAO,CAAC;AAAA,IACnB,OAAO;AAGL,aAAO,OAAO,SAAS,YAAY,QAAQ;AACzC,eAAO,IAAI;AAAA,MACb;AAAA,IACF;AAEA,QAAI,KAAK,IAAI,QAAQ,QAAQ,WAAW,CAAC,IAAI,GAAG;AAE9C;AAAA,IACF;AACA,MAAE;AAAA,EACJ;AACA,SAAO,UAAU,IAAI,KAAK,MAAM,OAAO,IAAI;AAC7C;AAEA,SAAS,eAAe,MAAM;AAC5B,QAAM,UAAU,MAAM,IAAI;AAC1B,SAAO,QAAQ;AACf,SAAO,QAAQ;AACf,SAAO,QAAQ;AACf,SAAO,QAAQ;AAEf,MAAI,YAAY,MAAM;AACpB,YAAQ,WAAW,KAAK;AAAA,EAC1B;AACA,MAAI,cAAc,MAAM;AACtB,YAAQ,SAAS,KAAK;AAAA,EACxB;AACA,MAAI,qBAAqB,MAAM;AAC7B,YAAQ,gBAAgB,KAAK;AAAA,EAC/B;AACA,MAAI,mBAAmB,MAAM;AAC3B,YAAQ,kBAAkB,KAAK;AAAA,EACjC;AACA,SAAO;AACT;AAEA,SAAS,cAAc,OAAO;AAC5B,QAAM,YAAY,MAAM,OAAO,OAAO,SAAU,OAAO;AACrD,WAAO,SAAS,KAAK,MAAM;AAAA,EAC7B,CAAC;AACD,MAAI,UAAU,WAAW,KAAK,UAAU,WAAW,MAAM,OAAO,QAAQ;AACtE,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC7C;AACF;AAEA,SAAS,aAAa,YAAY,OAAO;AACvC,MAAI,MAAM,eAAe,WAAW,MAAM;AACxC,UAAM,cAAc,WAAW,KAAK,OAAO,SAAU,UAAU;AAC7D,aAAO,OAAO,KAAK,QAAQ,EAAE,CAAC,MAAM;AAAA,IACtC,CAAC,EAAE,IAAI,SAAU,UAAU;AACzB,aAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;AAAA,IAChC,CAAC;AAED,QAAI,YAAY,SAAS,GAAG;AAC1B,YAAM,IAAI,MAAM,8BAA8B,YAAY,KAAK,GAAG,IAChE,gCAAgC;AAAA,IACpC;AAAA,EACF;AAEA,MAAI,MAAM,aAAa;AACrB;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,YAAY;AACvC,MAAI,OAAO,WAAW,aAAa,UAAU;AAC3C,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC/D;AAeF;AAMA,SAAS,cAAc,UAAU,MAAM;AACrC,QAAM,iBAAiB,OAAO,KAAK,QAAQ;AAC3C,QAAM,aAAa,OAAO,KAAK,IAAI,MAAM,IAAI,CAAC;AAC9C,MAAI;AACJ,MAAI,eAAe,UAAU,WAAW,QAAQ;AAC9C,iBAAa;AAAA,EACf,OAAO;AACL,iBAAa;AAAA,EACf;AAEA,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,EACF;AAGA,eAAa,WAAW,KAAK,SAAU,MAAM,OAAO;AAClD,QAAI,UAAU,WAAW,QAAQ,IAAI;AACrC,QAAI,YAAY,IAAI;AAClB,gBAAU,OAAO;AAAA,IACnB;AACA,QAAI,WAAW,WAAW,QAAQ,KAAK;AACvC,QAAI,aAAa,IAAI;AACnB,iBAAW,OAAO;AAAA,IACpB;AACA,WAAO,UAAU,WAAW,KAAK,UAAU,WAAW,IAAI;AAAA,EAC5D,CAAC;AAED,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,WAAW,KAAK,IAAI,MAAM;AAAA,EAC5B;AACF;AAEA,SAAe,cAAc,IAAI,YAAY;AAAA;AAC3C,iBAAa,0BAA0B,UAAU;AACjD,UAAM,mBAAmB,MAAM,WAAW,KAAK;AAC/C,eAAW,QAAQ,gBAAgB,WAAW,KAAK;AAEnD,kBAAc,WAAW,KAAK;AAI9B,QAAI;AACJ,aAAS,SAAS;AAChB,aAAO,QAAQ,MAAM,UAAU,KAAK,UAAU,UAAU,CAAC;AAAA,IAC3D;AAEA,UAAM,WAAW,WAAW,QAAS,SAAS,OAAO;AAErD,UAAM,WAAW,WAAW,QAAS,SAAS,OAAO;AACrD,UAAM,SAAS,aAAa;AAE5B,QAAI,qBAAqB;AACzB,QAAI,aAAa;AAEjB,aAAS,WAAW,KAAK;AACvB,UAAI,IAAI,QAAQ,IAAI,aAAa,SAAS;AACxC,6BAAqB;AAAA,MACvB;AACA,UAAI,WAAW;AACf,UAAI,QAAQ,IAAI,SAAS,CAAC;AAE1B,mBAAa,CAAC,CAAC,IAAI,MAAM,QAAQ;AAEjC,UAAI,YAAY;AACd,eAAO;AAAA,MACT;AAEA,UAAI,MAAM,QAAQ,IAAI;AAAA,QACpB,KAAK;AAAA,UACH,QAAQ,aAAa,WAAW,MAAM,MAAM;AAAA,UAC5C,yBAAyB,WAAW,MAAM;AAAA,QAC5C;AAAA,QACA,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,OAAG,YAAY,KAAK,SAAS,CAAC,QAAQ,kBAAkB,MAAM,CAAC;AAE/D,UAAM,OAAO,IAAI,QAAQ,UAAU;AAEnC,QAAI,oBAAoB;AACtB,YAAM,IAAI,MAAM,wCACd,SACA,uBAAuB;AAAA,IAC3B;AAKA,UAAM,YAAY,WAAW,MAAM;AACnC,UAAM,iBAAiB,EAAE,EAAE,MAAM,KAAK,IAAI,WAAW;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,WAAO;AAAA,MACL,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,QAAQ,aAAa,WAAW;AAAA,IAClC;AAAA,EACF;AAAA;AAEA,SAAe,aAAa,IAAI;AAAA;AAG9B,UAAM,aAAa,MAAM,GAAG,QAAQ;AAAA,MAClC,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,cAAc;AAAA,IAChB,CAAC;AACD,UAAMF,OAAM;AAAA,MACV,SAAS,CAAC;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,UACH,QAAQ,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,IAAAA,KAAI,UAAU,QAAQA,KAAI,SAAS,WAAW,KAAK,OAAO,SAAU,KAAK;AACvE,aAAO,IAAI,IAAI,aAAa;AAAA,IAC9B,CAAC,EAAE,IAAI,SAAU,KAAK;AACpB,YAAM,YAAY,IAAI,IAAI,UAAU,SAAY,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAE9E,aAAO,UAAU,IAAI,SAAU,UAAU;AACvC,cAAM,OAAO,IAAI,IAAI,MAAM,QAAQ;AACnC,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK,gBAAgB,KAAK,QAAQ,GAAG;AAAA,QACvC;AAAA,MACF,CAAC;AAAA,IACH,CAAC,CAAC;AAGF,IAAAA,KAAI,QAAQ,KAAK,SAAU,MAAM,OAAO;AACtC,aAAO,QAAQ,KAAK,MAAM,MAAM,IAAI;AAAA,IACtC,CAAC;AACD,IAAAA,KAAI,aAAaA,KAAI,QAAQ;AAC7B,WAAOA;AAAA,EACT;AAAA;AAGA,IAAM,aAAa;AAGnB,IAAM,aAAa,EAAE,KAAU,CAAC,EAAE;AAElC,IAAM,sBAAsB;AAAA,EAC1B,WAAW,EAAE,OAAO,GAAG,UAAU,YAAY,QAAQ,WAAW;AAAA,EAChE,gBAAgB,CAAC;AACnB;AAIA,SAAS,kBAAkB,OAAO,OAAO;AACvC,SAAO,MAAM,IAAI,OACd,KAAK,CAAC,QAAQ,OAAO,GAAG,MAAM,KAAK;AACxC;AAOA,SAAS,2BAA2B,UAAU,OAAO;AACnD,QAAM,UAAU,SAAS,KAAK;AAC9B,QAAM,eAAe,OAAO,OAAO;AAEnC,SAAO,iBAAiB;AAC1B;AAIA,SAAS,kBAAkB,YAAY,OAAO;AAC5C,QAAM,cAAc,MAAM,IAAI,OAAO,IAAI,MAAM;AAE/C,SAAO,WAAW,MAAM,EAAE,KAAK,SAAU,GAAG,GAAG;AAC7C,QAAI,OAAO,YAAY,QAAQ,CAAC;AAChC,QAAI,OAAO,YAAY,QAAQ,CAAC;AAChC,QAAI,SAAS,IAAI;AACf,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,SAAS,IAAI;AACf,aAAO,OAAO;AAAA,IAChB;AACA,WAAO,QAAQ,MAAM,IAAI;AAAA,EAC3B,CAAC;AACH;AAGA,SAAS,uBAAuB,OAAO,UAAU,YAAY;AAC3D,eAAa,kBAAkB,YAAY,KAAK;AAGhD,MAAI,uBAAuB;AAC3B,WAAS,IAAI,GAAG,MAAM,WAAW,QAAQ,IAAI,KAAK,KAAK;AACrD,UAAM,QAAQ,WAAW,CAAC;AAC1B,QAAI,wBAAwB,CAAC,kBAAkB,OAAO,KAAK,GAAG;AAC5D,aAAO,WAAW,MAAM,CAAC;AAAA,IAC3B;AACA,QAAI,IAAI,MAAM,KAAK,2BAA2B,UAAU,KAAK,GAAG;AAC9D,6BAAuB;AAAA,IACzB;AAAA,EACF;AACA,SAAO,CAAC;AACV;AAEA,SAAS,wBAAwB,UAAU;AACzC,QAAM,SAAS,CAAC;AAChB,aAAW,CAAC,OAAO,OAAO,KAAK,OAAO,QAAQ,QAAQ,GAAG;AACvD,eAAW,YAAY,OAAO,KAAK,OAAO,GAAG;AAC3C,UAAI,aAAa,OAAO;AACtB,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,oBAAoB,OAAO,UAAU,YAAY;AAC1E,QAAM,SAAS;AAAA;AAAA,IAEb;AAAA;AAAA,IAEA,uBAAuB,OAAO,UAAU,UAAU;AAAA;AAAA,IAElD,wBAAwB,QAAQ;AAAA,EAClC;AAEA,SAAO,kBAAkBE,MAAK,MAAM,GAAG,KAAK;AAC9C;AAIA,SAAS,sBAAsB,aAAa,WAAW,QAAQ;AAC7D,MAAI,WAAW;AAGb,UAAM,cAAc,gCAAgC,WAAW,WAAW;AAC1E,UAAM,kBAAkB,0BAA0B,QAAQ,WAAW;AAErE,WAAO,eAAe;AAAA,EACxB;AAKA,SAAO,wBAAwB,QAAQ,WAAW;AACpD;AAEA,IAAM,kBAAkB,CAAC,OAAO,OAAO,QAAQ,OAAO,MAAM;AAC5D,SAAS,oBAAoB,SAAS;AACpC,SAAO,gBAAgB,QAAQ,OAAO,MAAM;AAC9C;AAMA,SAAS,0BAA0B,aAAa,UAAU;AACxD,QAAM,aAAa,YAAY,CAAC;AAChC,QAAM,UAAU,SAAS,UAAU;AAEnC,MAAI,OAAO,YAAY,aAAa;AAElC,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,OAAO,KAAK,OAAO,EAAE,WAAW,KAClD,OAAO,OAAO,MAAM;AAEtB,SAAO,CAAC;AACV;AAEA,SAAS,kBAAkB,OAAO,WAAW,QAAQ,UAAU;AAC7D,QAAM,cAAc,MAAM,IAAI,OAAO,IAAI,MAAM;AAC/C,QAAM,cAAc,sBAAsB,aAAa,WAAW,MAAM;AAExE,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,SAAO,0BAA0B,aAAa,QAAQ;AACxD;AAQA,SAAS,oBAAoB,UAAU,YAAY,WAAW,SAAS;AACrE,SAAO,QAAQ,OAAO,SAAU,OAAO;AACrC,WAAO,kBAAkB,OAAO,WAAW,YAAY,QAAQ;AAAA,EACjE,CAAC;AACH;AAIA,SAAS,sBAAsB,UAAU,YAAY,WAAW,SAAS,UAAU;AACjF,QAAM,kBAAkB,oBAAoB,UAAU,YAAY,WAAW,OAAO;AAEpF,MAAI,gBAAgB,WAAW,GAAG;AAChC,QAAI,UAAU;AACZ,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AAGA,UAAM,eAAe,QAAQ,CAAC;AAC9B,iBAAa,cAAc;AAC3B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,WAAW,KAAK,CAAC,UAAU;AAC7C,WAAO,gBAAgB,CAAC;AAAA,EAC1B;AAEA,QAAM,gBAAgB,cAAc,UAAU;AAE9C,WAAS,WAAW,OAAO;AACzB,UAAM,cAAc,MAAM,IAAI,OAAO,IAAI,MAAM;AAC/C,QAAI,QAAQ;AACZ,eAAW,cAAc,aAAa;AACpC,UAAI,cAAc,UAAU,GAAG;AAC7B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,MAAI,UAAU;AACZ,UAAM,eAAe,aAAa,SAAS,CAAC;AAC5C,UAAM,eAAe,SAAS,WAAW,IAAI,SAAS,CAAC,IAAI;AAC3D,UAAM,QAAQ,gBAAgB,KAAK,SAAUC,QAAO;AAClD,UAAI,gBAAgBA,OAAM,SAAS,gBAAgB,iBAAiBA,OAAM,MAAM;AAC9E,eAAO;AAAA,MACT;AAEA,UAAIA,OAAM,SAAS,cAAc;AAE/B,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT,CAAC;AAED,QAAI,CAAC,OAAO;AACV,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO,IAAI,iBAAiB,UAAU;AACxC;AAEA,SAAS,2BAA2B,cAAc,WAAW;AAC3D,UAAQ,cAAc;AAAA,IACpB,KAAK;AACH,aAAO,EAAE,KAAK,UAAU;AAAA,IAC1B,KAAK;AACH,aAAO,EAAE,QAAQ,UAAU;AAAA,IAC7B,KAAK;AACH,aAAO,EAAE,UAAU,UAAU;AAAA,IAC/B,KAAK;AACH,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,eAAe;AAAA,MACjB;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,UAAU;AAAA,QACV,iBAAiB;AAAA,MACnB;AAAA,EACJ;AAEA,SAAO;AAAA,IACL,UAAU;AAAA,EACZ;AACF;AAEA,SAAS,4BAA4B,UAAU,OAAO;AACpD,QAAM,QAAQ,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC;AAGxC,QAAM,UAAU,SAAS,KAAK,KAAK,CAAC;AACpC,QAAM,iBAAiB,CAAC;AACxB,QAAM,gBAAgB,OAAO,KAAK,OAAO;AAEzC,MAAI;AAEJ,aAAW,gBAAgB,eAAe;AACxC,QAAI,oBAAoB,YAAY,GAAG;AACrC,qBAAe,KAAK,KAAK;AAAA,IAC3B;AAEA,UAAM,YAAY,QAAQ,YAAY;AACtC,UAAM,eAAe,2BAA2B,cAAc,SAAS;AAEvE,QAAI,cAAc;AAChB,qBAAe,aAAa,CAAC,cAAc,YAAY,CAAC;AAAA,IAC1D,OAAO;AACL,qBAAe;AAAA,IACjB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,WAAW;AAAA,IACX;AAAA,EACF;AACF;AAEA,SAAS,2BAA2B,cAAc,WAAW;AAC3D,UAAQ,cAAc;AAAA,IACpB,KAAK;AACH,aAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,UAAU;AAAA,MACZ;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,eAAe;AAAA,MACjB;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,UAAU;AAAA,QACV,iBAAiB;AAAA,MACnB;AAAA,EACJ;AACF;AAEA,SAAS,uBAAuB,UAAU,OAAO;AAC/C,QAAM,cAAc,MAAM,IAAI,OAAO,IAAI,MAAM;AAE/C,MAAI,iBAAiB,CAAC;AACtB,QAAM,WAAW,CAAC;AAClB,QAAM,SAAS,CAAC;AAChB,MAAI;AACJ,MAAI;AAEJ,WAAS,OAAO,GAAG;AAEjB,QAAI,mBAAmB,OAAO;AAC5B,eAAS,KAAK,UAAU;AAAA,IAC1B;AACA,QAAI,iBAAiB,OAAO;AAC1B,aAAO,KAAK,UAAU;AAAA,IACxB;AAGA,qBAAiB,YAAY,MAAM,CAAC;AAAA,EACtC;AAEA,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,UAAM,aAAa,YAAY,CAAC;AAChC,UAAM,UAAU,SAAS,UAAU;AAEnC,QAAI,CAAC,WAAW,CAAC,OAAO,KAAK,OAAO,EAAE,QAAQ;AAC5C,aAAO,CAAC;AACR;AAAA,IACF,WAAW,OAAO,KAAK,OAAO,EAAE,KAAK,mBAAmB,GAAG;AACzD,aAAO,CAAC;AACR;AAAA,IACF,WAAW,IAAI,GAAG;AAChB,YAAM,YACJ,SAAS,WAAW,UAAU,WAC9B,SAAS,WAAW,UAAU;AAChC,YAAM,eAAe,OAAO,KAAK,SAAS,YAAY,IAAI,CAAC,CAAC,CAAC;AAC7D,YAAM,gBAAgB,YAAY,cAAc,CAAC,KAAK,CAAC;AACvD,YAAM,kBAAkB,YAAY,cAAc,OAAO,KAAK,OAAO,CAAC;AACtE,YAAM,sBAAsB,aAAa,CAAC,iBAAiB,CAAC;AAC5D,UAAI,qBAAqB;AACvB,eAAO,CAAC;AACR;AAAA,MACF;AAAA,IACF;AAEA,UAAM,gBAAgB,OAAO,KAAK,OAAO;AACzC,QAAI,eAAe;AAEnB,eAAW,gBAAgB,eAAe;AACxC,YAAM,YAAY,QAAQ,YAAY;AACtC,YAAM,UAAU,2BAA2B,cAAc,SAAS;AAElE,UAAI,cAAc;AAChB,uBAAe,aAAa,CAAC,cAAc,OAAO,CAAC;AAAA,MACrD,OAAO;AACL,uBAAe;AAAA,MACjB;AAAA,IACF;AAEA,aAAS,KAAK,cAAc,eAAe,aAAa,WAAW,UAAU;AAC7E,WAAO,KAAK,YAAY,eAAe,aAAa,SAAS,UAAU;AACvE,QAAI,qBAAqB,cAAc;AACrC,uBAAiB,aAAa;AAAA,IAChC;AACA,QAAI,mBAAmB,cAAc;AACnC,qBAAe,aAAa;AAAA,IAC9B;AAAA,EACF;AAEA,QAAMH,OAAM;AAAA,IACV;AAAA,IACA;AAAA,EACF;AAEA,MAAI,OAAO,mBAAmB,aAAa;AACzC,IAAAA,KAAI,kBAAkB;AAAA,EACxB;AACA,MAAI,OAAO,iBAAiB,aAAa;AACvC,IAAAA,KAAI,gBAAgB;AAAA,EACtB;AAEA,SAAO;AAAA,IACL,WAAWA;AAAA,IACX;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,UAAU;AAOpC,QAAM,SAAS,OAAO,KAAK,QAAQ,EAAE,IAAI,SAAU,KAAK;AACtD,WAAO,SAAS,GAAG;AAAA,EACrB,CAAC;AACD,SAAO,OAAO,KAAK,SAAU,KAAK;AAChC,WAAO,OAAO,QAAQ,YAAY,OAAO,KAAK,GAAG,EAAE,WAAW;AAAA,EAChE,CAAC;AACH;AAEA,SAAS,oBAAoB,UAAU;AAErC,SAAO;AAAA,IACL,WAAW,EAAE,UAAU,KAAK;AAAA,IAC5B,gBAAgB,CAAC,OAAO,KAAK,QAAQ,CAAC;AAAA,EACxC;AACF;AAEA,SAAS,iBAAiB,UAAU,OAAO;AACzC,MAAI,MAAM,aAAa;AACrB,WAAO,oBAAoB,UAAU,KAAK;AAAA,EAC5C;AAEA,MAAI,MAAM,IAAI,OAAO,WAAW,GAAG;AAEjC,WAAO,4BAA4B,UAAU,KAAK;AAAA,EACpD;AAEA,SAAO,uBAAuB,UAAU,KAAK;AAC/C;AAEA,SAAS,UAAU,SAAS,SAAS;AACnC,QAAM,WAAW,QAAQ;AACzB,QAAM,OAAO,QAAQ;AAErB,MAAI,mBAAmB,QAAQ,GAAG;AAChC,WAAO,OAAO,OAAO,CAAC,GAAG,qBAAqB,EAAE,OAAO,QAAQ,CAAC,EAAE,CAAC;AAAA,EACrE;AAEA,QAAM,gBAAgB,cAAc,UAAU,IAAI;AAElD,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc;AAChC,QAAM,QAAQ,sBAAsB,UAAU,YAAY,WAAW,SAAS,QAAQ,SAAS;AAE/F,QAAM,gBAAgB,iBAAiB,UAAU,KAAK;AACtD,QAAM,YAAY,cAAc;AAChC,QAAM,qBAAqB,cAAc;AAEzC,QAAM,iBAAiB,kBAAkB,oBAAoB,OAAO,UAAU,UAAU;AAExF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,OAAO;AAE/B,SAAO,MAAM,KAAK,UAAU,CAAC,IAAI,MAAM,MAAM;AAC/C;AAEA,SAAe,UAAU,IAAI,cAAc;AAAA;AACzC,UAAM,OAAO,MAAM,YAAY;AAI/B,QAAI,KAAK,YAAY;AACnB,UAAI,YAAY,QAAQ,OAAO,KAAK,WAAW,UAAU;AACvD,aAAK,SAAS;AAAA,MAChB;AACA,UAAI,cAAc,QAAQ,OAAO,KAAK,aAAa,UAAU;AAC3D,aAAK,QAAQ;AAAA,MACf;AAAA,IACF,OAAO;AACL,UAAI,cAAc,QAAQ,OAAO,KAAK,aAAa,UAAU;AAC3D,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,YAAY,QAAQ,OAAO,KAAK,WAAW,UAAU;AACvD,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AACA,QAAI,SAAS,QAAQ,OAAO,KAAK,QAAQ,UAAU;AACjD,WAAK,QAAQ;AAAA,IACf;AAEA,QAAI,KAAK,QAAQ,KAAK,KAAK,eAAe;AAIxC,WAAK,iBAAiB,KAAK;AAC3B,WAAK,SAAS,KAAK;AAAA,IACrB;AAEA,UAAMA,OAAM,MAAM,GAAG,QAAQ,IAAI;AAEjC,IAAAA,KAAI,OAAOA,KAAI,KAAK,OAAO,SAAU,KAAK;AACxC,aAAO,CAAC,aAAa,KAAK,IAAI,EAAE;AAAA,IAClC,CAAC;AAED,QAAI,KAAK,gBAAgB;AACvB,WAAK,QAAQ,KAAK;AAAA,IACpB;AAEA,IAAAA,KAAI,OAAOA,KAAI,KAAK,MAAM,GAAG,KAAK,KAAK;AACvC,WAAOA;AAAA,EACT;AAAA;AAEA,SAAe,gBAAgB,IAAI,MAAM,YAAY;AAAA;AACnD,QAAI,WAAW,SAAS,aAAa;AACnC,aAAO,UAAU,IAAI,IAAI;AAAA,IAC3B;AACA,WAAO,iBAAiB,EAAE,EAAE,MAAM,KAAK,IAAI,iBAAiB,UAAU,GAAG,IAAI;AAAA,EAC/E;AAAA;AAEA,SAAe,OAAO,IAAI,YAAYI,UAAS;AAAA;AAC7C,QAAI,WAAW,UAAU;AAEvB,uBAAiB,WAAW,UAAU,KAAK;AAC3C,iBAAW,WAAW,gBAAgB,WAAW,QAAQ;AAAA,IAC3D;AAEA,QAAI,WAAW,MAAM;AACnB,iBAAW,OAAO,YAAY,WAAW,IAAI;AAAA,IAC/C;AAEA,QAAI,WAAW,WAAW;AACxB,iBAAW,YAAY,gBAAgB,WAAW,SAAS;AAAA,IAC7D;AAEA,QAAI,EAAE,WAAW,aAAa;AAE5B,iBAAW,QAAQ;AAAA,IACrB;AAEA,wBAAoB,UAAU;AAE9B,UAAM,gBAAgB,MAAM,aAAa,EAAE;AAE3C,OAAG,YAAY,KAAK,SAAS,CAAC,QAAQ,kBAAkB,UAAU,CAAC;AACnE,UAAM,YAAY,UAAU,YAAY,cAAc,OAAO;AAC7D,OAAG,YAAY,KAAK,SAAS,CAAC,QAAQ,cAAc,SAAS,CAAC;AAE9D,UAAM,aAAa,UAAU;AAE7B,iBAAa,YAAY,UAAU;AAEnC,QAAI,OAAO,OAAO,OAAO;AAAA,MACvB,cAAc;AAAA,MACd,QAAQ;AAAA;AAAA,MAER,eAAe,cAAc;AAAA,IAC/B,GAAG,UAAU,SAAS;AAEtB,QAAI,cAAc,QAAQ,YAAY,QACpC,QAAQ,KAAK,UAAU,KAAK,MAAM,IAAI,GAAG;AAGzC,aAAO,EAAE,MAAM,CAAC,EAAE;AAAA,IACpB;AAEA,UAAM,eAAe,WAAW,QAC9B,OAAO,WAAW,KAAK,CAAC,MAAM,YAC9B,SAAS,WAAW,KAAK,CAAC,CAAC,MAAM;AAEnC,QAAI,cAAc;AAEhB,WAAK,aAAa;AAClB,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,QAAI,CAAC,UAAU,eAAe,QAAQ;AAGpC,WAAK,QAAQ,WAAW;AACxB,UAAI,UAAU,YAAY;AACxB,aAAK,OAAO,WAAW;AAAA,MACzB;AAAA,IACF;AAEA,QAAIA,UAAS;AACX,aAAO,QAAQ,QAAQ,WAAW,IAAI;AAAA,IACxC;AAEA,UAAMJ,OAAM,MAAM,gBAAgB,IAAI,MAAM,UAAU;AAGtD,QAAI,KAAK,oBAAoB,OAAO;AAGlC,MAAAA,KAAI,OAAO,qBAAqBA,KAAI,MAAM,KAAK,UAAU,UAAU;AAAA,IACrE;AAEA,QAAI,UAAU,eAAe,QAAQ;AAEnC,MAAAA,KAAI,OAAO,qBAAqBA,KAAI,MAAM,YAAY,UAAU,cAAc;AAAA,IAChF;AAEA,UAAM,OAAO;AAAA,MACX,MAAMA,KAAI,KAAK,IAAI,SAAU,KAAK;AAChC,cAAM,MAAM,IAAI;AAChB,YAAI,WAAW,QAAQ;AACrB,iBAAO,KAAK,KAAK,WAAW,MAAM;AAAA,QACpC;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,QAAI,WAAW,aAAa;AAC1B,WAAK,UAAU;AAAA,IACjB;AAEA,WAAO;AAAA,EACT;AAAA;AAEA,SAAe,UAAU,IAAI,YAAY;AAAA;AACvC,UAAM,YAAY,MAAM,OAAO,IAAI,YAAY,IAAI;AAEnD,WAAO;AAAA,MACL,QAAQ,GAAG;AAAA,MACX,OAAO,UAAU;AAAA,MACjB,UAAU,WAAW;AAAA,MACrB,OAAO;AAAA,QACL,WAAW,UAAU,UAAU;AAAA,QAC/B,SAAS,UAAU,UAAU;AAAA,MAC/B;AAAA,MACA,MAAM;AAAA,QACJ,WAAW,WAAW,aAAa,CAAC;AAAA,QACpC,UAAU;AAAA;AAAA,QACV,OAAO,WAAW;AAAA,QAClB,MAAM,WAAW;AAAA,QACjB,MAAM,WAAW,QAAQ,CAAC;AAAA,QAC1B,QAAQ,WAAW;AAAA,QACnB,WAAW;AAAA;AAAA,QACX,GAAG,CAAC,EAAE;AAAA;AAAA,MACR;AAAA,MACA,OAAO,WAAW;AAAA,MAClB,MAAM,WAAW,QAAQ;AAAA,MACzB,QAAQ,WAAW;AAAA,IACrB;AAAA,EACF;AAAA;AAEA,SAAe,cAAc,IAAI,OAAO;AAAA;AAEtC,QAAI,CAAC,MAAM,MAAM;AACf,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AAEA,QAAI,CAAC,MAAM,MAAM;AACf,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AAEA,UAAM,QAAQ,MAAM;AACpB,UAAM,WAAW,MAAM;AAEvB,aAAS,SAAS,KAAK;AACrB,UAAI,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,KAAK,IAAI,MAAM,QAAQ,GAAG;AAE9D,eAAO,EAAC,KAAK,OAAO,UAAU,KAAI;AAAA,MACpC;AAEA,aAAO,IAAI,MAAM,QAAQ;AACzB,aAAO;AAAA,IACT;AAEA,UAAM,OAAO,IAAI,OAAO,QAAQ;AAChC,UAAM,iBAAiB,EAAE,EAAE,YAAY,MAAM,EAAE;AAC/C,WAAO,EAAE,IAAI,KAAK;AAAA,EACpB;AAAA;AAEA,IAAM,SAAS,CAAC;AAChB,OAAO,cAAc,kBAAkB,SAAgB,YAAY;AAAA;AACjE,QAAI,OAAO,eAAe,UAAU;AAClC,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAEA,UAAM,iBAAiB,SAAS,IAAI,IAClC,cAAc;AAChB,WAAO,eAAe,MAAM,UAAU;AAAA,EACxC;AAAA,CAAC;AAED,OAAO,OAAO,kBAAkB,SAAgB,YAAY;AAAA;AAC1D,QAAI,OAAO,eAAe,UAAU;AAClC,YAAM,IAAI,MAAM,8CAA8C;AAAA,IAChE;AAEA,UAAM,UAAU,SAAS,IAAI,IAAI,OAAO;AACxC,WAAO,QAAQ,MAAM,UAAU;AAAA,EACjC;AAAA,CAAC;AAED,OAAO,UAAU,kBAAkB,SAAgB,YAAY;AAAA;AAC7D,QAAI,OAAO,eAAe,UAAU;AAClC,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AAEA,UAAM,UAAU,SAAS,IAAI,IAAI,UAAU;AAC3C,WAAO,QAAQ,MAAM,UAAU;AAAA,EACjC;AAAA,CAAC;AAED,OAAO,aAAa,kBAAkB,WAAkB;AAAA;AACtD,UAAM,gBAAgB,SAAS,IAAI,IAAI,aAAa;AACpD,WAAO,cAAc,IAAI;AAAA,EAC3B;AAAA,CAAC;AAED,OAAO,cAAc,kBAAkB,SAAgB,UAAU;AAAA;AAC/D,QAAI,OAAO,aAAa,UAAU;AAChC,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAEA,UAAM,iBAAiB,SAAS,IAAI,IAClC,cAAc;AAChB,WAAO,eAAe,MAAM,QAAQ;AAAA,EACtC;AAAA,CAAC;AAED,IAAO,2BAAQ;", "names": ["reason", "collationIndex", "Md5", "nextTick", "match", "res", "res", "res", "mapper", "reducer", "ddocValidator", "response", "result", "metaDoc", "doc", "progress", "mapResults", "res", "fields", "i", "field", "matcher", "res", "max", "uniq", "index", "explain"]}