// // Patient Service using PouchDB Service
// import { Injectable } from '@angular/core';
// import { FormGroup } from '@angular/forms';
// import { Observable } from 'rxjs';
// import { PouchdbService, PouchDBDocument, PouchDBResponse } from './pouchdb.service';

// export interface Patient extends PouchDBDocument {
//   first_name: string;
//   last_name: string;
//   mobile: string;
//   email?: string;
//   age?: number;
//   gender?: string;
//   address?: string;
//   medical_history?: string[];
//   type: 'patient';
// }

// @Injectable({ providedIn: 'root' })
// export class PatientService {
//   private readonly DB_NAME = 'patients_db';

//   constructor(private pouchService: PouchdbService) {
//     // Initialize DB when service loads
//     this.initializeDatabase();
//   }

//   private async initializeDatabase(): Promise<void> {
//     try {
//       await this.pouchService.initDB(this.DB_NAME).toPromise();
//       console.log('✅ Patient database initialized');
//     } catch (error) {
//       console.error('❌ Failed to initialize patient database:', error);
//     }
//   }

//   /**
//    * 🟢 CREATE / UPDATE PATIENT
//    */
//   savePatient(patientForm: FormGroup): Observable<PouchDBResponse> {
//     const formValue = patientForm.value;

//     // Auto-generate _id if new
//     if (!formValue._id) {
//       formValue._id = this.pouchService.generateId('patient');
//     }

//     // Set document type
//     formValue.type = 'patient';

//     // Upsert document
//     return this.pouchService.upsertDoc<Patient>(formValue, this.DB_NAME);
//   }

//   /**
//    * 🟡 READ SINGLE PATIENT
//    */
//   getPatientById(id: string): Observable<Patient> {
//     return this.pouchService.getDoc<Patient>(id, this.DB_NAME);
//   }

//   /**
//    * 🟡 READ ALL PATIENTS
//    */
//   getAllPatients(): Observable<Patient[]> {
//     return this.pouchService.getAllDocs<Patient>(this.DB_NAME);
//   }

//   /**
//    * 🟠 DELETE PATIENT
//    */
//   deletePatient(patient: Patient): Observable<PouchDBResponse> {
//     if (!patient._id) {
//       throw new Error('Patient _id is required for delete');
//     }
//     return this.pouchService.deleteDoc(patient._id, this.DB_NAME);
//   }

//   /**
//    * 🔍 SEARCH PATIENTS (BY NAME OR MOBILE)
//    */
//   searchPatients(searchTerm: string): Observable<Patient[]> {
//     const query = {
//       selector: {
//         $and: [
//           { type: 'patient' },
//           {
//             $or: [
//               { first_name: { $regex: `(?i)${searchTerm}` } },
//               { last_name: { $regex: `(?i)${searchTerm}` } },
//               { mobile: { $regex: `(?i)${searchTerm}` } }
//             ]
//           }
//         ]
//       }
//     };

//     return this.pouchService.findDocs<Patient>(query, this.DB_NAME);
//   }

//   /**
//    * 🔍 ADVANCED SEARCH with multiple criteria
//    */
//   advancedSearch(criteria: {
//     name?: string;
//     mobile?: string;
//     age?: { min?: number; max?: number };
//     gender?: string;
//   }): Observable<Patient[]> {
//     const conditions: any[] = [{ type: 'patient' }];

//     if (criteria.name) {
//       conditions.push({
//         $or: [
//           { first_name: { $regex: `(?i)${criteria.name}` } },
//           { last_name: { $regex: `(?i)${criteria.name}` } }
//         ]
//       });
//     }

//     if (criteria.mobile) {
//       conditions.push({ mobile: { $regex: `(?i)${criteria.mobile}` } });
//     }

//     if (criteria.age) {
//       const ageCondition: any = {};
//       if (criteria.age.min !== undefined) ageCondition.$gte = criteria.age.min;
//       if (criteria.age.max !== undefined) ageCondition.$lte = criteria.age.max;
//       if (Object.keys(ageCondition).length > 0) {
//         conditions.push({ age: ageCondition });
//       }
//     }

//     if (criteria.gender) {
//       conditions.push({ gender: criteria.gender });
//     }

//     const query = {
//       selector: { $and: conditions },
//       sort: [{ last_name: 'asc' }, { first_name: 'asc' }]
//     };

//     return this.pouchService.findDocs<Patient>(query, this.DB_NAME);
//   }

//   /**
//    * 📊 GET PATIENT STATISTICS
//    */
//   async getPatientStats(): Promise<{
//     total: number;
//     byGender: { [key: string]: number };
//     byAgeGroup: { [key: string]: number };
//   }> {
//     try {
//       const patients = await this.getAllPatients().toPromise() || [];
      
//       const stats = {
//         total: patients.length,
//         byGender: {} as { [key: string]: number },
//         byAgeGroup: {} as { [key: string]: number }
//       };

//       patients.forEach(patient => {
//         // Gender statistics
//         if (patient.gender) {
//           stats.byGender[patient.gender] = (stats.byGender[patient.gender] || 0) + 1;
//         }

//         // Age group statistics
//         if (patient.age) {
//           let ageGroup = '';
//           if (patient.age < 18) ageGroup = 'Under 18';
//           else if (patient.age < 30) ageGroup = '18-29';
//           else if (patient.age < 50) ageGroup = '30-49';
//           else if (patient.age < 65) ageGroup = '50-64';
//           else ageGroup = '65+';

//           stats.byAgeGroup[ageGroup] = (stats.byAgeGroup[ageGroup] || 0) + 1;
//         }
//       });

//       return stats;
//     } catch (error) {
//       console.error('❌ Error getting patient statistics:', error);
//       throw error;
//     }
//   }

//   /**
//    * 🔄 BULK OPERATIONS
//    */
//   bulkCreatePatients(patients: Partial<Patient>[]): Observable<PouchDBResponse[]> {
//     const patientsWithIds = patients.map(patient => ({
//       ...patient,
//       _id: patient._id || this.pouchService.generateId('patient'),
//       type: 'patient' as const
//     }));

//     return this.pouchService.bulkDocs<Patient>(patientsWithIds as Patient[], this.DB_NAME);
//   }

//   /**
//    * 📤 EXPORT PATIENTS
//    */
//   async exportPatients(format: 'json' | 'csv' = 'json'): Promise<string> {
//     try {
//       const patients = await this.getAllPatients().toPromise() || [];
      
//       if (format === 'json') {
//         return JSON.stringify(patients, null, 2);
//       } else {
//         // CSV format
//         if (patients.length === 0) return '';
        
//         const headers = Object.keys(patients[0]).filter(key => !key.startsWith('_'));
//         const csvRows = [headers.join(',')];
        
//         patients.forEach(patient => {
//           const row = headers.map(header => {
//             const value = (patient as any)[header];
//             return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value || '';
//           });
//           csvRows.push(row.join(','));
//         });
        
//         return csvRows.join('\n');
//       }
//     } catch (error) {
//       // console.error('❌ Error exporting patients:', error);
//       throw error;
//     }
//   }

//   /**
//    * 🔄 SYNC with remote database
//    */
//   syncWithRemote(remoteUrl: string): Observable<any> {
//     return this.pouchService.syncWithRemote(this.DB_NAME, remoteUrl, {
//       live: true,
//       retry: true
//     });
//   }

//   /**
//    * 📊 GET DATABASE INFO
//    */
//   getDatabaseInfo(): Observable<any> {
//     return this.pouchService.getDatabaseInfo(this.DB_NAME);
//   }

//   /**
//    * 🧹 COMPACT DATABASE
//    */
//   compactDatabase(): Observable<any> {
//     return this.pouchService.compactDatabase(this.DB_NAME);
//   }
// }
