{"version": 3, "sources": ["../../../../../../node_modules/vuvuzela/index.js", "../../../../../../node_modules/pouchdb-browser/lib/index.es.js"], "sourcesContent": ["'use strict';\n\n/**\n * Stringify/parse functions that don't operate\n * recursively, so they avoid call stack exceeded\n * errors.\n */\nexports.stringify = function stringify(input) {\n  var queue = [];\n  queue.push({obj: input});\n\n  var res = '';\n  var next, obj, prefix, val, i, arrayPrefix, keys, k, key, value, objPrefix;\n  while ((next = queue.pop())) {\n    obj = next.obj;\n    prefix = next.prefix || '';\n    val = next.val || '';\n    res += prefix;\n    if (val) {\n      res += val;\n    } else if (typeof obj !== 'object') {\n      res += typeof obj === 'undefined' ? null : JSON.stringify(obj);\n    } else if (obj === null) {\n      res += 'null';\n    } else if (Array.isArray(obj)) {\n      queue.push({val: ']'});\n      for (i = obj.length - 1; i >= 0; i--) {\n        arrayPrefix = i === 0 ? '' : ',';\n        queue.push({obj: obj[i], prefix: arrayPrefix});\n      }\n      queue.push({val: '['});\n    } else { // object\n      keys = [];\n      for (k in obj) {\n        if (obj.hasOwnProperty(k)) {\n          keys.push(k);\n        }\n      }\n      queue.push({val: '}'});\n      for (i = keys.length - 1; i >= 0; i--) {\n        key = keys[i];\n        value = obj[key];\n        objPrefix = (i > 0 ? ',' : '');\n        objPrefix += JSON.stringify(key) + ':';\n        queue.push({obj: value, prefix: objPrefix});\n      }\n      queue.push({val: '{'});\n    }\n  }\n  return res;\n};\n\n// Convenience function for the parse function.\n// This pop function is basically copied from\n// pouchCollate.parseIndexableString\nfunction pop(obj, stack, metaStack) {\n  var lastMetaElement = metaStack[metaStack.length - 1];\n  if (obj === lastMetaElement.element) {\n    // popping a meta-element, e.g. an object whose value is another object\n    metaStack.pop();\n    lastMetaElement = metaStack[metaStack.length - 1];\n  }\n  var element = lastMetaElement.element;\n  var lastElementIndex = lastMetaElement.index;\n  if (Array.isArray(element)) {\n    element.push(obj);\n  } else if (lastElementIndex === stack.length - 2) { // obj with key+value\n    var key = stack.pop();\n    element[key] = obj;\n  } else {\n    stack.push(obj); // obj with key only\n  }\n}\n\nexports.parse = function (str) {\n  var stack = [];\n  var metaStack = []; // stack for arrays and objects\n  var i = 0;\n  var collationIndex,parsedNum,numChar;\n  var parsedString,lastCh,numConsecutiveSlashes,ch;\n  var arrayElement, objElement;\n  while (true) {\n    collationIndex = str[i++];\n    if (collationIndex === '}' ||\n        collationIndex === ']' ||\n        typeof collationIndex === 'undefined') {\n      if (stack.length === 1) {\n        return stack.pop();\n      } else {\n        pop(stack.pop(), stack, metaStack);\n        continue;\n      }\n    }\n    switch (collationIndex) {\n      case ' ':\n      case '\\t':\n      case '\\n':\n      case ':':\n      case ',':\n        break;\n      case 'n':\n        i += 3; // 'ull'\n        pop(null, stack, metaStack);\n        break;\n      case 't':\n        i += 3; // 'rue'\n        pop(true, stack, metaStack);\n        break;\n      case 'f':\n        i += 4; // 'alse'\n        pop(false, stack, metaStack);\n        break;\n      case '0':\n      case '1':\n      case '2':\n      case '3':\n      case '4':\n      case '5':\n      case '6':\n      case '7':\n      case '8':\n      case '9':\n      case '-':\n        parsedNum = '';\n        i--;\n        while (true) {\n          numChar = str[i++];\n          if (/[\\d\\.\\-e\\+]/.test(numChar)) {\n            parsedNum += numChar;\n          } else {\n            i--;\n            break;\n          }\n        }\n        pop(parseFloat(parsedNum), stack, metaStack);\n        break;\n      case '\"':\n        parsedString = '';\n        lastCh = void 0;\n        numConsecutiveSlashes = 0;\n        while (true) {\n          ch = str[i++];\n          if (ch !== '\"' || (lastCh === '\\\\' &&\n              numConsecutiveSlashes % 2 === 1)) {\n            parsedString += ch;\n            lastCh = ch;\n            if (lastCh === '\\\\') {\n              numConsecutiveSlashes++;\n            } else {\n              numConsecutiveSlashes = 0;\n            }\n          } else {\n            break;\n          }\n        }\n        pop(JSON.parse('\"' + parsedString + '\"'), stack, metaStack);\n        break;\n      case '[':\n        arrayElement = { element: [], index: stack.length };\n        stack.push(arrayElement.element);\n        metaStack.push(arrayElement);\n        break;\n      case '{':\n        objElement = { element: {}, index: stack.length };\n        stack.push(objElement.element);\n        metaStack.push(objElement);\n        break;\n      default:\n        throw new Error(\n          'unexpectedly reached end of input: ' + collationIndex);\n    }\n  }\n};\n", "import Md5 from 'spark-md5';\nimport { v4 } from 'uuid';\nimport vuvuzela from 'vuvuzela';\nimport EE from 'events';\n\nfunction isBinaryObject(object) {\n  return (typeof ArrayBuffer !== 'undefined' && object instanceof ArrayBuffer) ||\n    (typeof Blob !== 'undefined' && object instanceof Blob);\n}\n\n/**\n * @template {ArrayBuffer | Blob} T\n * @param {T} object\n * @returns {T}\n */\nfunction cloneBinaryObject(object) {\n  return object instanceof ArrayBuffer\n    ? object.slice(0)\n    : object.slice(0, object.size, object.type);\n}\n\n// most of this is borrowed from lodash.isPlainObject:\n// https://github.com/fis-components/lodash.isplainobject/\n// blob/29c358140a74f252aeb08c9eb28bef86f2217d4a/index.js\n\nvar funcToString = Function.prototype.toString;\nvar objectCtorString = funcToString.call(Object);\n\nfunction isPlainObject(value) {\n  var proto = Object.getPrototypeOf(value);\n  /* istanbul ignore if */\n  if (proto === null) { // not sure when this happens, but I guess it can\n    return true;\n  }\n  var Ctor = proto.constructor;\n  return (typeof Ctor == 'function' &&\n    Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString);\n}\n\nfunction clone(object) {\n  var newObject;\n  var i;\n  var len;\n\n  if (!object || typeof object !== 'object') {\n    return object;\n  }\n\n  if (Array.isArray(object)) {\n    newObject = [];\n    for (i = 0, len = object.length; i < len; i++) {\n      newObject[i] = clone(object[i]);\n    }\n    return newObject;\n  }\n\n  // special case: to avoid inconsistencies between IndexedDB\n  // and other backends, we automatically stringify Dates\n  if (object instanceof Date && isFinite(object)) {\n    return object.toISOString();\n  }\n\n  if (isBinaryObject(object)) {\n    return cloneBinaryObject(object);\n  }\n\n  if (!isPlainObject(object)) {\n    return object; // don't clone objects like Workers\n  }\n\n  newObject = {};\n  for (i in object) {\n    /* istanbul ignore else */\n    if (Object.prototype.hasOwnProperty.call(object, i)) {\n      var value = clone(object[i]);\n      if (typeof value !== 'undefined') {\n        newObject[i] = value;\n      }\n    }\n  }\n  return newObject;\n}\n\nfunction once(fun) {\n  var called = false;\n  return function (...args) {\n    /* istanbul ignore if */\n    if (called) {\n      // this is a smoke test and should never actually happen\n      throw new Error('once called more than once');\n    } else {\n      called = true;\n      fun.apply(this, args);\n    }\n  };\n}\n\nfunction toPromise(func) {\n  //create the function we will be returning\n  return function (...args) {\n    // Clone arguments\n    args = clone(args);\n    var self = this;\n    // if the last argument is a function, assume its a callback\n    var usedCB = (typeof args[args.length - 1] === 'function') ? args.pop() : false;\n    var promise = new Promise(function (fulfill, reject) {\n      var resp;\n      try {\n        var callback = once(function (err, mesg) {\n          if (err) {\n            reject(err);\n          } else {\n            fulfill(mesg);\n          }\n        });\n        // create a callback for this invocation\n        // apply the function in the orig context\n        args.push(callback);\n        resp = func.apply(self, args);\n        if (resp && typeof resp.then === 'function') {\n          fulfill(resp);\n        }\n      } catch (e) {\n        reject(e);\n      }\n    });\n    // if there is a callback, call it back\n    if (usedCB) {\n      promise.then(function (result) {\n        usedCB(null, result);\n      }, usedCB);\n    }\n    return promise;\n  };\n}\n\nfunction logApiCall(self, name, args) {\n  /* istanbul ignore if */\n  if (self.constructor.listeners('debug').length) {\n    var logArgs = ['api', self.name, name];\n    for (var i = 0; i < args.length - 1; i++) {\n      logArgs.push(args[i]);\n    }\n    self.constructor.emit('debug', logArgs);\n\n    // override the callback itself to log the response\n    var origCallback = args[args.length - 1];\n    args[args.length - 1] = function (err, res) {\n      var responseArgs = ['api', self.name, name];\n      responseArgs = responseArgs.concat(\n        err ? ['error', err] : ['success', res]\n      );\n      self.constructor.emit('debug', responseArgs);\n      origCallback(err, res);\n    };\n  }\n}\n\nfunction adapterFun(name, callback) {\n  return toPromise(function (...args) {\n    if (this._closed) {\n      return Promise.reject(new Error('database is closed'));\n    }\n    if (this._destroyed) {\n      return Promise.reject(new Error('database is destroyed'));\n    }\n    var self = this;\n    logApiCall(self, name, args);\n    if (!this.taskqueue.isReady) {\n      return new Promise(function (fulfill, reject) {\n        self.taskqueue.addTask(function (failed) {\n          if (failed) {\n            reject(failed);\n          } else {\n            fulfill(self[name].apply(self, args));\n          }\n        });\n      });\n    }\n    return callback.apply(this, args);\n  });\n}\n\n// like underscore/lodash _.pick()\nfunction pick(obj, arr) {\n  var res = {};\n  for (var i = 0, len = arr.length; i < len; i++) {\n    var prop = arr[i];\n    if (prop in obj) {\n      res[prop] = obj[prop];\n    }\n  }\n  return res;\n}\n\n// Most browsers throttle concurrent requests at 6, so it's silly\n// to shim _bulk_get by trying to launch potentially hundreds of requests\n// and then letting the majority time out. We can handle this ourselves.\nvar MAX_NUM_CONCURRENT_REQUESTS = 6;\n\nfunction identityFunction(x) {\n  return x;\n}\n\nfunction formatResultForOpenRevsGet(result) {\n  return [{\n    ok: result\n  }];\n}\n\n// shim for P/CouchDB adapters that don't directly implement _bulk_get\nfunction bulkGet(db, opts, callback) {\n  var requests = opts.docs;\n\n  // consolidate into one request per doc if possible\n  var requestsById = new Map();\n  requests.forEach(function (request) {\n    if (requestsById.has(request.id)) {\n      requestsById.get(request.id).push(request);\n    } else {\n      requestsById.set(request.id, [request]);\n    }\n  });\n\n  var numDocs = requestsById.size;\n  var numDone = 0;\n  var perDocResults = new Array(numDocs);\n\n  function collapseResultsAndFinish() {\n    var results = [];\n    perDocResults.forEach(function (res) {\n      res.docs.forEach(function (info) {\n        results.push({\n          id: res.id,\n          docs: [info]\n        });\n      });\n    });\n    callback(null, {results});\n  }\n\n  function checkDone() {\n    if (++numDone === numDocs) {\n      collapseResultsAndFinish();\n    }\n  }\n\n  function gotResult(docIndex, id, docs) {\n    perDocResults[docIndex] = {id, docs};\n    checkDone();\n  }\n\n  var allRequests = [];\n  requestsById.forEach(function (value, key) {\n    allRequests.push(key);\n  });\n\n  var i = 0;\n\n  function nextBatch() {\n\n    if (i >= allRequests.length) {\n      return;\n    }\n\n    var upTo = Math.min(i + MAX_NUM_CONCURRENT_REQUESTS, allRequests.length);\n    var batch = allRequests.slice(i, upTo);\n    processBatch(batch, i);\n    i += batch.length;\n  }\n\n  function processBatch(batch, offset) {\n    batch.forEach(function (docId, j) {\n      var docIdx = offset + j;\n      var docRequests = requestsById.get(docId);\n\n      // just use the first request as the \"template\"\n      // TODO: The _bulk_get API allows for more subtle use cases than this,\n      // but for now it is unlikely that there will be a mix of different\n      // \"atts_since\" or \"attachments\" in the same request, since it's just\n      // replicate.js that is using this for the moment.\n      // Also, atts_since is aspirational, since we don't support it yet.\n      var docOpts = pick(docRequests[0], ['atts_since', 'attachments']);\n      docOpts.open_revs = docRequests.map(function (request) {\n        // rev is optional, open_revs disallowed\n        return request.rev;\n      });\n\n      // remove falsey / undefined revisions\n      docOpts.open_revs = docOpts.open_revs.filter(identityFunction);\n\n      var formatResult = identityFunction;\n\n      if (docOpts.open_revs.length === 0) {\n        delete docOpts.open_revs;\n\n        // when fetching only the \"winning\" leaf,\n        // transform the result so it looks like an open_revs\n        // request\n        formatResult = formatResultForOpenRevsGet;\n      }\n\n      // globally-supplied options\n      ['revs', 'attachments', 'binary', 'ajax', 'latest'].forEach(function (param) {\n        if (param in opts) {\n          docOpts[param] = opts[param];\n        }\n      });\n      db.get(docId, docOpts, function (err, res) {\n        var result;\n        /* istanbul ignore if */\n        if (err) {\n          result = [{error: err}];\n        } else {\n          result = formatResult(res);\n        }\n        gotResult(docIdx, docId, result);\n        nextBatch();\n      });\n    });\n  }\n\n  nextBatch();\n\n}\n\nvar hasLocal;\n\ntry {\n  localStorage.setItem('_pouch_check_localstorage', 1);\n  hasLocal = !!localStorage.getItem('_pouch_check_localstorage');\n} catch (e) {\n  hasLocal = false;\n}\n\nfunction hasLocalStorage() {\n  return hasLocal;\n}\n\nconst nextTick = typeof queueMicrotask === \"function\"\n  ? queueMicrotask\n  : function nextTick(fn) {\n    Promise.resolve().then(fn);\n  };\n\nclass Changes extends EE {\n  constructor() {\n    super();\n\n    this._listeners = {};\n\n    if (hasLocalStorage()) {\n      addEventListener(\"storage\", (e) => {\n        this.emit(e.key);\n      });\n    }\n  }\n\n  addListener(dbName, id, db, opts) {\n    if (this._listeners[id]) {\n      return;\n    }\n    var inprogress = false;\n    var self = this;\n    function eventFunction() {\n      if (!self._listeners[id]) {\n        return;\n      }\n      if (inprogress) {\n        inprogress = 'waiting';\n        return;\n      }\n      inprogress = true;\n      var changesOpts = pick(opts, [\n        'style', 'include_docs', 'attachments', 'conflicts', 'filter',\n        'doc_ids', 'view', 'since', 'query_params', 'binary', 'return_docs'\n      ]);\n\n      function onError() {\n        inprogress = false;\n      }\n\n      db.changes(changesOpts).on('change', function (c) {\n        if (c.seq > opts.since && !opts.cancelled) {\n          opts.since = c.seq;\n          opts.onChange(c);\n        }\n      }).on('complete', function () {\n        if (inprogress === 'waiting') {\n          nextTick(eventFunction);\n        }\n        inprogress = false;\n      }).on('error', onError);\n    }\n    this._listeners[id] = eventFunction;\n    this.on(dbName, eventFunction);\n  }\n\n  removeListener(dbName, id) {\n    if (!(id in this._listeners)) {\n      return;\n    }\n    super.removeListener(dbName, this._listeners[id]);\n    delete this._listeners[id];\n  }\n\n  notifyLocalWindows(dbName) {\n    //do a useless change on a storage thing\n    //in order to get other windows's listeners to activate\n    if (hasLocalStorage()) {\n      localStorage[dbName] = (localStorage[dbName] === \"a\") ? \"b\" : \"a\";\n    }\n  }\n\n  notify(dbName) {\n    this.emit(dbName);\n    this.notifyLocalWindows(dbName);\n  }\n}\n\nfunction guardedConsole(method) {\n  /* istanbul ignore else */\n  if (typeof console !== 'undefined' && typeof console[method] === 'function') {\n    var args = Array.prototype.slice.call(arguments, 1);\n    console[method].apply(console, args);\n  }\n}\n\nfunction randomNumber(min, max) {\n  var maxTimeout = 600000; // Hard-coded default of 10 minutes\n  min = parseInt(min, 10) || 0;\n  max = parseInt(max, 10);\n  if (max !== max || max <= min) {\n    max = (min || 1) << 1; //doubling\n  } else {\n    max = max + 1;\n  }\n  // In order to not exceed maxTimeout, pick a random value between half of maxTimeout and maxTimeout\n  if (max > maxTimeout) {\n    min = maxTimeout >> 1; // divide by two\n    max = maxTimeout;\n  }\n  var ratio = Math.random();\n  var range = max - min;\n\n  return ~~(range * ratio + min); // ~~ coerces to an int, but fast.\n}\n\nfunction defaultBackOff(min) {\n  var max = 0;\n  if (!min) {\n    max = 2000;\n  }\n  return randomNumber(min, max);\n}\n\n// designed to give info to browser users, who are disturbed\n// when they see http errors in the console\nfunction explainError(status, str) {\n  guardedConsole('info', 'The above ' + status + ' is totally normal. ' + str);\n}\n\nclass PouchError extends Error {\n  constructor(status, error, reason) {\n    super();\n    this.status = status;\n    this.name = error;\n    this.message = reason;\n    this.error = true;\n  }\n\n  toString() {\n    return JSON.stringify({\n      status: this.status,\n      name: this.name,\n      message: this.message,\n      reason: this.reason\n    });\n  }\n}\n\nvar UNAUTHORIZED = new PouchError(401, 'unauthorized', \"Name or password is incorrect.\");\nvar MISSING_BULK_DOCS = new PouchError(400, 'bad_request', \"Missing JSON list of 'docs'\");\nvar MISSING_DOC = new PouchError(404, 'not_found', 'missing');\nvar REV_CONFLICT = new PouchError(409, 'conflict', 'Document update conflict');\nvar INVALID_ID = new PouchError(400, 'bad_request', '_id field must contain a string');\nvar MISSING_ID = new PouchError(412, 'missing_id', '_id is required for puts');\nvar RESERVED_ID = new PouchError(400, 'bad_request', 'Only reserved document ids may start with underscore.');\nvar NOT_OPEN = new PouchError(412, 'precondition_failed', 'Database not open');\nvar UNKNOWN_ERROR = new PouchError(500, 'unknown_error', 'Database encountered an unknown error');\nvar BAD_ARG = new PouchError(500, 'badarg', 'Some query argument is invalid');\nvar INVALID_REQUEST = new PouchError(400, 'invalid_request', 'Request was invalid');\nvar QUERY_PARSE_ERROR = new PouchError(400, 'query_parse_error', 'Some query parameter is invalid');\nvar DOC_VALIDATION = new PouchError(500, 'doc_validation', 'Bad special document member');\nvar BAD_REQUEST = new PouchError(400, 'bad_request', 'Something wrong with the request');\nvar NOT_AN_OBJECT = new PouchError(400, 'bad_request', 'Document must be a JSON object');\nvar DB_MISSING = new PouchError(404, 'not_found', 'Database not found');\nvar IDB_ERROR = new PouchError(500, 'indexed_db_went_bad', 'unknown');\nvar WSQ_ERROR = new PouchError(500, 'web_sql_went_bad', 'unknown');\nvar LDB_ERROR = new PouchError(500, 'levelDB_went_went_bad', 'unknown');\nvar FORBIDDEN = new PouchError(403, 'forbidden', 'Forbidden by design doc validate_doc_update function');\nvar INVALID_REV = new PouchError(400, 'bad_request', 'Invalid rev format');\nvar FILE_EXISTS = new PouchError(412, 'file_exists', 'The database could not be created, the file already exists.');\nvar MISSING_STUB = new PouchError(412, 'missing_stub', 'A pre-existing attachment stub wasn\\'t found');\nvar INVALID_URL = new PouchError(413, 'invalid_url', 'Provided URL is invalid');\n\nfunction createError(error, reason) {\n  function CustomPouchError(reason) {\n    // inherit error properties from our parent error manually\n    // so as to allow proper JSON parsing.\n    var names = Object.getOwnPropertyNames(error);\n    for (var i = 0, len = names.length; i < len; i++) {\n      if (typeof error[names[i]] !== 'function') {\n        this[names[i]] = error[names[i]];\n      }\n    }\n\n    if (this.stack === undefined) {\n      this.stack = (new Error()).stack;\n    }\n\n    if (reason !== undefined) {\n      this.reason = reason;\n    }\n  }\n  CustomPouchError.prototype = PouchError.prototype;\n  return new CustomPouchError(reason);\n}\n\nfunction generateErrorFromResponse(err) {\n\n  if (typeof err !== 'object') {\n    var data = err;\n    err = UNKNOWN_ERROR;\n    err.data = data;\n  }\n\n  if ('error' in err && err.error === 'conflict') {\n    err.name = 'conflict';\n    err.status = 409;\n  }\n\n  if (!('name' in err)) {\n    err.name = err.error || 'unknown';\n  }\n\n  if (!('status' in err)) {\n    err.status = 500;\n  }\n\n  if (!('message' in err)) {\n    err.message = err.message || err.reason;\n  }\n\n  if (!('stack' in err)) {\n    err.stack = (new Error()).stack;\n  }\n\n  return err;\n}\n\nfunction tryFilter(filter, doc, req) {\n  try {\n    return !filter(doc, req);\n  } catch (err) {\n    var msg = 'Filter function threw: ' + err.toString();\n    return createError(BAD_REQUEST, msg);\n  }\n}\n\nfunction filterChange(opts) {\n  var req = {};\n  var hasFilter = opts.filter && typeof opts.filter === 'function';\n  req.query = opts.query_params;\n\n  return function filter(change) {\n    if (!change.doc) {\n      // CSG sends events on the changes feed that don't have documents,\n      // this hack makes a whole lot of existing code robust.\n      change.doc = {};\n    }\n\n    var filterReturn = hasFilter && tryFilter(opts.filter, change.doc, req);\n\n    if (typeof filterReturn === 'object') {\n      return filterReturn;\n    }\n\n    if (filterReturn) {\n      return false;\n    }\n\n    if (!opts.include_docs) {\n      delete change.doc;\n    } else if (!opts.attachments) {\n      for (var att in change.doc._attachments) {\n        /* istanbul ignore else */\n        if (Object.prototype.hasOwnProperty.call(change.doc._attachments, att)) {\n          change.doc._attachments[att].stub = true;\n        }\n      }\n    }\n    return true;\n  };\n}\n\n// shim for Function.prototype.name,\n\n// Determine id an ID is valid\n//   - invalid IDs begin with an underescore that does not begin '_design' or\n//     '_local'\n//   - any other string value is a valid id\n// Returns the specific error object for each case\nfunction invalidIdError(id) {\n  var err;\n  if (!id) {\n    err = createError(MISSING_ID);\n  } else if (typeof id !== 'string') {\n    err = createError(INVALID_ID);\n  } else if (/^_/.test(id) && !(/^_(design|local)/).test(id)) {\n    err = createError(RESERVED_ID);\n  }\n  if (err) {\n    throw err;\n  }\n}\n\n// Checks if a PouchDB object is \"remote\" or not. This is\n\nfunction isRemote(db) {\n  if (typeof db._remote === 'boolean') {\n    return db._remote;\n  }\n  /* istanbul ignore next */\n  if (typeof db.type === 'function') {\n    guardedConsole('warn',\n      'db.type() is deprecated and will be removed in ' +\n      'a future version of PouchDB');\n    return db.type() === 'http';\n  }\n  /* istanbul ignore next */\n  return false;\n}\n\nfunction listenerCount(ee, type) {\n  return 'listenerCount' in ee ? ee.listenerCount(type) :\n                                 EE.listenerCount(ee, type);\n}\n\nfunction parseDesignDocFunctionName(s) {\n  if (!s) {\n    return null;\n  }\n  var parts = s.split('/');\n  if (parts.length === 2) {\n    return parts;\n  }\n  if (parts.length === 1) {\n    return [s, s];\n  }\n  return null;\n}\n\nfunction normalizeDesignDocFunctionName(s) {\n  var normalized = parseDesignDocFunctionName(s);\n  return normalized ? normalized.join('/') : null;\n}\n\n// originally parseUri 1.2.2, now patched by us\n// (c) Steven Levithan <stevenlevithan.com>\n// MIT License\nvar keys = [\"source\", \"protocol\", \"authority\", \"userInfo\", \"user\", \"password\",\n    \"host\", \"port\", \"relative\", \"path\", \"directory\", \"file\", \"query\", \"anchor\"];\nvar qName =\"queryKey\";\nvar qParser = /(?:^|&)([^&=]*)=?([^&]*)/g;\n\n// use the \"loose\" parser\n/* eslint no-useless-escape: 0 */\nvar parser = /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nfunction parseUri(str) {\n  var m = parser.exec(str);\n  var uri = {};\n  var i = 14;\n\n  while (i--) {\n    var key = keys[i];\n    var value = m[i] || \"\";\n    var encoded = ['user', 'password'].indexOf(key) !== -1;\n    uri[key] = encoded ? decodeURIComponent(value) : value;\n  }\n\n  uri[qName] = {};\n  uri[keys[12]].replace(qParser, function ($0, $1, $2) {\n    if ($1) {\n      uri[qName][$1] = $2;\n    }\n  });\n\n  return uri;\n}\n\n// Based on https://github.com/alexdavid/scope-eval v0.0.3\n// (source: https://unpkg.com/scope-eval@0.0.3/scope_eval.js)\n// This is basically just a wrapper around new Function()\n\nfunction scopeEval(source, scope) {\n  var keys = [];\n  var values = [];\n  for (var key in scope) {\n    if (Object.prototype.hasOwnProperty.call(scope, key)) {\n      keys.push(key);\n      values.push(scope[key]);\n    }\n  }\n  keys.push(source);\n  return Function.apply(null, keys).apply(null, values);\n}\n\n// this is essentially the \"update sugar\" function from daleharvey/pouchdb#1388\n// the diffFun tells us what delta to apply to the doc.  it either returns\n// the doc, or false if it doesn't need to do an update after all\nfunction upsert(db, docId, diffFun) {\n  return db.get(docId)\n    .catch(function (err) {\n      /* istanbul ignore next */\n      if (err.status !== 404) {\n        throw err;\n      }\n      return {};\n    })\n    .then(function (doc) {\n      // the user might change the _rev, so save it for posterity\n      var docRev = doc._rev;\n      var newDoc = diffFun(doc);\n\n      if (!newDoc) {\n        // if the diffFun returns falsy, we short-circuit as\n        // an optimization\n        return {updated: false, rev: docRev};\n      }\n\n      // users aren't allowed to modify these values,\n      // so reset them here\n      newDoc._id = docId;\n      newDoc._rev = docRev;\n      return tryAndPut(db, newDoc, diffFun);\n    });\n}\n\nfunction tryAndPut(db, doc, diffFun) {\n  return db.put(doc).then(function (res) {\n    return {\n      updated: true,\n      rev: res.rev\n    };\n  }, function (err) {\n    /* istanbul ignore next */\n    if (err.status !== 409) {\n      throw err;\n    }\n    return upsert(db, doc._id, diffFun);\n  });\n}\n\nvar thisAtob = function (str) {\n  return atob(str);\n};\n\nvar thisBtoa = function (str) {\n  return btoa(str);\n};\n\n// Abstracts constructing a Blob object, so it also works in older\n// browsers that don't support the native Blob constructor (e.g.\n// old QtWebKit versions, Android < 4.4).\nfunction createBlob(parts, properties) {\n  /* global BlobBuilder,MSBlobBuilder,MozBlobBuilder,WebKitBlobBuilder */\n  parts = parts || [];\n  properties = properties || {};\n  try {\n    return new Blob(parts, properties);\n  } catch (e) {\n    if (e.name !== \"TypeError\") {\n      throw e;\n    }\n    var Builder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\n                  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\n                  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder :\n                  WebKitBlobBuilder;\n    var builder = new Builder();\n    for (var i = 0; i < parts.length; i += 1) {\n      builder.append(parts[i]);\n    }\n    return builder.getBlob(properties.type);\n  }\n}\n\n// From http://stackoverflow.com/questions/14967647/ (continues on next line)\n// encode-decode-image-with-base64-breaks-image (2013-04-21)\nfunction binaryStringToArrayBuffer(bin) {\n  var length = bin.length;\n  var buf = new ArrayBuffer(length);\n  var arr = new Uint8Array(buf);\n  for (var i = 0; i < length; i++) {\n    arr[i] = bin.charCodeAt(i);\n  }\n  return buf;\n}\n\nfunction binStringToBluffer(binString, type) {\n  return createBlob([binaryStringToArrayBuffer(binString)], {type});\n}\n\nfunction b64ToBluffer(b64, type) {\n  return binStringToBluffer(thisAtob(b64), type);\n}\n\n//Can't find original post, but this is close\n//http://stackoverflow.com/questions/6965107/ (continues on next line)\n//converting-between-strings-and-arraybuffers\nfunction arrayBufferToBinaryString(buffer) {\n  var binary = '';\n  var bytes = new Uint8Array(buffer);\n  var length = bytes.byteLength;\n  for (var i = 0; i < length; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return binary;\n}\n\n// shim for browsers that don't support it\nfunction readAsBinaryString(blob, callback) {\n  var reader = new FileReader();\n  var hasBinaryString = typeof reader.readAsBinaryString === 'function';\n  reader.onloadend = function (e) {\n    var result = e.target.result || '';\n    if (hasBinaryString) {\n      return callback(result);\n    }\n    callback(arrayBufferToBinaryString(result));\n  };\n  if (hasBinaryString) {\n    reader.readAsBinaryString(blob);\n  } else {\n    reader.readAsArrayBuffer(blob);\n  }\n}\n\nfunction blobToBinaryString(blobOrBuffer, callback) {\n  readAsBinaryString(blobOrBuffer, function (bin) {\n    callback(bin);\n  });\n}\n\nfunction blobToBase64(blobOrBuffer, callback) {\n  blobToBinaryString(blobOrBuffer, function (base64) {\n    callback(thisBtoa(base64));\n  });\n}\n\n// simplified API. universal browser support is assumed\nfunction readAsArrayBuffer(blob, callback) {\n  var reader = new FileReader();\n  reader.onloadend = function (e) {\n    var result = e.target.result || new ArrayBuffer(0);\n    callback(result);\n  };\n  reader.readAsArrayBuffer(blob);\n}\n\n// this is not used in the browser\n\nvar setImmediateShim = self.setImmediate || self.setTimeout;\nvar MD5_CHUNK_SIZE = 32768;\n\nfunction rawToBase64(raw) {\n  return thisBtoa(raw);\n}\n\nfunction appendBlob(buffer, blob, start, end, callback) {\n  if (start > 0 || end < blob.size) {\n    // only slice blob if we really need to\n    blob = blob.slice(start, end);\n  }\n  readAsArrayBuffer(blob, function (arrayBuffer) {\n    buffer.append(arrayBuffer);\n    callback();\n  });\n}\n\nfunction appendString(buffer, string, start, end, callback) {\n  if (start > 0 || end < string.length) {\n    // only create a substring if we really need to\n    string = string.substring(start, end);\n  }\n  buffer.appendBinary(string);\n  callback();\n}\n\nfunction binaryMd5(data, callback) {\n  var inputIsString = typeof data === 'string';\n  var len = inputIsString ? data.length : data.size;\n  var chunkSize = Math.min(MD5_CHUNK_SIZE, len);\n  var chunks = Math.ceil(len / chunkSize);\n  var currentChunk = 0;\n  var buffer = inputIsString ? new Md5() : new Md5.ArrayBuffer();\n\n  var append = inputIsString ? appendString : appendBlob;\n\n  function next() {\n    setImmediateShim(loadNextChunk);\n  }\n\n  function done() {\n    var raw = buffer.end(true);\n    var base64 = rawToBase64(raw);\n    callback(base64);\n    buffer.destroy();\n  }\n\n  function loadNextChunk() {\n    var start = currentChunk * chunkSize;\n    var end = start + chunkSize;\n    currentChunk++;\n    if (currentChunk < chunks) {\n      append(buffer, data, start, end, next);\n    } else {\n      append(buffer, data, start, end, done);\n    }\n  }\n  loadNextChunk();\n}\n\nfunction stringMd5(string) {\n  return Md5.hash(string);\n}\n\n/**\n * Creates a new revision string that does NOT include the revision height\n * For example '56649f1b0506c6ca9fda0746eb0cacdf'\n */\nfunction rev(doc, deterministic_revs) {\n  if (!deterministic_revs) {\n    return v4().replace(/-/g, '').toLowerCase();\n  }\n\n  var mutateableDoc = Object.assign({}, doc);\n  delete mutateableDoc._rev_tree;\n  return stringMd5(JSON.stringify(mutateableDoc));\n}\n\nvar uuid = v4; // mimic old import, only v4 is ever used elsewhere\n\n// We fetch all leafs of the revision tree, and sort them based on tree length\n// and whether they were deleted, undeleted documents with the longest revision\n// tree (most edits) win\n// The final sort algorithm is slightly documented in a sidebar here:\n// http://guide.couchdb.org/draft/conflicts.html\nfunction winningRev(metadata) {\n  var winningId;\n  var winningPos;\n  var winningDeleted;\n  var toVisit = metadata.rev_tree.slice();\n  var node;\n  while ((node = toVisit.pop())) {\n    var tree = node.ids;\n    var branches = tree[2];\n    var pos = node.pos;\n    if (branches.length) { // non-leaf\n      for (var i = 0, len = branches.length; i < len; i++) {\n        toVisit.push({pos: pos + 1, ids: branches[i]});\n      }\n      continue;\n    }\n    var deleted = !!tree[1].deleted;\n    var id = tree[0];\n    // sort by deleted, then pos, then id\n    if (!winningId || (winningDeleted !== deleted ? winningDeleted :\n        winningPos !== pos ? winningPos < pos : winningId < id)) {\n      winningId = id;\n      winningPos = pos;\n      winningDeleted = deleted;\n    }\n  }\n\n  return winningPos + '-' + winningId;\n}\n\n// Pretty much all below can be combined into a higher order function to\n// traverse revisions\n// The return value from the callback will be passed as context to all\n// children of that node\nfunction traverseRevTree(revs, callback) {\n  var toVisit = revs.slice();\n\n  var node;\n  while ((node = toVisit.pop())) {\n    var pos = node.pos;\n    var tree = node.ids;\n    var branches = tree[2];\n    var newCtx =\n      callback(branches.length === 0, pos, tree[0], node.ctx, tree[1]);\n    for (var i = 0, len = branches.length; i < len; i++) {\n      toVisit.push({pos: pos + 1, ids: branches[i], ctx: newCtx});\n    }\n  }\n}\n\nfunction sortByPos(a, b) {\n  return a.pos - b.pos;\n}\n\nfunction collectLeaves(revs) {\n  var leaves = [];\n  traverseRevTree(revs, function (isLeaf, pos, id, acc, opts) {\n    if (isLeaf) {\n      leaves.push({rev: pos + \"-\" + id, pos, opts});\n    }\n  });\n  leaves.sort(sortByPos).reverse();\n  for (var i = 0, len = leaves.length; i < len; i++) {\n    delete leaves[i].pos;\n  }\n  return leaves;\n}\n\n// returns revs of all conflicts that is leaves such that\n// 1. are not deleted and\n// 2. are different than winning revision\nfunction collectConflicts(metadata) {\n  var win = winningRev(metadata);\n  var leaves = collectLeaves(metadata.rev_tree);\n  var conflicts = [];\n  for (var i = 0, len = leaves.length; i < len; i++) {\n    var leaf = leaves[i];\n    if (leaf.rev !== win && !leaf.opts.deleted) {\n      conflicts.push(leaf.rev);\n    }\n  }\n  return conflicts;\n}\n\n// compact a tree by marking its non-leafs as missing,\n// and return a list of revs to delete\nfunction compactTree(metadata) {\n  var revs = [];\n  traverseRevTree(metadata.rev_tree, function (isLeaf, pos,\n                                               revHash, ctx, opts) {\n    if (opts.status === 'available' && !isLeaf) {\n      revs.push(pos + '-' + revHash);\n      opts.status = 'missing';\n    }\n  });\n  return revs;\n}\n\n// `findPathToLeaf()` returns an array of revs that goes from the specified\n// leaf rev to the root of that leaf’s branch.\n//\n// eg. for this rev tree:\n// 1-9692 ▶ 2-37aa ▶ 3-df22 ▶ 4-6e94 ▶ 5-df4a ▶ 6-6a3a ▶ 7-57e5\n//          ┃                 ┗━━━━━━▶ 5-8d8c ▶ 6-65e0\n//          ┗━━━━━━▶ 3-43f6 ▶ 4-a3b4\n//\n// For a `targetRev` of '7-57e5', `findPathToLeaf()` would return ['7-57e5', '6-6a3a', '5-df4a']\n// The `revs` argument has the same structure as what `revs_tree` has on e.g.\n// the IndexedDB representation of the rev tree datastructure. Please refer to\n// tests/unit/test.purge.js for examples of what these look like.\n//\n// This function will throw an error if:\n// - The requested revision does not exist\n// - The requested revision is not a leaf\nfunction findPathToLeaf(revs, targetRev) {\n  let path = [];\n  const toVisit = revs.slice();\n\n  let node;\n  while ((node = toVisit.pop())) {\n    const { pos, ids: tree } = node;\n    const rev = `${pos}-${tree[0]}`;\n    const branches = tree[2];\n\n    // just assuming we're already working on the path up towards our desired leaf.\n    path.push(rev);\n\n    // we've reached the leaf of our dreams, so return the computed path.\n    if (rev === targetRev) {\n      //…unleeeeess\n      if (branches.length !== 0) {\n        throw new Error('The requested revision is not a leaf');\n      }\n      return path.reverse();\n    }\n\n    // this is based on the assumption that after we have a leaf (`branches.length == 0`), we handle the next\n    // branch. this is true for all branches other than the path leading to the winning rev (which is 7-57e5 in\n    // the example above. i've added a reset condition for branching nodes (`branches.length > 1`) as well.\n    if (branches.length === 0 || branches.length > 1) {\n      path = [];\n    }\n\n    // as a next step, we push the branches of this node to `toVisit` for visiting it during the next iteration\n    for (let i = 0, len = branches.length; i < len; i++) {\n      toVisit.push({ pos: pos + 1, ids: branches[i] });\n    }\n  }\n  if (path.length === 0) {\n    throw new Error('The requested revision does not exist');\n  }\n  return path.reverse();\n}\n\n// build up a list of all the paths to the leafs in this revision tree\nfunction rootToLeaf(revs) {\n  var paths = [];\n  var toVisit = revs.slice();\n  var node;\n  while ((node = toVisit.pop())) {\n    var pos = node.pos;\n    var tree = node.ids;\n    var id = tree[0];\n    var opts = tree[1];\n    var branches = tree[2];\n    var isLeaf = branches.length === 0;\n\n    var history = node.history ? node.history.slice() : [];\n    history.push({id, opts});\n    if (isLeaf) {\n      paths.push({pos: (pos + 1 - history.length), ids: history});\n    }\n    for (var i = 0, len = branches.length; i < len; i++) {\n      toVisit.push({pos: pos + 1, ids: branches[i], history});\n    }\n  }\n  return paths.reverse();\n}\n\n// for a better overview of what this is doing, read:\n\nfunction sortByPos$1(a, b) {\n  return a.pos - b.pos;\n}\n\n// classic binary search\nfunction binarySearch(arr, item, comparator) {\n  var low = 0;\n  var high = arr.length;\n  var mid;\n  while (low < high) {\n    mid = (low + high) >>> 1;\n    if (comparator(arr[mid], item) < 0) {\n      low = mid + 1;\n    } else {\n      high = mid;\n    }\n  }\n  return low;\n}\n\n// assuming the arr is sorted, insert the item in the proper place\nfunction insertSorted(arr, item, comparator) {\n  var idx = binarySearch(arr, item, comparator);\n  arr.splice(idx, 0, item);\n}\n\n// Turn a path as a flat array into a tree with a single branch.\n// If any should be stemmed from the beginning of the array, that's passed\n// in as the second argument\nfunction pathToTree(path, numStemmed) {\n  var root;\n  var leaf;\n  for (var i = numStemmed, len = path.length; i < len; i++) {\n    var node = path[i];\n    var currentLeaf = [node.id, node.opts, []];\n    if (leaf) {\n      leaf[2].push(currentLeaf);\n      leaf = currentLeaf;\n    } else {\n      root = leaf = currentLeaf;\n    }\n  }\n  return root;\n}\n\n// compare the IDs of two trees\nfunction compareTree(a, b) {\n  return a[0] < b[0] ? -1 : 1;\n}\n\n// Merge two trees together\n// The roots of tree1 and tree2 must be the same revision\nfunction mergeTree(in_tree1, in_tree2) {\n  var queue = [{tree1: in_tree1, tree2: in_tree2}];\n  var conflicts = false;\n  while (queue.length > 0) {\n    var item = queue.pop();\n    var tree1 = item.tree1;\n    var tree2 = item.tree2;\n\n    if (tree1[1].status || tree2[1].status) {\n      tree1[1].status =\n        (tree1[1].status ===  'available' ||\n        tree2[1].status === 'available') ? 'available' : 'missing';\n    }\n\n    for (var i = 0; i < tree2[2].length; i++) {\n      if (!tree1[2][0]) {\n        conflicts = 'new_leaf';\n        tree1[2][0] = tree2[2][i];\n        continue;\n      }\n\n      var merged = false;\n      for (var j = 0; j < tree1[2].length; j++) {\n        if (tree1[2][j][0] === tree2[2][i][0]) {\n          queue.push({tree1: tree1[2][j], tree2: tree2[2][i]});\n          merged = true;\n        }\n      }\n      if (!merged) {\n        conflicts = 'new_branch';\n        insertSorted(tree1[2], tree2[2][i], compareTree);\n      }\n    }\n  }\n  return {conflicts, tree: in_tree1};\n}\n\nfunction doMerge(tree, path, dontExpand) {\n  var restree = [];\n  var conflicts = false;\n  var merged = false;\n  var res;\n\n  if (!tree.length) {\n    return {tree: [path], conflicts: 'new_leaf'};\n  }\n\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var branch = tree[i];\n    if (branch.pos === path.pos && branch.ids[0] === path.ids[0]) {\n      // Paths start at the same position and have the same root, so they need\n      // merged\n      res = mergeTree(branch.ids, path.ids);\n      restree.push({pos: branch.pos, ids: res.tree});\n      conflicts = conflicts || res.conflicts;\n      merged = true;\n    } else if (dontExpand !== true) {\n      // The paths start at a different position, take the earliest path and\n      // traverse up until it as at the same point from root as the path we\n      // want to merge.  If the keys match we return the longer path with the\n      // other merged After stemming we don't want to expand the trees\n\n      var t1 = branch.pos < path.pos ? branch : path;\n      var t2 = branch.pos < path.pos ? path : branch;\n      var diff = t2.pos - t1.pos;\n\n      var candidateParents = [];\n\n      var trees = [];\n      trees.push({ids: t1.ids, diff, parent: null, parentIdx: null});\n      while (trees.length > 0) {\n        var item = trees.pop();\n        if (item.diff === 0) {\n          if (item.ids[0] === t2.ids[0]) {\n            candidateParents.push(item);\n          }\n          continue;\n        }\n        var elements = item.ids[2];\n        for (var j = 0, elementsLen = elements.length; j < elementsLen; j++) {\n          trees.push({\n            ids: elements[j],\n            diff: item.diff - 1,\n            parent: item.ids,\n            parentIdx: j\n          });\n        }\n      }\n\n      var el = candidateParents[0];\n\n      if (!el) {\n        restree.push(branch);\n      } else {\n        res = mergeTree(el.ids, t2.ids);\n        el.parent[2][el.parentIdx] = res.tree;\n        restree.push({pos: t1.pos, ids: t1.ids});\n        conflicts = conflicts || res.conflicts;\n        merged = true;\n      }\n    } else {\n      restree.push(branch);\n    }\n  }\n\n  // We didnt find\n  if (!merged) {\n    restree.push(path);\n  }\n\n  restree.sort(sortByPos$1);\n\n  return {\n    tree: restree,\n    conflicts: conflicts || 'internal_node'\n  };\n}\n\n// To ensure we don't grow the revision tree infinitely, we stem old revisions\nfunction stem(tree, depth) {\n  // First we break out the tree into a complete list of root to leaf paths\n  var paths = rootToLeaf(tree);\n  var stemmedRevs;\n\n  var result;\n  for (var i = 0, len = paths.length; i < len; i++) {\n    // Then for each path, we cut off the start of the path based on the\n    // `depth` to stem to, and generate a new set of flat trees\n    var path = paths[i];\n    var stemmed = path.ids;\n    var node;\n    if (stemmed.length > depth) {\n      // only do the stemming work if we actually need to stem\n      if (!stemmedRevs) {\n        stemmedRevs = {}; // avoid allocating this object unnecessarily\n      }\n      var numStemmed = stemmed.length - depth;\n      node = {\n        pos: path.pos + numStemmed,\n        ids: pathToTree(stemmed, numStemmed)\n      };\n\n      for (var s = 0; s < numStemmed; s++) {\n        var rev = (path.pos + s) + '-' + stemmed[s].id;\n        stemmedRevs[rev] = true;\n      }\n    } else { // no need to actually stem\n      node = {\n        pos: path.pos,\n        ids: pathToTree(stemmed, 0)\n      };\n    }\n\n    // Then we remerge all those flat trees together, ensuring that we don't\n    // connect trees that would go beyond the depth limit\n    if (result) {\n      result = doMerge(result, node, true).tree;\n    } else {\n      result = [node];\n    }\n  }\n\n  // this is memory-heavy per Chrome profiler, avoid unless we actually stemmed\n  if (stemmedRevs) {\n    traverseRevTree(result, function (isLeaf, pos, revHash) {\n      // some revisions may have been removed in a branch but not in another\n      delete stemmedRevs[pos + '-' + revHash];\n    });\n  }\n\n  return {\n    tree: result,\n    revs: stemmedRevs ? Object.keys(stemmedRevs) : []\n  };\n}\n\nfunction merge(tree, path, depth) {\n  var newTree = doMerge(tree, path);\n  var stemmed = stem(newTree.tree, depth);\n  return {\n    tree: stemmed.tree,\n    stemmedRevs: stemmed.revs,\n    conflicts: newTree.conflicts\n  };\n}\n\n// return true if a rev exists in the rev tree, false otherwise\nfunction revExists(revs, rev) {\n  var toVisit = revs.slice();\n  var splitRev = rev.split('-');\n  var targetPos = parseInt(splitRev[0], 10);\n  var targetId = splitRev[1];\n\n  var node;\n  while ((node = toVisit.pop())) {\n    if (node.pos === targetPos && node.ids[0] === targetId) {\n      return true;\n    }\n    var branches = node.ids[2];\n    for (var i = 0, len = branches.length; i < len; i++) {\n      toVisit.push({pos: node.pos + 1, ids: branches[i]});\n    }\n  }\n  return false;\n}\n\nfunction getTrees(node) {\n  return node.ids;\n}\n\n// check if a specific revision of a doc has been deleted\n//  - metadata: the metadata object from the doc store\n//  - rev: (optional) the revision to check. defaults to winning revision\nfunction isDeleted(metadata, rev) {\n  if (!rev) {\n    rev = winningRev(metadata);\n  }\n  var id = rev.substring(rev.indexOf('-') + 1);\n  var toVisit = metadata.rev_tree.map(getTrees);\n\n  var tree;\n  while ((tree = toVisit.pop())) {\n    if (tree[0] === id) {\n      return !!tree[1].deleted;\n    }\n    toVisit = toVisit.concat(tree[2]);\n  }\n}\n\nfunction isLocalId(id) {\n  return typeof id === 'string' && id.startsWith('_local/');\n}\n\n// returns the current leaf node for a given revision\nfunction latest(rev, metadata) {\n  var toVisit = metadata.rev_tree.slice();\n  var node;\n  while ((node = toVisit.pop())) {\n    var pos = node.pos;\n    var tree = node.ids;\n    var id = tree[0];\n    var opts = tree[1];\n    var branches = tree[2];\n    var isLeaf = branches.length === 0;\n\n    var history = node.history ? node.history.slice() : [];\n    history.push({id, pos, opts});\n\n    if (isLeaf) {\n      for (var i = 0, len = history.length; i < len; i++) {\n        var historyNode = history[i];\n        var historyRev = historyNode.pos + '-' + historyNode.id;\n\n        if (historyRev === rev) {\n          // return the rev of this leaf\n          return pos + '-' + id;\n        }\n      }\n    }\n\n    for (var j = 0, l = branches.length; j < l; j++) {\n      toVisit.push({pos: pos + 1, ids: branches[j], history});\n    }\n  }\n\n  /* istanbul ignore next */\n  throw new Error('Unable to resolve latest revision for id ' + metadata.id + ', rev ' + rev);\n}\n\nfunction tryCatchInChangeListener(self, change, pending, lastSeq) {\n  // isolate try/catches to avoid V8 deoptimizations\n  try {\n    self.emit('change', change, pending, lastSeq);\n  } catch (e) {\n    guardedConsole('error', 'Error in .on(\"change\", function):', e);\n  }\n}\n\nfunction processChange(doc, metadata, opts) {\n  var changeList = [{rev: doc._rev}];\n  if (opts.style === 'all_docs') {\n    changeList = collectLeaves(metadata.rev_tree)\n    .map(function (x) { return {rev: x.rev}; });\n  }\n  var change = {\n    id: metadata.id,\n    changes: changeList,\n    doc\n  };\n\n  if (isDeleted(metadata, doc._rev)) {\n    change.deleted = true;\n  }\n  if (opts.conflicts) {\n    change.doc._conflicts = collectConflicts(metadata);\n    if (!change.doc._conflicts.length) {\n      delete change.doc._conflicts;\n    }\n  }\n  return change;\n}\n\nclass Changes$1 extends EE {\n  constructor(db, opts, callback) {\n    super();\n    this.db = db;\n    opts = opts ? clone(opts) : {};\n    var complete = opts.complete = once((err, resp) => {\n      if (err) {\n        if (listenerCount(this, 'error') > 0) {\n          this.emit('error', err);\n        }\n      } else {\n        this.emit('complete', resp);\n      }\n      this.removeAllListeners();\n      db.removeListener('destroyed', onDestroy);\n    });\n    if (callback) {\n      this.on('complete', function (resp) {\n        callback(null, resp);\n      });\n      this.on('error', callback);\n    }\n    const onDestroy = () => {\n      this.cancel();\n    };\n    db.once('destroyed', onDestroy);\n\n    opts.onChange = (change, pending, lastSeq) => {\n      /* istanbul ignore if */\n      if (this.isCancelled) {\n        return;\n      }\n      tryCatchInChangeListener(this, change, pending, lastSeq);\n    };\n\n    var promise = new Promise(function (fulfill, reject) {\n      opts.complete = function (err, res) {\n        if (err) {\n          reject(err);\n        } else {\n          fulfill(res);\n        }\n      };\n    });\n    this.once('cancel', function () {\n      db.removeListener('destroyed', onDestroy);\n      opts.complete(null, {status: 'cancelled'});\n    });\n    this.then = promise.then.bind(promise);\n    this['catch'] = promise['catch'].bind(promise);\n    this.then(function (result) {\n      complete(null, result);\n    }, complete);\n\n\n\n    if (!db.taskqueue.isReady) {\n      db.taskqueue.addTask((failed) => {\n        if (failed) {\n          opts.complete(failed);\n        } else if (this.isCancelled) {\n          this.emit('cancel');\n        } else {\n          this.validateChanges(opts);\n        }\n      });\n    } else {\n      this.validateChanges(opts);\n    }\n  }\n\n  cancel() {\n    this.isCancelled = true;\n    if (this.db.taskqueue.isReady) {\n      this.emit('cancel');\n    }\n  }\n\n  validateChanges(opts) {\n    var callback = opts.complete;\n\n    /* istanbul ignore else */\n    if (PouchDB._changesFilterPlugin) {\n      PouchDB._changesFilterPlugin.validate(opts, (err) => {\n        if (err) {\n          return callback(err);\n        }\n        this.doChanges(opts);\n      });\n    } else {\n      this.doChanges(opts);\n    }\n  }\n\n  doChanges(opts) {\n    var callback = opts.complete;\n\n    opts = clone(opts);\n    if ('live' in opts && !('continuous' in opts)) {\n      opts.continuous = opts.live;\n    }\n    opts.processChange = processChange;\n\n    if (opts.since === 'latest') {\n      opts.since = 'now';\n    }\n    if (!opts.since) {\n      opts.since = 0;\n    }\n    if (opts.since === 'now') {\n      this.db.info().then((info) => {\n        /* istanbul ignore if */\n        if (this.isCancelled) {\n          callback(null, {status: 'cancelled'});\n          return;\n        }\n        opts.since = info.update_seq;\n        this.doChanges(opts);\n      }, callback);\n      return;\n    }\n\n    /* istanbul ignore else */\n    if (PouchDB._changesFilterPlugin) {\n      PouchDB._changesFilterPlugin.normalize(opts);\n      if (PouchDB._changesFilterPlugin.shouldFilter(this, opts)) {\n        return PouchDB._changesFilterPlugin.filter(this, opts);\n      }\n    } else {\n      ['doc_ids', 'filter', 'selector', 'view'].forEach(function (key) {\n        if (key in opts) {\n          guardedConsole('warn',\n            'The \"' + key + '\" option was passed in to changes/replicate, ' +\n            'but pouchdb-changes-filter plugin is not installed, so it ' +\n            'was ignored. Please install the plugin to enable filtering.'\n          );\n        }\n      });\n    }\n\n    if (!('descending' in opts)) {\n      opts.descending = false;\n    }\n\n    // 0 and 1 should return 1 document\n    opts.limit = opts.limit === 0 ? 1 : opts.limit;\n    opts.complete = callback;\n    var newPromise = this.db._changes(opts);\n    /* istanbul ignore else */\n    if (newPromise && typeof newPromise.cancel === 'function') {\n      const cancel = this.cancel;\n      this.cancel = (...args) => {\n        newPromise.cancel();\n        cancel.apply(this, args);\n      };\n    }\n  }\n}\n\n/*\n * A generic pouch adapter\n */\n\n// Wrapper for functions that call the bulkdocs api with a single doc,\n// if the first result is an error, return an error\nfunction yankError(callback, docId) {\n  return function (err, results) {\n    if (err || (results[0] && results[0].error)) {\n      err = err || results[0];\n      err.docId = docId;\n      callback(err);\n    } else {\n      callback(null, results.length ? results[0]  : results);\n    }\n  };\n}\n\n// clean docs given to us by the user\nfunction cleanDocs(docs) {\n  for (var i = 0; i < docs.length; i++) {\n    var doc = docs[i];\n    if (doc._deleted) {\n      delete doc._attachments; // ignore atts for deleted docs\n    } else if (doc._attachments) {\n      // filter out extraneous keys from _attachments\n      var atts = Object.keys(doc._attachments);\n      for (var j = 0; j < atts.length; j++) {\n        var att = atts[j];\n        doc._attachments[att] = pick(doc._attachments[att],\n          ['data', 'digest', 'content_type', 'length', 'revpos', 'stub']);\n      }\n    }\n  }\n}\n\n// compare two docs, first by _id then by _rev\nfunction compareByIdThenRev(a, b) {\n  if (a._id === b._id) {\n    const aStart = a._revisions ? a._revisions.start : 0;\n    const bStart = b._revisions ? b._revisions.start : 0;\n    return aStart - bStart;\n  }\n  return a._id < b._id ? -1 : 1;\n}\n\n// for every node in a revision tree computes its distance from the closest\n// leaf\nfunction computeHeight(revs) {\n  var height = {};\n  var edges = [];\n  traverseRevTree(revs, function (isLeaf, pos, id, prnt) {\n    var rev$$1 = pos + \"-\" + id;\n    if (isLeaf) {\n      height[rev$$1] = 0;\n    }\n    if (prnt !== undefined) {\n      edges.push({from: prnt, to: rev$$1});\n    }\n    return rev$$1;\n  });\n\n  edges.reverse();\n  edges.forEach(function (edge) {\n    if (height[edge.from] === undefined) {\n      height[edge.from] = 1 + height[edge.to];\n    } else {\n      height[edge.from] = Math.min(height[edge.from], 1 + height[edge.to]);\n    }\n  });\n  return height;\n}\n\nfunction allDocsKeysParse(opts) {\n  var keys =  ('limit' in opts) ?\n    opts.keys.slice(opts.skip, opts.limit + opts.skip) :\n    (opts.skip > 0) ? opts.keys.slice(opts.skip) : opts.keys;\n  opts.keys = keys;\n  opts.skip = 0;\n  delete opts.limit;\n  if (opts.descending) {\n    keys.reverse();\n    opts.descending = false;\n  }\n}\n\n// all compaction is done in a queue, to avoid attaching\n// too many listeners at once\nfunction doNextCompaction(self) {\n  var task = self._compactionQueue[0];\n  var opts = task.opts;\n  var callback = task.callback;\n  self.get('_local/compaction').catch(function () {\n    return false;\n  }).then(function (doc) {\n    if (doc && doc.last_seq) {\n      opts.last_seq = doc.last_seq;\n    }\n    self._compact(opts, function (err, res) {\n      /* istanbul ignore if */\n      if (err) {\n        callback(err);\n      } else {\n        callback(null, res);\n      }\n      nextTick(function () {\n        self._compactionQueue.shift();\n        if (self._compactionQueue.length) {\n          doNextCompaction(self);\n        }\n      });\n    });\n  });\n}\n\nfunction appendPurgeSeq(db, docId, rev$$1) {\n  return db.get('_local/purges').then(function (doc) {\n    const purgeSeq = doc.purgeSeq + 1;\n    doc.purges.push({\n      docId,\n      rev: rev$$1,\n      purgeSeq,\n    });\n    if (doc.purges.length > self.purged_infos_limit) {\n      doc.purges.splice(0, doc.purges.length - self.purged_infos_limit);\n    }\n    doc.purgeSeq = purgeSeq;\n    return doc;\n  }).catch(function (err) {\n    if (err.status !== 404) {\n      throw err;\n    }\n    return {\n      _id: '_local/purges',\n      purges: [{\n        docId,\n        rev: rev$$1,\n        purgeSeq: 0,\n      }],\n      purgeSeq: 0,\n    };\n  }).then(function (doc) {\n    return db.put(doc);\n  });\n}\n\nfunction attachmentNameError(name) {\n  if (name.charAt(0) === '_') {\n    return name + ' is not a valid attachment name, attachment ' +\n      'names cannot start with \\'_\\'';\n  }\n  return false;\n}\n\nfunction isNotSingleDoc(doc) {\n  return doc === null || typeof doc !== 'object' || Array.isArray(doc);\n}\n\nconst validRevRegex = /^\\d+-[^-]*$/;\nfunction isValidRev(rev$$1) {\n  return typeof rev$$1 === 'string' && validRevRegex.test(rev$$1);\n}\n\nclass AbstractPouchDB extends EE {\n  _setup() {\n    this.post = adapterFun('post', function (doc, opts, callback) {\n      if (typeof opts === 'function') {\n        callback = opts;\n        opts = {};\n      }\n      if (isNotSingleDoc(doc)) {\n        return callback(createError(NOT_AN_OBJECT));\n      }\n      this.bulkDocs({docs: [doc]}, opts, yankError(callback, doc._id));\n    }).bind(this);\n\n    this.put = adapterFun('put', function (doc, opts, cb) {\n      if (typeof opts === 'function') {\n        cb = opts;\n        opts = {};\n      }\n      if (isNotSingleDoc(doc)) {\n        return cb(createError(NOT_AN_OBJECT));\n      }\n      invalidIdError(doc._id);\n      if ('_rev' in doc && !isValidRev(doc._rev)) {\n        return cb(createError(INVALID_REV));\n      }\n      if (isLocalId(doc._id) && typeof this._putLocal === 'function') {\n        if (doc._deleted) {\n          return this._removeLocal(doc, cb);\n        } else {\n          return this._putLocal(doc, cb);\n        }\n      }\n\n      const putDoc = (next) => {\n        if (typeof this._put === 'function' && opts.new_edits !== false) {\n          this._put(doc, opts, next);\n        } else {\n          this.bulkDocs({docs: [doc]}, opts, yankError(next, doc._id));\n        }\n      };\n\n      if (opts.force && doc._rev) {\n        transformForceOptionToNewEditsOption();\n        putDoc(function (err) {\n          var result = err ? null : {ok: true, id: doc._id, rev: doc._rev};\n          cb(err, result);\n        });\n      } else {\n        putDoc(cb);\n      }\n\n      function transformForceOptionToNewEditsOption() {\n        var parts = doc._rev.split('-');\n        var oldRevId = parts[1];\n        var oldRevNum = parseInt(parts[0], 10);\n\n        var newRevNum = oldRevNum + 1;\n        var newRevId = rev();\n\n        doc._revisions = {\n          start: newRevNum,\n          ids: [newRevId, oldRevId]\n        };\n        doc._rev = newRevNum + '-' + newRevId;\n        opts.new_edits = false;\n      }\n    }).bind(this);\n\n    this.putAttachment = adapterFun('putAttachment', function (docId, attachmentId, rev$$1, blob, type) {\n      var api = this;\n      if (typeof type === 'function') {\n        type = blob;\n        blob = rev$$1;\n        rev$$1 = null;\n      }\n      // Lets fix in https://github.com/pouchdb/pouchdb/issues/3267\n      /* istanbul ignore if */\n      if (typeof type === 'undefined') {\n        type = blob;\n        blob = rev$$1;\n        rev$$1 = null;\n      }\n      if (!type) {\n        guardedConsole('warn', 'Attachment', attachmentId, 'on document', docId, 'is missing content_type');\n      }\n\n      function createAttachment(doc) {\n        var prevrevpos = '_rev' in doc ? parseInt(doc._rev, 10) : 0;\n        doc._attachments = doc._attachments || {};\n        doc._attachments[attachmentId] = {\n          content_type: type,\n          data: blob,\n          revpos: ++prevrevpos\n        };\n        return api.put(doc);\n      }\n\n      return api.get(docId).then(function (doc) {\n        if (doc._rev !== rev$$1) {\n          throw createError(REV_CONFLICT);\n        }\n\n        return createAttachment(doc);\n      }, function (err) {\n        // create new doc\n        /* istanbul ignore else */\n        if (err.reason === MISSING_DOC.message) {\n          return createAttachment({_id: docId});\n        } else {\n          throw err;\n        }\n      });\n    }).bind(this);\n\n    this.removeAttachment = adapterFun('removeAttachment', function (docId, attachmentId, rev$$1, callback) {\n      this.get(docId, (err, obj) => {\n        /* istanbul ignore if */\n        if (err) {\n          callback(err);\n          return;\n        }\n        if (obj._rev !== rev$$1) {\n          callback(createError(REV_CONFLICT));\n          return;\n        }\n        /* istanbul ignore if */\n        if (!obj._attachments) {\n          return callback();\n        }\n        delete obj._attachments[attachmentId];\n        if (Object.keys(obj._attachments).length === 0) {\n          delete obj._attachments;\n        }\n        this.put(obj, callback);\n      });\n    }).bind(this);\n\n    this.remove = adapterFun('remove', function (docOrId, optsOrRev, opts, callback) {\n      var doc;\n      if (typeof optsOrRev === 'string') {\n        // id, rev, opts, callback style\n        doc = {\n          _id: docOrId,\n          _rev: optsOrRev\n        };\n        if (typeof opts === 'function') {\n          callback = opts;\n          opts = {};\n        }\n      } else {\n        // doc, opts, callback style\n        doc = docOrId;\n        if (typeof optsOrRev === 'function') {\n          callback = optsOrRev;\n          opts = {};\n        } else {\n          callback = opts;\n          opts = optsOrRev;\n        }\n      }\n      opts = opts || {};\n      opts.was_delete = true;\n      var newDoc = {_id: doc._id, _rev: (doc._rev || opts.rev)};\n      newDoc._deleted = true;\n      if (isLocalId(newDoc._id) && typeof this._removeLocal === 'function') {\n        return this._removeLocal(doc, callback);\n      }\n      this.bulkDocs({docs: [newDoc]}, opts, yankError(callback, newDoc._id));\n    }).bind(this);\n\n    this.revsDiff = adapterFun('revsDiff', function (req, opts, callback) {\n      if (typeof opts === 'function') {\n        callback = opts;\n        opts = {};\n      }\n      var ids = Object.keys(req);\n\n      if (!ids.length) {\n        return callback(null, {});\n      }\n\n      var count = 0;\n      var missing = new Map();\n\n      function addToMissing(id, revId) {\n        if (!missing.has(id)) {\n          missing.set(id, {missing: []});\n        }\n        missing.get(id).missing.push(revId);\n      }\n\n      function processDoc(id, rev_tree) {\n        // Is this fast enough? Maybe we should switch to a set simulated by a map\n        var missingForId = req[id].slice(0);\n        traverseRevTree(rev_tree, function (isLeaf, pos, revHash, ctx,\n          opts) {\n            var rev$$1 = pos + '-' + revHash;\n            var idx = missingForId.indexOf(rev$$1);\n            if (idx === -1) {\n              return;\n            }\n\n            missingForId.splice(idx, 1);\n            /* istanbul ignore if */\n            if (opts.status !== 'available') {\n              addToMissing(id, rev$$1);\n            }\n          });\n\n        // Traversing the tree is synchronous, so now `missingForId` contains\n        // revisions that were not found in the tree\n        missingForId.forEach(function (rev$$1) {\n          addToMissing(id, rev$$1);\n        });\n      }\n\n      ids.forEach(function (id) {\n        this._getRevisionTree(id, function (err, rev_tree) {\n          if (err && err.status === 404 && err.message === 'missing') {\n            missing.set(id, {missing: req[id]});\n          } else if (err) {\n            /* istanbul ignore next */\n            return callback(err);\n          } else {\n            processDoc(id, rev_tree);\n          }\n\n          if (++count === ids.length) {\n            // convert LazyMap to object\n            var missingObj = {};\n            missing.forEach(function (value, key) {\n              missingObj[key] = value;\n            });\n            return callback(null, missingObj);\n          }\n        });\n      }, this);\n    }).bind(this);\n\n    // _bulk_get API for faster replication, as described in\n    // https://github.com/apache/couchdb-chttpd/pull/33\n    // At the \"abstract\" level, it will just run multiple get()s in\n    // parallel, because this isn't much of a performance cost\n    // for local databases (except the cost of multiple transactions, which is\n    // small). The http adapter overrides this in order\n    // to do a more efficient single HTTP request.\n    this.bulkGet = adapterFun('bulkGet', function (opts, callback) {\n      bulkGet(this, opts, callback);\n    }).bind(this);\n\n    // compact one document and fire callback\n    // by compacting we mean removing all revisions which\n    // are further from the leaf in revision tree than max_height\n    this.compactDocument = adapterFun('compactDocument', function (docId, maxHeight, callback) {\n      this._getRevisionTree(docId, (err, revTree) => {\n        /* istanbul ignore if */\n        if (err) {\n          return callback(err);\n        }\n        var height = computeHeight(revTree);\n        var candidates = [];\n        var revs = [];\n        Object.keys(height).forEach(function (rev$$1) {\n          if (height[rev$$1] > maxHeight) {\n            candidates.push(rev$$1);\n          }\n        });\n\n        traverseRevTree(revTree, function (isLeaf, pos, revHash, ctx, opts) {\n          var rev$$1 = pos + '-' + revHash;\n          if (opts.status === 'available' && candidates.indexOf(rev$$1) !== -1) {\n            revs.push(rev$$1);\n          }\n        });\n        this._doCompaction(docId, revs, callback);\n      });\n    }).bind(this);\n\n    // compact the whole database using single document\n    // compaction\n    this.compact = adapterFun('compact', function (opts, callback) {\n      if (typeof opts === 'function') {\n        callback = opts;\n        opts = {};\n      }\n\n      opts = opts || {};\n\n      this._compactionQueue = this._compactionQueue || [];\n      this._compactionQueue.push({opts, callback});\n      if (this._compactionQueue.length === 1) {\n        doNextCompaction(this);\n      }\n    }).bind(this);\n\n    /* Begin api wrappers. Specific functionality to storage belongs in the _[method] */\n    this.get = adapterFun('get', function (id, opts, cb) {\n      if (typeof opts === 'function') {\n        cb = opts;\n        opts = {};\n      }\n      opts = opts || {};\n      if (typeof id !== 'string') {\n        return cb(createError(INVALID_ID));\n      }\n      if (isLocalId(id) && typeof this._getLocal === 'function') {\n        return this._getLocal(id, cb);\n      }\n      var leaves = [];\n\n      const finishOpenRevs = () => {\n        var result = [];\n        var count = leaves.length;\n        /* istanbul ignore if */\n        if (!count) {\n          return cb(null, result);\n        }\n\n        // order with open_revs is unspecified\n        leaves.forEach((leaf) => {\n          this.get(id, {\n            rev: leaf,\n            revs: opts.revs,\n            latest: opts.latest,\n            attachments: opts.attachments,\n            binary: opts.binary\n          }, function (err, doc) {\n            if (!err) {\n              // using latest=true can produce duplicates\n              var existing;\n              for (var i = 0, l = result.length; i < l; i++) {\n                if (result[i].ok && result[i].ok._rev === doc._rev) {\n                  existing = true;\n                  break;\n                }\n              }\n              if (!existing) {\n                result.push({ok: doc});\n              }\n            } else {\n              result.push({missing: leaf});\n            }\n            count--;\n            if (!count) {\n              cb(null, result);\n            }\n          });\n        });\n      };\n\n      if (opts.open_revs) {\n        if (opts.open_revs === \"all\") {\n          this._getRevisionTree(id, function (err, rev_tree) {\n            /* istanbul ignore if */\n            if (err) {\n              return cb(err);\n            }\n            leaves = collectLeaves(rev_tree).map(function (leaf) {\n              return leaf.rev;\n            });\n            finishOpenRevs();\n          });\n        } else {\n          if (Array.isArray(opts.open_revs)) {\n            leaves = opts.open_revs;\n            for (var i = 0; i < leaves.length; i++) {\n              var l = leaves[i];\n              // looks like it's the only thing couchdb checks\n              if (!isValidRev(l)) {\n                return cb(createError(INVALID_REV));\n              }\n            }\n            finishOpenRevs();\n          } else {\n            return cb(createError(UNKNOWN_ERROR, 'function_clause'));\n          }\n        }\n        return; // open_revs does not like other options\n      }\n\n      return this._get(id, opts, (err, result) => {\n        if (err) {\n          err.docId = id;\n          return cb(err);\n        }\n\n        var doc = result.doc;\n        var metadata = result.metadata;\n        var ctx = result.ctx;\n\n        if (opts.conflicts) {\n          var conflicts = collectConflicts(metadata);\n          if (conflicts.length) {\n            doc._conflicts = conflicts;\n          }\n        }\n\n        if (isDeleted(metadata, doc._rev)) {\n          doc._deleted = true;\n        }\n\n        if (opts.revs || opts.revs_info) {\n          var splittedRev = doc._rev.split('-');\n          var revNo       = parseInt(splittedRev[0], 10);\n          var revHash     = splittedRev[1];\n\n          var paths = rootToLeaf(metadata.rev_tree);\n          var path = null;\n\n          for (var i = 0; i < paths.length; i++) {\n            var currentPath = paths[i];\n            const hashIndex = currentPath.ids.findIndex(x => x.id === revHash);\n            var hashFoundAtRevPos = hashIndex === (revNo - 1);\n\n            if (hashFoundAtRevPos || (!path && hashIndex !== -1)) {\n              path = currentPath;\n            }\n          }\n\n          /* istanbul ignore if */\n          if (!path) {\n            err = new Error('invalid rev tree');\n            err.docId = id;\n            return cb(err);\n          }\n\n          const pathId = doc._rev.split('-')[1];\n          const indexOfRev = path.ids.findIndex(x => x.id === pathId) + 1;\n          var howMany = path.ids.length - indexOfRev;\n          path.ids.splice(indexOfRev, howMany);\n          path.ids.reverse();\n\n          if (opts.revs) {\n            doc._revisions = {\n              start: (path.pos + path.ids.length) - 1,\n              ids: path.ids.map(function (rev$$1) {\n                return rev$$1.id;\n              })\n            };\n          }\n          if (opts.revs_info) {\n            var pos =  path.pos + path.ids.length;\n            doc._revs_info = path.ids.map(function (rev$$1) {\n              pos--;\n              return {\n                rev: pos + '-' + rev$$1.id,\n                status: rev$$1.opts.status\n              };\n            });\n          }\n        }\n\n        if (opts.attachments && doc._attachments) {\n          var attachments = doc._attachments;\n          var count = Object.keys(attachments).length;\n          if (count === 0) {\n            return cb(null, doc);\n          }\n          Object.keys(attachments).forEach((key) => {\n            this._getAttachment(doc._id, key, attachments[key], {\n              binary: opts.binary,\n              metadata,\n              ctx\n            }, function (err, data) {\n              var att = doc._attachments[key];\n              att.data = data;\n              delete att.stub;\n              delete att.length;\n              if (!--count) {\n                cb(null, doc);\n              }\n            });\n          });\n        } else {\n          if (doc._attachments) {\n            for (var key in doc._attachments) {\n              /* istanbul ignore else */\n              if (Object.prototype.hasOwnProperty.call(doc._attachments, key)) {\n                doc._attachments[key].stub = true;\n              }\n            }\n          }\n          cb(null, doc);\n        }\n      });\n    }).bind(this);\n\n    // TODO: I don't like this, it forces an extra read for every\n    // attachment read and enforces a confusing api between\n    // adapter.js and the adapter implementation\n    this.getAttachment = adapterFun('getAttachment', function (docId, attachmentId, opts, callback) {\n      if (opts instanceof Function) {\n        callback = opts;\n        opts = {};\n      }\n      this._get(docId, opts, (err, res) => {\n        if (err) {\n          return callback(err);\n        }\n        if (res.doc._attachments && res.doc._attachments[attachmentId]) {\n          opts.ctx = res.ctx;\n          opts.binary = true;\n          opts.metadata = res.metadata;\n          this._getAttachment(docId, attachmentId,\n                              res.doc._attachments[attachmentId], opts, callback);\n        } else {\n          return callback(createError(MISSING_DOC));\n        }\n      });\n    }).bind(this);\n\n    this.allDocs = adapterFun('allDocs', function (opts, callback) {\n      if (typeof opts === 'function') {\n        callback = opts;\n        opts = {};\n      }\n      opts.skip = typeof opts.skip !== 'undefined' ? opts.skip : 0;\n      if (opts.start_key) {\n        opts.startkey = opts.start_key;\n      }\n      if (opts.end_key) {\n        opts.endkey = opts.end_key;\n      }\n      if ('keys' in opts) {\n        if (!Array.isArray(opts.keys)) {\n          return callback(new TypeError('options.keys must be an array'));\n        }\n        var incompatibleOpt =\n          ['startkey', 'endkey', 'key'].filter(function (incompatibleOpt) {\n          return incompatibleOpt in opts;\n        })[0];\n        if (incompatibleOpt) {\n          callback(createError(QUERY_PARSE_ERROR,\n            'Query parameter `' + incompatibleOpt +\n            '` is not compatible with multi-get'\n          ));\n          return;\n        }\n        if (!isRemote(this)) {\n          allDocsKeysParse(opts);\n          if (opts.keys.length === 0) {\n            return this._allDocs({limit: 0}, callback);\n          }\n        }\n      }\n\n      return this._allDocs(opts, callback);\n    }).bind(this);\n\n    this.close = adapterFun('close', function (callback) {\n      this._closed = true;\n      this.emit('closed');\n      return this._close(callback);\n    }).bind(this);\n\n    this.info = adapterFun('info', function (callback) {\n      this._info((err, info) => {\n        if (err) {\n          return callback(err);\n        }\n        // assume we know better than the adapter, unless it informs us\n        info.db_name = info.db_name || this.name;\n        info.auto_compaction = !!(this.auto_compaction && !isRemote(this));\n        info.adapter = this.adapter;\n        callback(null, info);\n      });\n    }).bind(this);\n\n    this.id = adapterFun('id', function (callback) {\n      return this._id(callback);\n    }).bind(this);\n\n    this.bulkDocs = adapterFun('bulkDocs', function (req, opts, callback) {\n      if (typeof opts === 'function') {\n        callback = opts;\n        opts = {};\n      }\n\n      opts = opts || {};\n\n      if (Array.isArray(req)) {\n        req = {\n          docs: req\n        };\n      }\n\n      if (!req || !req.docs || !Array.isArray(req.docs)) {\n        return callback(createError(MISSING_BULK_DOCS));\n      }\n\n      for (var i = 0; i < req.docs.length; ++i) {\n        const doc = req.docs[i];\n        if (isNotSingleDoc(doc)) {\n          return callback(createError(NOT_AN_OBJECT));\n        }\n        if ('_rev' in doc && !isValidRev(doc._rev)) {\n          return callback(createError(INVALID_REV));\n        }\n      }\n\n      var attachmentError;\n      req.docs.forEach(function (doc) {\n        if (doc._attachments) {\n          Object.keys(doc._attachments).forEach(function (name) {\n            attachmentError = attachmentError || attachmentNameError(name);\n            if (!doc._attachments[name].content_type) {\n              guardedConsole('warn', 'Attachment', name, 'on document', doc._id, 'is missing content_type');\n            }\n          });\n        }\n      });\n\n      if (attachmentError) {\n        return callback(createError(BAD_REQUEST, attachmentError));\n      }\n\n      if (!('new_edits' in opts)) {\n        if ('new_edits' in req) {\n          opts.new_edits = req.new_edits;\n        } else {\n          opts.new_edits = true;\n        }\n      }\n\n      var adapter = this;\n      if (!opts.new_edits && !isRemote(adapter)) {\n        // ensure revisions of the same doc are sorted, so that\n        // the local adapter processes them correctly (#2935)\n        req.docs.sort(compareByIdThenRev);\n      }\n\n      cleanDocs(req.docs);\n\n      // in the case of conflicts, we want to return the _ids to the user\n      // however, the underlying adapter may destroy the docs array, so\n      // create a copy here\n      var ids = req.docs.map(function (doc) {\n        return doc._id;\n      });\n\n      this._bulkDocs(req, opts, function (err, res) {\n        if (err) {\n          return callback(err);\n        }\n        if (!opts.new_edits) {\n          // this is what couch does when new_edits is false\n          res = res.filter(function (x) {\n            return x.error;\n          });\n        }\n        // add ids for error/conflict responses (not required for CouchDB)\n        if (!isRemote(adapter)) {\n          for (var i = 0, l = res.length; i < l; i++) {\n            res[i].id = res[i].id || ids[i];\n          }\n        }\n\n        callback(null, res);\n      });\n    }).bind(this);\n\n    this.registerDependentDatabase = adapterFun('registerDependentDatabase', function (dependentDb, callback) {\n      var dbOptions = clone(this.__opts);\n      if (this.__opts.view_adapter) {\n        dbOptions.adapter = this.__opts.view_adapter;\n      }\n\n      var depDB = new this.constructor(dependentDb, dbOptions);\n\n      function diffFun(doc) {\n        doc.dependentDbs = doc.dependentDbs || {};\n        if (doc.dependentDbs[dependentDb]) {\n          return false; // no update required\n        }\n        doc.dependentDbs[dependentDb] = true;\n        return doc;\n      }\n      upsert(this, '_local/_pouch_dependentDbs', diffFun).then(function () {\n        callback(null, {db: depDB});\n      }).catch(callback);\n    }).bind(this);\n\n    this.destroy = adapterFun('destroy', function (opts, callback) {\n\n      if (typeof opts === 'function') {\n        callback = opts;\n        opts = {};\n      }\n\n      var usePrefix = 'use_prefix' in this ? this.use_prefix : true;\n\n      const destroyDb = () => {\n        // call destroy method of the particular adaptor\n        this._destroy(opts, (err, resp) => {\n          if (err) {\n            return callback(err);\n          }\n          this._destroyed = true;\n          this.emit('destroyed');\n          callback(null, resp || { 'ok': true });\n        });\n      };\n\n      if (isRemote(this)) {\n        // no need to check for dependent DBs if it's a remote DB\n        return destroyDb();\n      }\n\n      this.get('_local/_pouch_dependentDbs', (err, localDoc) => {\n        if (err) {\n          /* istanbul ignore if */\n          if (err.status !== 404) {\n            return callback(err);\n          } else { // no dependencies\n            return destroyDb();\n          }\n        }\n        var dependentDbs = localDoc.dependentDbs;\n        var PouchDB = this.constructor;\n        var deletedMap = Object.keys(dependentDbs).map((name) => {\n          // use_prefix is only false in the browser\n          /* istanbul ignore next */\n          var trueName = usePrefix ?\n            name.replace(new RegExp('^' + PouchDB.prefix), '') : name;\n          return new PouchDB(trueName, this.__opts).destroy();\n        });\n        Promise.all(deletedMap).then(destroyDb, callback);\n      });\n    }).bind(this);\n  }\n\n  _compact(opts, callback) {\n    var changesOpts = {\n      return_docs: false,\n      last_seq: opts.last_seq || 0,\n      since: opts.last_seq || 0\n    };\n    var promises = [];\n\n    var taskId;\n    var compactedDocs = 0;\n\n    const onChange = (row) => {\n      this.activeTasks.update(taskId, {\n        completed_items: ++compactedDocs\n      });\n      promises.push(this.compactDocument(row.id, 0));\n    };\n    const onError = (err) => {\n      this.activeTasks.remove(taskId, err);\n      callback(err);\n    };\n    const onComplete = (resp) => {\n      var lastSeq = resp.last_seq;\n      Promise.all(promises).then(() => {\n        return upsert(this, '_local/compaction', (doc) => {\n          if (!doc.last_seq || doc.last_seq < lastSeq) {\n            doc.last_seq = lastSeq;\n            return doc;\n          }\n          return false; // somebody else got here first, don't update\n        });\n      }).then(() => {\n        this.activeTasks.remove(taskId);\n        callback(null, {ok: true});\n      }).catch(onError);\n    };\n\n    this.info().then((info) => {\n      taskId = this.activeTasks.add({\n        name: 'database_compaction',\n        total_items: info.update_seq - changesOpts.last_seq,\n      });\n\n      this.changes(changesOpts)\n        .on('change', onChange)\n        .on('complete', onComplete)\n        .on('error', onError);\n    });\n  }\n\n  changes(opts, callback) {\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n\n    opts = opts || {};\n\n    // By default set return_docs to false if the caller has opts.live = true,\n    // this will prevent us from collecting the set of changes indefinitely\n    // resulting in growing memory\n    opts.return_docs = ('return_docs' in opts) ? opts.return_docs : !opts.live;\n\n    return new Changes$1(this, opts, callback);\n  }\n\n  type() {\n    return (typeof this._type === 'function') ? this._type() : this.adapter;\n  }\n}\n\n// The abstract purge implementation expects a doc id and the rev of a leaf node in that doc.\n// It will return errors if the rev doesn’t exist or isn’t a leaf.\nAbstractPouchDB.prototype.purge = adapterFun('_purge', function (docId, rev$$1, callback) {\n  if (typeof this._purge === 'undefined') {\n    return callback(createError(UNKNOWN_ERROR, 'Purge is not implemented in the ' + this.adapter + ' adapter.'));\n  }\n  var self = this;\n\n  self._getRevisionTree(docId, (error, revs) => {\n    if (error) {\n      return callback(error);\n    }\n    if (!revs) {\n      return callback(createError(MISSING_DOC));\n    }\n    let path;\n    try {\n      path = findPathToLeaf(revs, rev$$1);\n    } catch (error) {\n      return callback(error.message || error);\n    }\n    self._purge(docId, path, (error, result) => {\n      if (error) {\n        return callback(error);\n      } else {\n        appendPurgeSeq(self, docId, rev$$1).then(function () {\n          return callback(null, result);\n        });\n      }\n    });\n  });\n});\n\nclass TaskQueue {\n  constructor() {\n    this.isReady = false;\n    this.failed = false;\n    this.queue = [];\n  }\n\n  execute() {\n    var fun;\n    if (this.failed) {\n      while ((fun = this.queue.shift())) {\n        fun(this.failed);\n      }\n    } else {\n      while ((fun = this.queue.shift())) {\n        fun();\n      }\n    }\n  }\n\n  fail(err) {\n    this.failed = err;\n    this.execute();\n  }\n\n  ready(db) {\n    this.isReady = true;\n    this.db = db;\n    this.execute();\n  }\n\n  addTask(fun) {\n    this.queue.push(fun);\n    if (this.failed) {\n      this.execute();\n    }\n  }\n}\n\nfunction parseAdapter(name, opts) {\n  var match = name.match(/([a-z-]*):\\/\\/(.*)/);\n  if (match) {\n    // the http adapter expects the fully qualified name\n    return {\n      name: /https?/.test(match[1]) ? match[1] + '://' + match[2] : match[2],\n      adapter: match[1]\n    };\n  }\n\n  var adapters = PouchDB.adapters;\n  var preferredAdapters = PouchDB.preferredAdapters;\n  var prefix = PouchDB.prefix;\n  var adapterName = opts.adapter;\n\n  if (!adapterName) { // automatically determine adapter\n    for (var i = 0; i < preferredAdapters.length; ++i) {\n      adapterName = preferredAdapters[i];\n      // check for browsers that have been upgraded from websql-only to websql+idb\n      /* istanbul ignore if */\n      if (adapterName === 'idb' && 'websql' in adapters &&\n          hasLocalStorage() && localStorage['_pouch__websqldb_' + prefix + name]) {\n        // log it, because this can be confusing during development\n        guardedConsole('log', 'PouchDB is downgrading \"' + name + '\" to WebSQL to' +\n          ' avoid data loss, because it was already opened with WebSQL.');\n        continue; // keep using websql to avoid user data loss\n      }\n      break;\n    }\n  }\n\n  var adapter = adapters[adapterName];\n\n  // if adapter is invalid, then an error will be thrown later\n  var usePrefix = (adapter && 'use_prefix' in adapter) ?\n    adapter.use_prefix : true;\n\n  return {\n    name: usePrefix ? (prefix + name) : name,\n    adapter: adapterName\n  };\n}\n\nfunction inherits(A, B) {\n  A.prototype = Object.create(B.prototype, {\n    constructor: { value: A }\n  });\n}\n\nfunction createClass(parent, init) {\n  let klass = function (...args) {\n    if (!(this instanceof klass)) {\n      return new klass(...args);\n    }\n    init.apply(this, args);\n  };\n  inherits(klass, parent);\n  return klass;\n}\n\n// OK, so here's the deal. Consider this code:\n//     var db1 = new PouchDB('foo');\n//     var db2 = new PouchDB('foo');\n//     db1.destroy();\n// ^ these two both need to emit 'destroyed' events,\n// as well as the PouchDB constructor itself.\n// So we have one db object (whichever one got destroy() called on it)\n// responsible for emitting the initial event, which then gets emitted\n// by the constructor, which then broadcasts it to any other dbs\n// that may have been created with the same name.\nfunction prepareForDestruction(self) {\n\n  function onDestroyed(from_constructor) {\n    self.removeListener('closed', onClosed);\n    if (!from_constructor) {\n      self.constructor.emit('destroyed', self.name);\n    }\n  }\n\n  function onClosed() {\n    self.removeListener('destroyed', onDestroyed);\n    self.constructor.emit('unref', self);\n  }\n\n  self.once('destroyed', onDestroyed);\n  self.once('closed', onClosed);\n  self.constructor.emit('ref', self);\n}\n\nclass PouchInternal extends AbstractPouchDB {\n  constructor(name, opts) {\n    super();\n    this._setup(name, opts);\n  }\n\n  _setup(name, opts) {\n    super._setup();\n    opts = opts || {};\n\n    if (name && typeof name === 'object') {\n      opts = name;\n      name = opts.name;\n      delete opts.name;\n    }\n\n    if (opts.deterministic_revs === undefined) {\n      opts.deterministic_revs = true;\n    }\n\n    this.__opts = opts = clone(opts);\n\n    this.auto_compaction = opts.auto_compaction;\n    this.purged_infos_limit = opts.purged_infos_limit || 1000;\n    this.prefix = PouchDB.prefix;\n\n    if (typeof name !== 'string') {\n      throw new Error('Missing/invalid DB name');\n    }\n\n    var prefixedName = (opts.prefix || '') + name;\n    var backend = parseAdapter(prefixedName, opts);\n\n    opts.name = backend.name;\n    opts.adapter = opts.adapter || backend.adapter;\n\n    this.name = name;\n    this._adapter = opts.adapter;\n    PouchDB.emit('debug', ['adapter', 'Picked adapter: ', opts.adapter]);\n\n    if (!PouchDB.adapters[opts.adapter] ||\n        !PouchDB.adapters[opts.adapter].valid()) {\n      throw new Error('Invalid Adapter: ' + opts.adapter);\n    }\n\n    if (opts.view_adapter) {\n      if (!PouchDB.adapters[opts.view_adapter] ||\n          !PouchDB.adapters[opts.view_adapter].valid()) {\n        throw new Error('Invalid View Adapter: ' + opts.view_adapter);\n      }\n    }\n\n    this.taskqueue = new TaskQueue();\n\n    this.adapter = opts.adapter;\n\n    PouchDB.adapters[opts.adapter].call(this, opts, (err) => {\n      if (err) {\n        return this.taskqueue.fail(err);\n      }\n      prepareForDestruction(this);\n\n      this.emit('created', this);\n      PouchDB.emit('created', this.name);\n      this.taskqueue.ready(this);\n    });\n  }\n}\n\nconst PouchDB = createClass(PouchInternal, function (name, opts) {\n  PouchInternal.prototype._setup.call(this, name, opts);\n});\n\nvar f$1 = fetch;\nvar h = Headers;\n\nclass ActiveTasks {\n  constructor() {\n    this.tasks = {};\n  }\n\n  list() {\n    return Object.values(this.tasks);\n  }\n\n  add(task) {\n    const id = v4();\n    this.tasks[id] = {\n      id,\n      name: task.name,\n      total_items: task.total_items,\n      created_at: new Date().toJSON()\n    };\n    return id;\n  }\n\n  get(id) {\n    return this.tasks[id];\n  }\n\n  /* eslint-disable no-unused-vars */\n  remove(id, reason) {\n    delete this.tasks[id];\n    return this.tasks;\n  }\n\n  update(id, updatedTask) {\n    const task = this.tasks[id];\n    if (typeof task !== 'undefined') {\n      const mergedTask = {\n        id: task.id,\n        name: task.name,\n        created_at: task.created_at,\n        total_items: updatedTask.total_items || task.total_items,\n        completed_items: updatedTask.completed_items || task.completed_items,\n        updated_at: new Date().toJSON()\n      };\n      this.tasks[id] = mergedTask;\n    }\n    return this.tasks;\n  }\n}\n\nPouchDB.adapters = {};\nPouchDB.preferredAdapters = [];\n\nPouchDB.prefix = '_pouch_';\n\nvar eventEmitter = new EE();\n\nfunction setUpEventEmitter(Pouch) {\n  Object.keys(EE.prototype).forEach(function (key) {\n    if (typeof EE.prototype[key] === 'function') {\n      Pouch[key] = eventEmitter[key].bind(eventEmitter);\n    }\n  });\n\n  // these are created in constructor.js, and allow us to notify each DB with\n  // the same name that it was destroyed, via the constructor object\n  var destructListeners = Pouch._destructionListeners = new Map();\n\n  Pouch.on('ref', function onConstructorRef(db) {\n    if (!destructListeners.has(db.name)) {\n      destructListeners.set(db.name, []);\n    }\n    destructListeners.get(db.name).push(db);\n  });\n\n  Pouch.on('unref', function onConstructorUnref(db) {\n    if (!destructListeners.has(db.name)) {\n      return;\n    }\n    var dbList = destructListeners.get(db.name);\n    var pos = dbList.indexOf(db);\n    if (pos < 0) {\n      /* istanbul ignore next */\n      return;\n    }\n    dbList.splice(pos, 1);\n    if (dbList.length > 1) {\n      /* istanbul ignore next */\n      destructListeners.set(db.name, dbList);\n    } else {\n      destructListeners.delete(db.name);\n    }\n  });\n\n  Pouch.on('destroyed', function onConstructorDestroyed(name) {\n    if (!destructListeners.has(name)) {\n      return;\n    }\n    var dbList = destructListeners.get(name);\n    destructListeners.delete(name);\n    dbList.forEach(function (db) {\n      db.emit('destroyed',true);\n    });\n  });\n}\n\nsetUpEventEmitter(PouchDB);\n\nPouchDB.adapter = function (id, obj, addToPreferredAdapters) {\n  /* istanbul ignore else */\n  if (obj.valid()) {\n    PouchDB.adapters[id] = obj;\n    if (addToPreferredAdapters) {\n      PouchDB.preferredAdapters.push(id);\n    }\n  }\n};\n\nPouchDB.plugin = function (obj) {\n  if (typeof obj === 'function') { // function style for plugins\n    obj(PouchDB);\n  } else if (typeof obj !== 'object' || Object.keys(obj).length === 0) {\n    throw new Error('Invalid plugin: got \"' + obj + '\", expected an object or a function');\n  } else {\n    Object.keys(obj).forEach(function (id) { // object style for plugins\n      PouchDB.prototype[id] = obj[id];\n    });\n  }\n  if (this.__defaults) {\n    PouchDB.__defaults = Object.assign({}, this.__defaults);\n  }\n  return PouchDB;\n};\n\nPouchDB.defaults = function (defaultOpts) {\n  let PouchWithDefaults = createClass(PouchDB, function (name, opts) {\n    opts = opts || {};\n\n    if (name && typeof name === 'object') {\n      opts = name;\n      name = opts.name;\n      delete opts.name;\n    }\n\n    opts = Object.assign({}, PouchWithDefaults.__defaults, opts);\n    PouchDB.call(this, name, opts);\n  });\n\n  PouchWithDefaults.preferredAdapters = PouchDB.preferredAdapters.slice();\n  Object.keys(PouchDB).forEach(function (key) {\n    if (!(key in PouchWithDefaults)) {\n      PouchWithDefaults[key] = PouchDB[key];\n    }\n  });\n\n  // make default options transitive\n  // https://github.com/pouchdb/pouchdb/issues/5922\n  PouchWithDefaults.__defaults = Object.assign({}, this.__defaults, defaultOpts);\n\n  return PouchWithDefaults;\n};\n\nPouchDB.fetch = function (url, opts) {\n  return f$1(url, opts);\n};\n\nPouchDB.prototype.activeTasks = PouchDB.activeTasks = new ActiveTasks();\n\n// managed automatically by set-version.js\nvar version = \"9.0.0\";\n\n// this would just be \"return doc[field]\", but fields\n// can be \"deep\" due to dot notation\nfunction getFieldFromDoc(doc, parsedField) {\n  var value = doc;\n  for (var i = 0, len = parsedField.length; i < len; i++) {\n    var key = parsedField[i];\n    value = value[key];\n    if (!value) {\n      break;\n    }\n  }\n  return value;\n}\n\nfunction compare(left, right) {\n  return left < right ? -1 : left > right ? 1 : 0;\n}\n\n// Converts a string in dot notation to an array of its components, with backslash escaping\nfunction parseField(fieldName) {\n  // fields may be deep (e.g. \"foo.bar.baz\"), so parse\n  var fields = [];\n  var current = '';\n  for (var i = 0, len = fieldName.length; i < len; i++) {\n    var ch = fieldName[i];\n    if (i > 0 && fieldName[i - 1] === '\\\\' && (ch === '$' || ch === '.')) {\n      // escaped delimiter\n      current = current.substring(0, current.length - 1) + ch;\n    } else if (ch === '.') {\n      // When `.` is not escaped (above), it is a field delimiter\n      fields.push(current);\n      current = '';\n    } else { // normal character\n      current += ch;\n    }\n  }\n  fields.push(current);\n  return fields;\n}\n\nvar combinationFields = ['$or', '$nor', '$not'];\nfunction isCombinationalField(field) {\n  return combinationFields.indexOf(field) > -1;\n}\n\nfunction getKey(obj) {\n  return Object.keys(obj)[0];\n}\n\nfunction getValue(obj) {\n  return obj[getKey(obj)];\n}\n\n\n// flatten an array of selectors joined by an $and operator\nfunction mergeAndedSelectors(selectors) {\n\n  // sort to ensure that e.g. if the user specified\n  // $and: [{$gt: 'a'}, {$gt: 'b'}], then it's collapsed into\n  // just {$gt: 'b'}\n  var res = {};\n  var first = {$or: true, $nor: true};\n\n  selectors.forEach(function (selector) {\n    Object.keys(selector).forEach(function (field) {\n      var matcher = selector[field];\n      if (typeof matcher !== 'object') {\n        matcher = {$eq: matcher};\n      }\n\n      if (isCombinationalField(field)) {\n        // or, nor\n        if (matcher instanceof Array) {\n          if (first[field]) {\n            first[field] = false;\n            res[field] = matcher;\n            return;\n          }\n\n          var entries = [];\n          res[field].forEach(function (existing) {\n            Object.keys(matcher).forEach(function (key) {\n              var m = matcher[key];\n              var longest = Math.max(Object.keys(existing).length, Object.keys(m).length);\n              var merged = mergeAndedSelectors([existing, m]);\n              if (Object.keys(merged).length <= longest) {\n                // we have a situation like: (a :{$eq :1} || ...) && (a {$eq: 2} || ...)\n                // merging would produce a $eq 2 when actually we shouldn't ever match against these merged conditions\n                // merged should always contain more values to be valid\n                return;\n              }\n              entries.push(merged);\n            });\n          });\n          res[field] = entries;\n        } else {\n          // not\n          res[field] = mergeAndedSelectors([matcher]);\n        }\n      } else {\n        var fieldMatchers = res[field] = res[field] || {};\n        Object.keys(matcher).forEach(function (operator) {\n          var value = matcher[operator];\n\n          if (operator === '$gt' || operator === '$gte') {\n            return mergeGtGte(operator, value, fieldMatchers);\n          } else if (operator === '$lt' || operator === '$lte') {\n            return mergeLtLte(operator, value, fieldMatchers);\n          } else if (operator === '$ne') {\n            return mergeNe(value, fieldMatchers);\n          } else if (operator === '$eq') {\n            return mergeEq(value, fieldMatchers);\n          } else if (operator === \"$regex\") {\n            return mergeRegex(value, fieldMatchers);\n          }\n          fieldMatchers[operator] = value;\n        });\n      }\n    });\n  });\n\n  return res;\n}\n\n\n\n// collapse logically equivalent gt/gte values\nfunction mergeGtGte(operator, value, fieldMatchers) {\n  if (typeof fieldMatchers.$eq !== 'undefined') {\n    return; // do nothing\n  }\n  if (typeof fieldMatchers.$gte !== 'undefined') {\n    if (operator === '$gte') {\n      if (value > fieldMatchers.$gte) { // more specificity\n        fieldMatchers.$gte = value;\n      }\n    } else { // operator === '$gt'\n      if (value >= fieldMatchers.$gte) { // more specificity\n        delete fieldMatchers.$gte;\n        fieldMatchers.$gt = value;\n      }\n    }\n  } else if (typeof fieldMatchers.$gt !== 'undefined') {\n    if (operator === '$gte') {\n      if (value > fieldMatchers.$gt) { // more specificity\n        delete fieldMatchers.$gt;\n        fieldMatchers.$gte = value;\n      }\n    } else { // operator === '$gt'\n      if (value > fieldMatchers.$gt) { // more specificity\n        fieldMatchers.$gt = value;\n      }\n    }\n  } else {\n    fieldMatchers[operator] = value;\n  }\n}\n\n// collapse logically equivalent lt/lte values\nfunction mergeLtLte(operator, value, fieldMatchers) {\n  if (typeof fieldMatchers.$eq !== 'undefined') {\n    return; // do nothing\n  }\n  if (typeof fieldMatchers.$lte !== 'undefined') {\n    if (operator === '$lte') {\n      if (value < fieldMatchers.$lte) { // more specificity\n        fieldMatchers.$lte = value;\n      }\n    } else { // operator === '$gt'\n      if (value <= fieldMatchers.$lte) { // more specificity\n        delete fieldMatchers.$lte;\n        fieldMatchers.$lt = value;\n      }\n    }\n  } else if (typeof fieldMatchers.$lt !== 'undefined') {\n    if (operator === '$lte') {\n      if (value < fieldMatchers.$lt) { // more specificity\n        delete fieldMatchers.$lt;\n        fieldMatchers.$lte = value;\n      }\n    } else { // operator === '$gt'\n      if (value < fieldMatchers.$lt) { // more specificity\n        fieldMatchers.$lt = value;\n      }\n    }\n  } else {\n    fieldMatchers[operator] = value;\n  }\n}\n\n// combine $ne values into one array\nfunction mergeNe(value, fieldMatchers) {\n  if ('$ne' in fieldMatchers) {\n    // there are many things this could \"not\" be\n    fieldMatchers.$ne.push(value);\n  } else { // doesn't exist yet\n    fieldMatchers.$ne = [value];\n  }\n}\n\n// add $eq into the mix\nfunction mergeEq(value, fieldMatchers) {\n  // these all have less specificity than the $eq\n  // TODO: check for user errors here\n  delete fieldMatchers.$gt;\n  delete fieldMatchers.$gte;\n  delete fieldMatchers.$lt;\n  delete fieldMatchers.$lte;\n  delete fieldMatchers.$ne;\n  fieldMatchers.$eq = value;\n}\n\n// combine $regex values into one array\nfunction mergeRegex(value, fieldMatchers) {\n  if ('$regex' in fieldMatchers) {\n    // a value could match multiple regexes\n    fieldMatchers.$regex.push(value);\n  } else { // doesn't exist yet\n    fieldMatchers.$regex = [value];\n  }\n}\n\n//#7458: execute function mergeAndedSelectors on nested $and\nfunction mergeAndedSelectorsNested(obj) {\n    for (var prop in obj) {\n        if (Array.isArray(obj)) {\n            for (var i in obj) {\n                if (obj[i]['$and']) {\n                    obj[i] = mergeAndedSelectors(obj[i]['$and']);\n                }\n            }\n        }\n        var value = obj[prop];\n        if (typeof value === 'object') {\n            mergeAndedSelectorsNested(value); // <- recursive call\n        }\n    }\n    return obj;\n}\n\n//#7458: determine id $and is present in selector (at any level)\nfunction isAndInSelector(obj, isAnd) {\n    for (var prop in obj) {\n        if (prop === '$and') {\n            isAnd = true;\n        }\n        var value = obj[prop];\n        if (typeof value === 'object') {\n            isAnd = isAndInSelector(value, isAnd); // <- recursive call\n        }\n    }\n    return isAnd;\n}\n\n//\n// normalize the selector\n//\nfunction massageSelector(input) {\n  var result = clone(input);\n\n  //#7458: if $and is present in selector (at any level) merge nested $and\n  if (isAndInSelector(result, false)) {\n    result = mergeAndedSelectorsNested(result);\n    if ('$and' in result) {\n      result = mergeAndedSelectors(result['$and']);\n    }\n  }\n\n  ['$or', '$nor'].forEach(function (orOrNor) {\n    if (orOrNor in result) {\n      // message each individual selector\n      // e.g. {foo: 'bar'} becomes {foo: {$eq: 'bar'}}\n      result[orOrNor].forEach(function (subSelector) {\n        var fields = Object.keys(subSelector);\n        for (var i = 0; i < fields.length; i++) {\n          var field = fields[i];\n          var matcher = subSelector[field];\n          if (typeof matcher !== 'object' || matcher === null) {\n            subSelector[field] = {$eq: matcher};\n          }\n        }\n      });\n    }\n  });\n\n  if ('$not' in result) {\n    //This feels a little like forcing, but it will work for now,\n    //I would like to come back to this and make the merging of selectors a little more generic\n    result['$not'] = mergeAndedSelectors([result['$not']]);\n  }\n\n  var fields = Object.keys(result);\n\n  for (var i = 0; i < fields.length; i++) {\n    var field = fields[i];\n    var matcher = result[field];\n\n    if (typeof matcher !== 'object' || matcher === null) {\n      matcher = {$eq: matcher};\n    }\n    result[field] = matcher;\n  }\n\n  normalizeArrayOperators(result);\n\n  return result;\n}\n\n//\n// The $ne and $regex values must be placed in an array because these operators can be used multiple times on the same field.\n// When $and is used, mergeAndedSelectors takes care of putting some of them into arrays, otherwise it's done here.\n//\nfunction normalizeArrayOperators(selector) {\n  Object.keys(selector).forEach(function (field) {\n    var matcher = selector[field];\n\n    if (Array.isArray(matcher)) {\n      matcher.forEach(function (matcherItem) {\n        if (matcherItem && typeof matcherItem === 'object') {\n          normalizeArrayOperators(matcherItem);\n        }\n      });\n    } else if (field === '$ne') {\n      selector.$ne = [matcher];\n    } else if (field === '$regex') {\n      selector.$regex = [matcher];\n    } else if (matcher && typeof matcher === 'object') {\n      normalizeArrayOperators(matcher);\n    }\n  });\n}\n\nfunction pad(str, padWith, upToLength) {\n  var padding = '';\n  var targetLength = upToLength - str.length;\n  /* istanbul ignore next */\n  while (padding.length < targetLength) {\n    padding += padWith;\n  }\n  return padding;\n}\n\nfunction padLeft(str, padWith, upToLength) {\n  var padding = pad(str, padWith, upToLength);\n  return padding + str;\n}\n\nvar MIN_MAGNITUDE = -324; // verified by -Number.MIN_VALUE\nvar MAGNITUDE_DIGITS = 3; // ditto\nvar SEP = ''; // set to '_' for easier debugging\n\nfunction collate(a, b) {\n\n  if (a === b) {\n    return 0;\n  }\n\n  a = normalizeKey(a);\n  b = normalizeKey(b);\n\n  var ai = collationIndex(a);\n  var bi = collationIndex(b);\n  if ((ai - bi) !== 0) {\n    return ai - bi;\n  }\n  switch (typeof a) {\n    case 'number':\n      return a - b;\n    case 'boolean':\n      return a < b ? -1 : 1;\n    case 'string':\n      return stringCollate(a, b);\n  }\n  return Array.isArray(a) ? arrayCollate(a, b) : objectCollate(a, b);\n}\n\n// couch considers null/NaN/Infinity/-Infinity === undefined,\n// for the purposes of mapreduce indexes. also, dates get stringified.\nfunction normalizeKey(key) {\n  switch (typeof key) {\n    case 'undefined':\n      return null;\n    case 'number':\n      if (key === Infinity || key === -Infinity || isNaN(key)) {\n        return null;\n      }\n      return key;\n    case 'object':\n      var origKey = key;\n      if (Array.isArray(key)) {\n        var len = key.length;\n        key = new Array(len);\n        for (var i = 0; i < len; i++) {\n          key[i] = normalizeKey(origKey[i]);\n        }\n      /* istanbul ignore next */\n      } else if (key instanceof Date) {\n        return key.toJSON();\n      } else if (key !== null) { // generic object\n        key = {};\n        for (var k in origKey) {\n          if (Object.prototype.hasOwnProperty.call(origKey, k)) {\n            var val = origKey[k];\n            if (typeof val !== 'undefined') {\n              key[k] = normalizeKey(val);\n            }\n          }\n        }\n      }\n  }\n  return key;\n}\n\nfunction indexify(key) {\n  if (key !== null) {\n    switch (typeof key) {\n      case 'boolean':\n        return key ? 1 : 0;\n      case 'number':\n        return numToIndexableString(key);\n      case 'string':\n        // We've to be sure that key does not contain \\u0000\n        // Do order-preserving replacements:\n        // 0 -> 1, 1\n        // 1 -> 1, 2\n        // 2 -> 2, 2\n        /* eslint-disable no-control-regex */\n        return key\n          .replace(/\\u0002/g, '\\u0002\\u0002')\n          .replace(/\\u0001/g, '\\u0001\\u0002')\n          .replace(/\\u0000/g, '\\u0001\\u0001');\n        /* eslint-enable no-control-regex */\n      case 'object':\n        var isArray = Array.isArray(key);\n        var arr = isArray ? key : Object.keys(key);\n        var i = -1;\n        var len = arr.length;\n        var result = '';\n        if (isArray) {\n          while (++i < len) {\n            result += toIndexableString(arr[i]);\n          }\n        } else {\n          while (++i < len) {\n            var objKey = arr[i];\n            result += toIndexableString(objKey) +\n                toIndexableString(key[objKey]);\n          }\n        }\n        return result;\n    }\n  }\n  return '';\n}\n\n// convert the given key to a string that would be appropriate\n// for lexical sorting, e.g. within a database, where the\n// sorting is the same given by the collate() function.\nfunction toIndexableString(key) {\n  var zero = '\\u0000';\n  key = normalizeKey(key);\n  return collationIndex(key) + SEP + indexify(key) + zero;\n}\n\nfunction parseNumber(str, i) {\n  var originalIdx = i;\n  var num;\n  var zero = str[i] === '1';\n  if (zero) {\n    num = 0;\n    i++;\n  } else {\n    var neg = str[i] === '0';\n    i++;\n    var numAsString = '';\n    var magAsString = str.substring(i, i + MAGNITUDE_DIGITS);\n    var magnitude = parseInt(magAsString, 10) + MIN_MAGNITUDE;\n    /* istanbul ignore next */\n    if (neg) {\n      magnitude = -magnitude;\n    }\n    i += MAGNITUDE_DIGITS;\n    while (true) {\n      var ch = str[i];\n      if (ch === '\\u0000') {\n        break;\n      } else {\n        numAsString += ch;\n      }\n      i++;\n    }\n    numAsString = numAsString.split('.');\n    if (numAsString.length === 1) {\n      num = parseInt(numAsString, 10);\n    } else {\n      /* istanbul ignore next */\n      num = parseFloat(numAsString[0] + '.' + numAsString[1]);\n    }\n    /* istanbul ignore next */\n    if (neg) {\n      num = num - 10;\n    }\n    /* istanbul ignore next */\n    if (magnitude !== 0) {\n      // parseFloat is more reliable than pow due to rounding errors\n      // e.g. Number.MAX_VALUE would return Infinity if we did\n      // num * Math.pow(10, magnitude);\n      num = parseFloat(num + 'e' + magnitude);\n    }\n  }\n  return {num, length : i - originalIdx};\n}\n\n// move up the stack while parsing\n// this function moved outside of parseIndexableString for performance\nfunction pop(stack, metaStack) {\n  var obj = stack.pop();\n\n  if (metaStack.length) {\n    var lastMetaElement = metaStack[metaStack.length - 1];\n    if (obj === lastMetaElement.element) {\n      // popping a meta-element, e.g. an object whose value is another object\n      metaStack.pop();\n      lastMetaElement = metaStack[metaStack.length - 1];\n    }\n    var element = lastMetaElement.element;\n    var lastElementIndex = lastMetaElement.index;\n    if (Array.isArray(element)) {\n      element.push(obj);\n    } else if (lastElementIndex === stack.length - 2) { // obj with key+value\n      var key = stack.pop();\n      element[key] = obj;\n    } else {\n      stack.push(obj); // obj with key only\n    }\n  }\n}\n\nfunction parseIndexableString(str) {\n  var stack = [];\n  var metaStack = []; // stack for arrays and objects\n  var i = 0;\n\n  /*eslint no-constant-condition: [\"error\", { \"checkLoops\": false }]*/\n  while (true) {\n    var collationIndex = str[i++];\n    if (collationIndex === '\\u0000') {\n      if (stack.length === 1) {\n        return stack.pop();\n      } else {\n        pop(stack, metaStack);\n        continue;\n      }\n    }\n    switch (collationIndex) {\n      case '1':\n        stack.push(null);\n        break;\n      case '2':\n        stack.push(str[i] === '1');\n        i++;\n        break;\n      case '3':\n        var parsedNum = parseNumber(str, i);\n        stack.push(parsedNum.num);\n        i += parsedNum.length;\n        break;\n      case '4':\n        var parsedStr = '';\n        /*eslint no-constant-condition: [\"error\", { \"checkLoops\": false }]*/\n        while (true) {\n          var ch = str[i];\n          if (ch === '\\u0000') {\n            break;\n          }\n          parsedStr += ch;\n          i++;\n        }\n        // perform the reverse of the order-preserving replacement\n        // algorithm (see above)\n        /* eslint-disable no-control-regex */\n        parsedStr = parsedStr.replace(/\\u0001\\u0001/g, '\\u0000')\n          .replace(/\\u0001\\u0002/g, '\\u0001')\n          .replace(/\\u0002\\u0002/g, '\\u0002');\n        /* eslint-enable no-control-regex */\n        stack.push(parsedStr);\n        break;\n      case '5':\n        var arrayElement = { element: [], index: stack.length };\n        stack.push(arrayElement.element);\n        metaStack.push(arrayElement);\n        break;\n      case '6':\n        var objElement = { element: {}, index: stack.length };\n        stack.push(objElement.element);\n        metaStack.push(objElement);\n        break;\n      /* istanbul ignore next */\n      default:\n        throw new Error(\n          'bad collationIndex or unexpectedly reached end of input: ' +\n            collationIndex);\n    }\n  }\n}\n\nfunction arrayCollate(a, b) {\n  var len = Math.min(a.length, b.length);\n  for (var i = 0; i < len; i++) {\n    var sort = collate(a[i], b[i]);\n    if (sort !== 0) {\n      return sort;\n    }\n  }\n  return (a.length === b.length) ? 0 :\n    (a.length > b.length) ? 1 : -1;\n}\nfunction stringCollate(a, b) {\n  // See: https://github.com/daleharvey/pouchdb/issues/40\n  // This is incompatible with the CouchDB implementation, but its the\n  // best we can do for now\n  return (a === b) ? 0 : ((a > b) ? 1 : -1);\n}\nfunction objectCollate(a, b) {\n  var ak = Object.keys(a), bk = Object.keys(b);\n  var len = Math.min(ak.length, bk.length);\n  for (var i = 0; i < len; i++) {\n    // First sort the keys\n    var sort = collate(ak[i], bk[i]);\n    if (sort !== 0) {\n      return sort;\n    }\n    // if the keys are equal sort the values\n    sort = collate(a[ak[i]], b[bk[i]]);\n    if (sort !== 0) {\n      return sort;\n    }\n\n  }\n  return (ak.length === bk.length) ? 0 :\n    (ak.length > bk.length) ? 1 : -1;\n}\n// The collation is defined by erlangs ordered terms\n// the atoms null, true, false come first, then numbers, strings,\n// arrays, then objects\n// null/undefined/NaN/Infinity/-Infinity are all considered null\nfunction collationIndex(x) {\n  var id = ['boolean', 'number', 'string', 'object'];\n  var idx = id.indexOf(typeof x);\n  //false if -1 otherwise true, but fast!!!!1\n  if (~idx) {\n    if (x === null) {\n      return 1;\n    }\n    if (Array.isArray(x)) {\n      return 5;\n    }\n    return idx < 3 ? (idx + 2) : (idx + 3);\n  }\n  /* istanbul ignore next */\n  if (Array.isArray(x)) {\n    return 5;\n  }\n}\n\n// conversion:\n// x yyy zz...zz\n// x = 0 for negative, 1 for 0, 2 for positive\n// y = exponent (for negative numbers negated) moved so that it's >= 0\n// z = mantisse\nfunction numToIndexableString(num) {\n\n  if (num === 0) {\n    return '1';\n  }\n\n  // convert number to exponential format for easier and\n  // more succinct string sorting\n  var expFormat = num.toExponential().split(/e\\+?/);\n  var magnitude = parseInt(expFormat[1], 10);\n\n  var neg = num < 0;\n\n  var result = neg ? '0' : '2';\n\n  // first sort by magnitude\n  // it's easier if all magnitudes are positive\n  var magForComparison = ((neg ? -magnitude : magnitude) - MIN_MAGNITUDE);\n  var magString = padLeft((magForComparison).toString(), '0', MAGNITUDE_DIGITS);\n\n  result += SEP + magString;\n\n  // then sort by the factor\n  var factor = Math.abs(parseFloat(expFormat[0])); // [1..10)\n  /* istanbul ignore next */\n  if (neg) { // for negative reverse ordering\n    factor = 10 - factor;\n  }\n\n  var factorStr = factor.toFixed(20);\n\n  // strip zeros from the end\n  factorStr = factorStr.replace(/\\.?0+$/, '');\n\n  result += SEP + factorStr;\n\n  return result;\n}\n\n// create a comparator based on the sort object\nfunction createFieldSorter(sort) {\n\n  function getFieldValuesAsArray(doc) {\n    return sort.map(function (sorting) {\n      var fieldName = getKey(sorting);\n      var parsedField = parseField(fieldName);\n      var docFieldValue = getFieldFromDoc(doc, parsedField);\n      return docFieldValue;\n    });\n  }\n\n  return function (aRow, bRow) {\n    var aFieldValues = getFieldValuesAsArray(aRow.doc);\n    var bFieldValues = getFieldValuesAsArray(bRow.doc);\n    var collation = collate(aFieldValues, bFieldValues);\n    if (collation !== 0) {\n      return collation;\n    }\n    // this is what mango seems to do\n    return compare(aRow.doc._id, bRow.doc._id);\n  };\n}\n\nfunction filterInMemoryFields(rows, requestDef, inMemoryFields) {\n  rows = rows.filter(function (row) {\n    return rowFilter(row.doc, requestDef.selector, inMemoryFields);\n  });\n\n  if (requestDef.sort) {\n    // in-memory sort\n    var fieldSorter = createFieldSorter(requestDef.sort);\n    rows = rows.sort(fieldSorter);\n    if (typeof requestDef.sort[0] !== 'string' &&\n        getValue(requestDef.sort[0]) === 'desc') {\n      rows = rows.reverse();\n    }\n  }\n\n  if ('limit' in requestDef || 'skip' in requestDef) {\n    // have to do the limit in-memory\n    var skip = requestDef.skip || 0;\n    var limit = ('limit' in requestDef ? requestDef.limit : rows.length) + skip;\n    rows = rows.slice(skip, limit);\n  }\n  return rows;\n}\n\nfunction rowFilter(doc, selector, inMemoryFields) {\n  return inMemoryFields.every(function (field) {\n    var matcher = selector[field];\n    var parsedField = parseField(field);\n    var docFieldValue = getFieldFromDoc(doc, parsedField);\n    if (isCombinationalField(field)) {\n      return matchCominationalSelector(field, matcher, doc);\n    }\n\n    return matchSelector(matcher, doc, parsedField, docFieldValue);\n  });\n}\n\nfunction matchSelector(matcher, doc, parsedField, docFieldValue) {\n  if (!matcher) {\n    // no filtering necessary; this field is just needed for sorting\n    return true;\n  }\n\n  // is matcher an object, if so continue recursion\n  if (typeof matcher === 'object') {\n    return Object.keys(matcher).every(function (maybeUserOperator) {\n      var userValue = matcher[ maybeUserOperator ];\n      // explicit operator\n      if (maybeUserOperator.indexOf(\"$\") === 0) {\n        return match(maybeUserOperator, doc, userValue, parsedField, docFieldValue);\n      } else {\n        var subParsedField = parseField(maybeUserOperator);\n\n        if (\n          docFieldValue === undefined &&\n          typeof userValue !== \"object\" &&\n          subParsedField.length > 0\n        ) {\n          // the field does not exist, return or getFieldFromDoc will throw\n          return false;\n        }\n\n        var subDocFieldValue = getFieldFromDoc(docFieldValue, subParsedField);\n\n        if (typeof userValue === \"object\") {\n          // field value is an object that might contain more operators\n          return matchSelector(userValue, doc, parsedField, subDocFieldValue);\n        }\n\n        // implicit operator\n        return match(\"$eq\", doc, userValue, subParsedField, subDocFieldValue);\n      }\n    });\n  }\n\n  // no more depth, No need to recurse further\n  return matcher === docFieldValue;\n}\n\nfunction matchCominationalSelector(field, matcher, doc) {\n\n  if (field === '$or') {\n    return matcher.some(function (orMatchers) {\n      return rowFilter(doc, orMatchers, Object.keys(orMatchers));\n    });\n  }\n\n  if (field === '$not') {\n    return !rowFilter(doc, matcher, Object.keys(matcher));\n  }\n\n  //`$nor`\n  return !matcher.find(function (orMatchers) {\n    return rowFilter(doc, orMatchers, Object.keys(orMatchers));\n  });\n\n}\n\nfunction match(userOperator, doc, userValue, parsedField, docFieldValue) {\n  if (!matchers[userOperator]) {\n    /* istanbul ignore next */\n    throw new Error('unknown operator \"' + userOperator +\n      '\" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, ' +\n      '$nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');\n  }\n  return matchers[userOperator](doc, userValue, parsedField, docFieldValue);\n}\n\nfunction fieldExists(docFieldValue) {\n  return typeof docFieldValue !== 'undefined' && docFieldValue !== null;\n}\n\nfunction fieldIsNotUndefined(docFieldValue) {\n  return typeof docFieldValue !== 'undefined';\n}\n\nfunction modField(docFieldValue, userValue) {\n  if (typeof docFieldValue !== \"number\" ||\n    parseInt(docFieldValue, 10) !== docFieldValue) {\n    return false;\n  }\n\n  var divisor = userValue[0];\n  var mod = userValue[1];\n\n  return docFieldValue % divisor === mod;\n}\n\nfunction arrayContainsValue(docFieldValue, userValue) {\n  return userValue.some(function (val) {\n    if (docFieldValue instanceof Array) {\n      return docFieldValue.some(function (docFieldValueItem) {\n        return collate(val, docFieldValueItem) === 0;\n      });\n    }\n\n    return collate(val, docFieldValue) === 0;\n  });\n}\n\nfunction arrayContainsAllValues(docFieldValue, userValue) {\n  return userValue.every(function (val) {\n    return docFieldValue.some(function (docFieldValueItem) {\n      return collate(val, docFieldValueItem) === 0;\n    });\n  });\n}\n\nfunction arraySize(docFieldValue, userValue) {\n  return docFieldValue.length === userValue;\n}\n\nfunction regexMatch(docFieldValue, userValue) {\n  var re = new RegExp(userValue);\n\n  return re.test(docFieldValue);\n}\n\nfunction typeMatch(docFieldValue, userValue) {\n\n  switch (userValue) {\n    case 'null':\n      return docFieldValue === null;\n    case 'boolean':\n      return typeof (docFieldValue) === 'boolean';\n    case 'number':\n      return typeof (docFieldValue) === 'number';\n    case 'string':\n      return typeof (docFieldValue) === 'string';\n    case 'array':\n      return docFieldValue instanceof Array;\n    case 'object':\n      return ({}).toString.call(docFieldValue) === '[object Object]';\n  }\n}\n\nvar matchers = {\n\n  '$elemMatch': function (doc, userValue, parsedField, docFieldValue) {\n    if (!Array.isArray(docFieldValue)) {\n      return false;\n    }\n\n    if (docFieldValue.length === 0) {\n      return false;\n    }\n\n    if (typeof docFieldValue[0] === 'object' &&  docFieldValue[0] !== null) {\n      return docFieldValue.some(function (val) {\n        return rowFilter(val, userValue, Object.keys(userValue));\n      });\n    }\n\n    return docFieldValue.some(function (val) {\n      return matchSelector(userValue, doc, parsedField, val);\n    });\n  },\n\n  '$allMatch': function (doc, userValue, parsedField, docFieldValue) {\n    if (!Array.isArray(docFieldValue)) {\n      return false;\n    }\n\n    /* istanbul ignore next */\n    if (docFieldValue.length === 0) {\n      return false;\n    }\n\n    if (typeof docFieldValue[0] === 'object' &&  docFieldValue[0] !== null) {\n      return docFieldValue.every(function (val) {\n        return rowFilter(val, userValue, Object.keys(userValue));\n      });\n    }\n\n    return docFieldValue.every(function (val) {\n      return matchSelector(userValue, doc, parsedField, val);\n    });\n  },\n\n  '$eq': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) === 0;\n  },\n\n  '$gte': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) >= 0;\n  },\n\n  '$gt': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) > 0;\n  },\n\n  '$lte': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) <= 0;\n  },\n\n  '$lt': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) < 0;\n  },\n\n  '$exists': function (doc, userValue, parsedField, docFieldValue) {\n    //a field that is null is still considered to exist\n    if (userValue) {\n      return fieldIsNotUndefined(docFieldValue);\n    }\n\n    return !fieldIsNotUndefined(docFieldValue);\n  },\n\n  '$mod': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) && modField(docFieldValue, userValue);\n  },\n\n  '$ne': function (doc, userValue, parsedField, docFieldValue) {\n    return userValue.every(function (neValue) {\n      return collate(docFieldValue, neValue) !== 0;\n    });\n  },\n  '$in': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) && arrayContainsValue(docFieldValue, userValue);\n  },\n\n  '$nin': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) && !arrayContainsValue(docFieldValue, userValue);\n  },\n\n  '$size': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) &&\n      Array.isArray(docFieldValue) &&\n      arraySize(docFieldValue, userValue);\n  },\n\n  '$all': function (doc, userValue, parsedField, docFieldValue) {\n    return Array.isArray(docFieldValue) && arrayContainsAllValues(docFieldValue, userValue);\n  },\n\n  '$regex': function (doc, userValue, parsedField, docFieldValue) {\n    return fieldExists(docFieldValue) &&\n      typeof docFieldValue == \"string\" &&\n      userValue.every(function (regexValue) {\n        return regexMatch(docFieldValue, regexValue);\n      });\n  },\n\n  '$type': function (doc, userValue, parsedField, docFieldValue) {\n    return typeMatch(docFieldValue, userValue);\n  }\n};\n\n// return true if the given doc matches the supplied selector\nfunction matchesSelector(doc, selector) {\n  /* istanbul ignore if */\n  if (typeof selector !== 'object') {\n    // match the CouchDB error message\n    throw new Error('Selector error: expected a JSON object');\n  }\n\n  selector = massageSelector(selector);\n  var row = {\n    doc\n  };\n\n  var rowsMatched = filterInMemoryFields([row], { selector }, Object.keys(selector));\n  return rowsMatched && rowsMatched.length === 1;\n}\n\nfunction evalFilter(input) {\n  return scopeEval('\"use strict\";\\nreturn ' + input + ';', {});\n}\n\nfunction evalView(input) {\n  var code = [\n    'return function(doc) {',\n    '  \"use strict\";',\n    '  var emitted = false;',\n    '  var emit = function (a, b) {',\n    '    emitted = true;',\n    '  };',\n    '  var view = ' + input + ';',\n    '  view(doc);',\n    '  if (emitted) {',\n    '    return true;',\n    '  }',\n    '};'\n  ].join('\\n');\n\n  return scopeEval(code, {});\n}\n\nfunction validate(opts, callback) {\n  if (opts.selector) {\n    if (opts.filter && opts.filter !== '_selector') {\n      var filterName = typeof opts.filter === 'string' ?\n        opts.filter : 'function';\n      return callback(new Error('selector invalid for filter \"' + filterName + '\"'));\n    }\n  }\n  callback();\n}\n\nfunction normalize(opts) {\n  if (opts.view && !opts.filter) {\n    opts.filter = '_view';\n  }\n\n  if (opts.selector && !opts.filter) {\n    opts.filter = '_selector';\n  }\n\n  if (opts.filter && typeof opts.filter === 'string') {\n    if (opts.filter === '_view') {\n      opts.view = normalizeDesignDocFunctionName(opts.view);\n    } else {\n      opts.filter = normalizeDesignDocFunctionName(opts.filter);\n    }\n  }\n}\n\nfunction shouldFilter(changesHandler, opts) {\n  return opts.filter && typeof opts.filter === 'string' &&\n    !opts.doc_ids && !isRemote(changesHandler.db);\n}\n\nfunction filter(changesHandler, opts) {\n  var callback = opts.complete;\n  if (opts.filter === '_view') {\n    if (!opts.view || typeof opts.view !== 'string') {\n      var err = createError(BAD_REQUEST,\n        '`view` filter parameter not found or invalid.');\n      return callback(err);\n    }\n    // fetch a view from a design doc, make it behave like a filter\n    var viewName = parseDesignDocFunctionName(opts.view);\n    changesHandler.db.get('_design/' + viewName[0], function (err, ddoc) {\n      /* istanbul ignore if */\n      if (changesHandler.isCancelled) {\n        return callback(null, {status: 'cancelled'});\n      }\n      /* istanbul ignore next */\n      if (err) {\n        return callback(generateErrorFromResponse(err));\n      }\n      var mapFun = ddoc && ddoc.views && ddoc.views[viewName[1]] &&\n        ddoc.views[viewName[1]].map;\n      if (!mapFun) {\n        return callback(createError(MISSING_DOC,\n          (ddoc.views ? 'missing json key: ' + viewName[1] :\n            'missing json key: views')));\n      }\n      opts.filter = evalView(mapFun);\n      changesHandler.doChanges(opts);\n    });\n  } else if (opts.selector) {\n    opts.filter = function (doc) {\n      return matchesSelector(doc, opts.selector);\n    };\n    changesHandler.doChanges(opts);\n  } else {\n    // fetch a filter from a design doc\n    var filterName = parseDesignDocFunctionName(opts.filter);\n    changesHandler.db.get('_design/' + filterName[0], function (err, ddoc) {\n      /* istanbul ignore if */\n      if (changesHandler.isCancelled) {\n        return callback(null, {status: 'cancelled'});\n      }\n      /* istanbul ignore next */\n      if (err) {\n        return callback(generateErrorFromResponse(err));\n      }\n      var filterFun = ddoc && ddoc.filters && ddoc.filters[filterName[1]];\n      if (!filterFun) {\n        return callback(createError(MISSING_DOC,\n          ((ddoc && ddoc.filters) ? 'missing json key: ' + filterName[1]\n            : 'missing json key: filters')));\n      }\n      opts.filter = evalFilter(filterFun);\n      changesHandler.doChanges(opts);\n    });\n  }\n}\n\nfunction applyChangesFilterPlugin(PouchDB) {\n  PouchDB._changesFilterPlugin = {\n    validate,\n    normalize,\n    shouldFilter,\n    filter\n  };\n}\n\n// TODO: remove from pouchdb-core (breaking)\nPouchDB.plugin(applyChangesFilterPlugin);\n\nPouchDB.version = version;\n\n//\n// Blobs are not supported in all versions of IndexedDB, notably\n// Chrome <37, Android <5 and (some?) webkit-based browsers.\n// In those versions, storing a blob will throw.\n//\n// Example Webkit error:\n// > DataCloneError: Failed to store record in an IDBObjectStore: BlobURLs are not yet supported.\n//\n// Various other blob bugs exist in Chrome v37-42 (inclusive).\n// Detecting them is expensive and confusing to users, and Chrome 37-42\n// is at very low usage worldwide, so we do a hacky userAgent check instead.\n//\n// content-type bug: https://code.google.com/p/chromium/issues/detail?id=408120\n// 404 bug: https://code.google.com/p/chromium/issues/detail?id=447916\n// FileReader bug: https://code.google.com/p/chromium/issues/detail?id=447836\n//\nfunction checkBlobSupport(txn, store, docIdOrCreateDoc) {\n  return new Promise(function (resolve) {\n    var blob$$1 = createBlob(['']);\n\n    let req;\n    if (typeof docIdOrCreateDoc === 'function') {\n      // Store may require a specific key path, in which case we can't store the\n      // blob directly in the store.\n      const createDoc = docIdOrCreateDoc;\n      const doc = createDoc(blob$$1);\n      req = txn.objectStore(store).put(doc);\n    } else {\n      const docId = docIdOrCreateDoc;\n      req = txn.objectStore(store).put(blob$$1, docId);\n    }\n\n    req.onsuccess = function () {\n      var matchedChrome = navigator.userAgent.match(/Chrome\\/(\\d+)/);\n      var matchedEdge = navigator.userAgent.match(/Edge\\//);\n      // MS Edge pretends to be Chrome 42:\n      // https://msdn.microsoft.com/en-us/library/hh869301%28v=vs.85%29.aspx\n      resolve(matchedEdge || !matchedChrome ||\n        parseInt(matchedChrome[1], 10) >= 43);\n    };\n\n    req.onerror = txn.onabort = function (e) {\n      // If the transaction aborts now its due to not being able to\n      // write to the database, likely due to the disk being full\n      e.preventDefault();\n      e.stopPropagation();\n      resolve(false);\n    };\n  }).catch(function () {\n    return false; // error, so assume unsupported\n  });\n}\n\nfunction toObject(array) {\n  return array.reduce(function (obj, item) {\n    obj[item] = true;\n    return obj;\n  }, {});\n}\n// List of top level reserved words for doc\nvar reservedWords = toObject([\n  '_id',\n  '_rev',\n  '_access',\n  '_attachments',\n  '_deleted',\n  '_revisions',\n  '_revs_info',\n  '_conflicts',\n  '_deleted_conflicts',\n  '_local_seq',\n  '_rev_tree',\n  // replication documents\n  '_replication_id',\n  '_replication_state',\n  '_replication_state_time',\n  '_replication_state_reason',\n  '_replication_stats',\n  // Specific to Couchbase Sync Gateway\n  '_removed'\n]);\n\n// List of reserved words that should end up in the document\nvar dataWords = toObject([\n  '_access',\n  '_attachments',\n  // replication documents\n  '_replication_id',\n  '_replication_state',\n  '_replication_state_time',\n  '_replication_state_reason',\n  '_replication_stats'\n]);\n\nfunction parseRevisionInfo(rev$$1) {\n  if (!/^\\d+-/.test(rev$$1)) {\n    return createError(INVALID_REV);\n  }\n  var idx = rev$$1.indexOf('-');\n  var left = rev$$1.substring(0, idx);\n  var right = rev$$1.substring(idx + 1);\n  return {\n    prefix: parseInt(left, 10),\n    id: right\n  };\n}\n\nfunction makeRevTreeFromRevisions(revisions, opts) {\n  var pos = revisions.start - revisions.ids.length + 1;\n\n  var revisionIds = revisions.ids;\n  var ids = [revisionIds[0], opts, []];\n\n  for (var i = 1, len = revisionIds.length; i < len; i++) {\n    ids = [revisionIds[i], {status: 'missing'}, [ids]];\n  }\n\n  return [{\n    pos,\n    ids\n  }];\n}\n\n// Preprocess documents, parse their revisions, assign an id and a\n// revision for new writes that are missing them, etc\nfunction parseDoc(doc, newEdits, dbOpts) {\n  if (!dbOpts) {\n    dbOpts = {\n      deterministic_revs: true\n    };\n  }\n\n  var nRevNum;\n  var newRevId;\n  var revInfo;\n  var opts = {status: 'available'};\n  if (doc._deleted) {\n    opts.deleted = true;\n  }\n\n  if (newEdits) {\n    if (!doc._id) {\n      doc._id = uuid();\n    }\n    newRevId = rev(doc, dbOpts.deterministic_revs);\n    if (doc._rev) {\n      revInfo = parseRevisionInfo(doc._rev);\n      if (revInfo.error) {\n        return revInfo;\n      }\n      doc._rev_tree = [{\n        pos: revInfo.prefix,\n        ids: [revInfo.id, {status: 'missing'}, [[newRevId, opts, []]]]\n      }];\n      nRevNum = revInfo.prefix + 1;\n    } else {\n      doc._rev_tree = [{\n        pos: 1,\n        ids : [newRevId, opts, []]\n      }];\n      nRevNum = 1;\n    }\n  } else {\n    if (doc._revisions) {\n      doc._rev_tree = makeRevTreeFromRevisions(doc._revisions, opts);\n      nRevNum = doc._revisions.start;\n      newRevId = doc._revisions.ids[0];\n    }\n    if (!doc._rev_tree) {\n      revInfo = parseRevisionInfo(doc._rev);\n      if (revInfo.error) {\n        return revInfo;\n      }\n      nRevNum = revInfo.prefix;\n      newRevId = revInfo.id;\n      doc._rev_tree = [{\n        pos: nRevNum,\n        ids: [newRevId, opts, []]\n      }];\n    }\n  }\n\n  invalidIdError(doc._id);\n\n  doc._rev = nRevNum + '-' + newRevId;\n\n  var result = {metadata : {}, data : {}};\n  for (var key in doc) {\n    /* istanbul ignore else */\n    if (Object.prototype.hasOwnProperty.call(doc, key)) {\n      var specialKey = key[0] === '_';\n      if (specialKey && !reservedWords[key]) {\n        var error = createError(DOC_VALIDATION, key);\n        error.message = DOC_VALIDATION.message + ': ' + key;\n        throw error;\n      } else if (specialKey && !dataWords[key]) {\n        result.metadata[key.slice(1)] = doc[key];\n      } else {\n        result.data[key] = doc[key];\n      }\n    }\n  }\n  return result;\n}\n\nfunction parseBase64(data) {\n  try {\n    return thisAtob(data);\n  } catch (e) {\n    var err = createError(BAD_ARG,\n      'Attachment is not a valid base64 string');\n    return {error: err};\n  }\n}\n\nfunction preprocessString(att, blobType, callback) {\n  var asBinary = parseBase64(att.data);\n  if (asBinary.error) {\n    return callback(asBinary.error);\n  }\n\n  att.length = asBinary.length;\n  if (blobType === 'blob') {\n    att.data = binStringToBluffer(asBinary, att.content_type);\n  } else if (blobType === 'base64') {\n    att.data = thisBtoa(asBinary);\n  } else { // binary\n    att.data = asBinary;\n  }\n  binaryMd5(asBinary, function (result) {\n    att.digest = 'md5-' + result;\n    callback();\n  });\n}\n\nfunction preprocessBlob(att, blobType, callback) {\n  binaryMd5(att.data, function (md5) {\n    att.digest = 'md5-' + md5;\n    // size is for blobs (browser), length is for buffers (node)\n    att.length = att.data.size || att.data.length || 0;\n    if (blobType === 'binary') {\n      blobToBinaryString(att.data, function (binString) {\n        att.data = binString;\n        callback();\n      });\n    } else if (blobType === 'base64') {\n      blobToBase64(att.data, function (b64) {\n        att.data = b64;\n        callback();\n      });\n    } else {\n      callback();\n    }\n  });\n}\n\nfunction preprocessAttachment(att, blobType, callback) {\n  if (att.stub) {\n    return callback();\n  }\n  if (typeof att.data === 'string') { // input is a base64 string\n    preprocessString(att, blobType, callback);\n  } else { // input is a blob\n    preprocessBlob(att, blobType, callback);\n  }\n}\n\nfunction preprocessAttachments(docInfos, blobType, callback) {\n\n  if (!docInfos.length) {\n    return callback();\n  }\n\n  var docv = 0;\n  var overallErr;\n\n  docInfos.forEach(function (docInfo) {\n    var attachments = docInfo.data && docInfo.data._attachments ?\n      Object.keys(docInfo.data._attachments) : [];\n    var recv = 0;\n\n    if (!attachments.length) {\n      return done();\n    }\n\n    function processedAttachment(err) {\n      overallErr = err;\n      recv++;\n      if (recv === attachments.length) {\n        done();\n      }\n    }\n\n    for (var key in docInfo.data._attachments) {\n      if (Object.prototype.hasOwnProperty.call(docInfo.data._attachments, key)) {\n        preprocessAttachment(docInfo.data._attachments[key],\n          blobType, processedAttachment);\n      }\n    }\n  });\n\n  function done() {\n    docv++;\n    if (docInfos.length === docv) {\n      if (overallErr) {\n        callback(overallErr);\n      } else {\n        callback();\n      }\n    }\n  }\n}\n\nfunction updateDoc(revLimit, prev, docInfo, results,\n                   i, cb, writeDoc, newEdits) {\n\n  if (revExists(prev.rev_tree, docInfo.metadata.rev) && !newEdits) {\n    results[i] = docInfo;\n    return cb();\n  }\n\n  // sometimes this is pre-calculated. historically not always\n  var previousWinningRev = prev.winningRev || winningRev(prev);\n  var previouslyDeleted = 'deleted' in prev ? prev.deleted :\n    isDeleted(prev, previousWinningRev);\n  var deleted = 'deleted' in docInfo.metadata ? docInfo.metadata.deleted :\n    isDeleted(docInfo.metadata);\n  var isRoot = /^1-/.test(docInfo.metadata.rev);\n\n  if (previouslyDeleted && !deleted && newEdits && isRoot) {\n    var newDoc = docInfo.data;\n    newDoc._rev = previousWinningRev;\n    newDoc._id = docInfo.metadata.id;\n    docInfo = parseDoc(newDoc, newEdits);\n  }\n\n  var merged = merge(prev.rev_tree, docInfo.metadata.rev_tree[0], revLimit);\n\n  var inConflict = newEdits && ((\n    (previouslyDeleted && deleted && merged.conflicts !== 'new_leaf') ||\n    (!previouslyDeleted && merged.conflicts !== 'new_leaf') ||\n    (previouslyDeleted && !deleted && merged.conflicts === 'new_branch')));\n\n  if (inConflict) {\n    var err = createError(REV_CONFLICT);\n    results[i] = err;\n    return cb();\n  }\n\n  var newRev = docInfo.metadata.rev;\n  docInfo.metadata.rev_tree = merged.tree;\n  docInfo.stemmedRevs = merged.stemmedRevs || [];\n  /* istanbul ignore else */\n  if (prev.rev_map) {\n    docInfo.metadata.rev_map = prev.rev_map; // used only by leveldb\n  }\n\n  // recalculate\n  var winningRev$$1 = winningRev(docInfo.metadata);\n  var winningRevIsDeleted = isDeleted(docInfo.metadata, winningRev$$1);\n\n  // calculate the total number of documents that were added/removed,\n  // from the perspective of total_rows/doc_count\n  var delta = (previouslyDeleted === winningRevIsDeleted) ? 0 :\n    previouslyDeleted < winningRevIsDeleted ? -1 : 1;\n\n  var newRevIsDeleted;\n  if (newRev === winningRev$$1) {\n    // if the new rev is the same as the winning rev, we can reuse that value\n    newRevIsDeleted = winningRevIsDeleted;\n  } else {\n    // if they're not the same, then we need to recalculate\n    newRevIsDeleted = isDeleted(docInfo.metadata, newRev);\n  }\n\n  writeDoc(docInfo, winningRev$$1, winningRevIsDeleted, newRevIsDeleted,\n    true, delta, i, cb);\n}\n\nfunction rootIsMissing(docInfo) {\n  return docInfo.metadata.rev_tree[0].ids[1].status === 'missing';\n}\n\nfunction processDocs(revLimit, docInfos, api, fetchedDocs, tx, results,\n                     writeDoc, opts, overallCallback) {\n\n  // Default to 1000 locally\n  revLimit = revLimit || 1000;\n\n  function insertDoc(docInfo, resultsIdx, callback) {\n    // Cant insert new deleted documents\n    var winningRev$$1 = winningRev(docInfo.metadata);\n    var deleted = isDeleted(docInfo.metadata, winningRev$$1);\n    if ('was_delete' in opts && deleted) {\n      results[resultsIdx] = createError(MISSING_DOC, 'deleted');\n      return callback();\n    }\n\n    // 4712 - detect whether a new document was inserted with a _rev\n    var inConflict = newEdits && rootIsMissing(docInfo);\n\n    if (inConflict) {\n      var err = createError(REV_CONFLICT);\n      results[resultsIdx] = err;\n      return callback();\n    }\n\n    var delta = deleted ? 0 : 1;\n\n    writeDoc(docInfo, winningRev$$1, deleted, deleted, false,\n      delta, resultsIdx, callback);\n  }\n\n  var newEdits = opts.new_edits;\n  var idsToDocs = new Map();\n\n  var docsDone = 0;\n  var docsToDo = docInfos.length;\n\n  function checkAllDocsDone() {\n    if (++docsDone === docsToDo && overallCallback) {\n      overallCallback();\n    }\n  }\n\n  docInfos.forEach(function (currentDoc, resultsIdx) {\n\n    if (currentDoc._id && isLocalId(currentDoc._id)) {\n      var fun = currentDoc._deleted ? '_removeLocal' : '_putLocal';\n      api[fun](currentDoc, {ctx: tx}, function (err, res) {\n        results[resultsIdx] = err || res;\n        checkAllDocsDone();\n      });\n      return;\n    }\n\n    var id = currentDoc.metadata.id;\n    if (idsToDocs.has(id)) {\n      docsToDo--; // duplicate\n      idsToDocs.get(id).push([currentDoc, resultsIdx]);\n    } else {\n      idsToDocs.set(id, [[currentDoc, resultsIdx]]);\n    }\n  });\n\n  // in the case of new_edits, the user can provide multiple docs\n  // with the same id. these need to be processed sequentially\n  idsToDocs.forEach(function (docs, id) {\n    var numDone = 0;\n\n    function docWritten() {\n      if (++numDone < docs.length) {\n        nextDoc();\n      } else {\n        checkAllDocsDone();\n      }\n    }\n    function nextDoc() {\n      var value = docs[numDone];\n      var currentDoc = value[0];\n      var resultsIdx = value[1];\n\n      if (fetchedDocs.has(id)) {\n        updateDoc(revLimit, fetchedDocs.get(id), currentDoc, results,\n          resultsIdx, docWritten, writeDoc, newEdits);\n      } else {\n        // Ensure stemming applies to new writes as well\n        var merged = merge([], currentDoc.metadata.rev_tree[0], revLimit);\n        currentDoc.metadata.rev_tree = merged.tree;\n        currentDoc.stemmedRevs = merged.stemmedRevs || [];\n        insertDoc(currentDoc, resultsIdx, docWritten);\n      }\n    }\n    nextDoc();\n  });\n}\n\n// IndexedDB requires a versioned database structure, so we use the\n// version here to manage migrations.\nvar ADAPTER_VERSION = 5;\n\n// The object stores created for each database\n// DOC_STORE stores the document meta data, its revision history and state\n// Keyed by document id\nvar DOC_STORE = 'document-store';\n// BY_SEQ_STORE stores a particular version of a document, keyed by its\n// sequence id\nvar BY_SEQ_STORE = 'by-sequence';\n// Where we store attachments\nvar ATTACH_STORE = 'attach-store';\n// Where we store many-to-many relations\n// between attachment digests and seqs\nvar ATTACH_AND_SEQ_STORE = 'attach-seq-store';\n\n// Where we store database-wide meta data in a single record\n// keyed by id: META_STORE\nvar META_STORE = 'meta-store';\n// Where we store local documents\nvar LOCAL_STORE = 'local-store';\n// Where we detect blob support\nvar DETECT_BLOB_SUPPORT_STORE = 'detect-blob-support';\n\nfunction safeJsonParse(str) {\n  // This try/catch guards against stack overflow errors.\n  // JSON.parse() is faster than vuvuzela.parse() but vuvuzela\n  // cannot overflow.\n  try {\n    return JSON.parse(str);\n  } catch (e) {\n    /* istanbul ignore next */\n    return vuvuzela.parse(str);\n  }\n}\n\nfunction safeJsonStringify(json) {\n  try {\n    return JSON.stringify(json);\n  } catch (e) {\n    /* istanbul ignore next */\n    return vuvuzela.stringify(json);\n  }\n}\n\nfunction idbError(callback) {\n  return function (evt) {\n    var message = 'unknown_error';\n    if (evt.target && evt.target.error) {\n      message = evt.target.error.name || evt.target.error.message;\n    }\n    callback(createError(IDB_ERROR, message, evt.type));\n  };\n}\n\n// Unfortunately, the metadata has to be stringified\n// when it is put into the database, because otherwise\n// IndexedDB can throw errors for deeply-nested objects.\n// Originally we just used JSON.parse/JSON.stringify; now\n// we use this custom vuvuzela library that avoids recursion.\n// If we could do it all over again, we'd probably use a\n// format for the revision trees other than JSON.\nfunction encodeMetadata(metadata, winningRev, deleted) {\n  return {\n    data: safeJsonStringify(metadata),\n    winningRev,\n    deletedOrLocal: deleted ? '1' : '0',\n    seq: metadata.seq, // highest seq for this doc\n    id: metadata.id\n  };\n}\n\nfunction decodeMetadata(storedObject) {\n  if (!storedObject) {\n    return null;\n  }\n  var metadata = safeJsonParse(storedObject.data);\n  metadata.winningRev = storedObject.winningRev;\n  metadata.deleted = storedObject.deletedOrLocal === '1';\n  metadata.seq = storedObject.seq;\n  return metadata;\n}\n\n// read the doc back out from the database. we don't store the\n// _id or _rev because we already have _doc_id_rev.\nfunction decodeDoc(doc) {\n  if (!doc) {\n    return doc;\n  }\n  var idx = doc._doc_id_rev.lastIndexOf(':');\n  doc._id = doc._doc_id_rev.substring(0, idx - 1);\n  doc._rev = doc._doc_id_rev.substring(idx + 1);\n  delete doc._doc_id_rev;\n  return doc;\n}\n\n// Read a blob from the database, encoding as necessary\n// and translating from base64 if the IDB doesn't support\n// native Blobs\nfunction readBlobData(body, type, asBlob, callback) {\n  if (asBlob) {\n    if (!body) {\n      callback(createBlob([''], {type}));\n    } else if (typeof body !== 'string') { // we have blob support\n      callback(body);\n    } else { // no blob support\n      callback(b64ToBluffer(body, type));\n    }\n  } else { // as base64 string\n    if (!body) {\n      callback('');\n    } else if (typeof body !== 'string') { // we have blob support\n      readAsBinaryString(body, function (binary) {\n        callback(thisBtoa(binary));\n      });\n    } else { // no blob support\n      callback(body);\n    }\n  }\n}\n\nfunction fetchAttachmentsIfNecessary(doc, opts, txn, cb) {\n  var attachments = Object.keys(doc._attachments || {});\n  if (!attachments.length) {\n    return cb && cb();\n  }\n  var numDone = 0;\n\n  function checkDone() {\n    if (++numDone === attachments.length && cb) {\n      cb();\n    }\n  }\n\n  function fetchAttachment(doc, att) {\n    var attObj = doc._attachments[att];\n    var digest = attObj.digest;\n    var req = txn.objectStore(ATTACH_STORE).get(digest);\n    req.onsuccess = function (e) {\n      attObj.body = e.target.result.body;\n      checkDone();\n    };\n  }\n\n  attachments.forEach(function (att) {\n    if (opts.attachments && opts.include_docs) {\n      fetchAttachment(doc, att);\n    } else {\n      doc._attachments[att].stub = true;\n      checkDone();\n    }\n  });\n}\n\n// IDB-specific postprocessing necessary because\n// we don't know whether we stored a true Blob or\n// a base64-encoded string, and if it's a Blob it\n// needs to be read outside of the transaction context\nfunction postProcessAttachments(results, asBlob) {\n  return Promise.all(results.map(function (row) {\n    if (row.doc && row.doc._attachments) {\n      var attNames = Object.keys(row.doc._attachments);\n      return Promise.all(attNames.map(function (att) {\n        var attObj = row.doc._attachments[att];\n        if (!('body' in attObj)) { // already processed\n          return;\n        }\n        var body = attObj.body;\n        var type = attObj.content_type;\n        return new Promise(function (resolve) {\n          readBlobData(body, type, asBlob, function (data) {\n            row.doc._attachments[att] = Object.assign(\n              pick(attObj, ['digest', 'content_type']),\n              {data}\n            );\n            resolve();\n          });\n        });\n      }));\n    }\n  }));\n}\n\nfunction compactRevs(revs, docId, txn) {\n\n  var possiblyOrphanedDigests = [];\n  var seqStore = txn.objectStore(BY_SEQ_STORE);\n  var attStore = txn.objectStore(ATTACH_STORE);\n  var attAndSeqStore = txn.objectStore(ATTACH_AND_SEQ_STORE);\n  var count = revs.length;\n\n  function checkDone() {\n    count--;\n    if (!count) { // done processing all revs\n      deleteOrphanedAttachments();\n    }\n  }\n\n  function deleteOrphanedAttachments() {\n    if (!possiblyOrphanedDigests.length) {\n      return;\n    }\n    possiblyOrphanedDigests.forEach(function (digest) {\n      var countReq = attAndSeqStore.index('digestSeq').count(\n        IDBKeyRange.bound(\n          digest + '::', digest + '::\\uffff', false, false));\n      countReq.onsuccess = function (e) {\n        var count = e.target.result;\n        if (!count) {\n          // orphaned\n          attStore.delete(digest);\n        }\n      };\n    });\n  }\n\n  revs.forEach(function (rev$$1) {\n    var index = seqStore.index('_doc_id_rev');\n    var key = docId + \"::\" + rev$$1;\n    index.getKey(key).onsuccess = function (e) {\n      var seq = e.target.result;\n      if (typeof seq !== 'number') {\n        return checkDone();\n      }\n      seqStore.delete(seq);\n\n      var cursor = attAndSeqStore.index('seq')\n        .openCursor(IDBKeyRange.only(seq));\n\n      cursor.onsuccess = function (event) {\n        var cursor = event.target.result;\n        if (cursor) {\n          var digest = cursor.value.digestSeq.split('::')[0];\n          possiblyOrphanedDigests.push(digest);\n          attAndSeqStore.delete(cursor.primaryKey);\n          cursor.continue();\n        } else { // done\n          checkDone();\n        }\n      };\n    };\n  });\n}\n\nfunction openTransactionSafely(idb, stores, mode) {\n  try {\n    return {\n      txn: idb.transaction(stores, mode)\n    };\n  } catch (err) {\n    return {\n      error: err\n    };\n  }\n}\n\nvar changesHandler = new Changes();\n\nfunction idbBulkDocs(dbOpts, req, opts, api, idb, callback) {\n  var docInfos = req.docs;\n  var txn;\n  var docStore;\n  var bySeqStore;\n  var attachStore;\n  var attachAndSeqStore;\n  var metaStore;\n  var docInfoError;\n  var metaDoc;\n\n  for (var i = 0, len = docInfos.length; i < len; i++) {\n    var doc = docInfos[i];\n    if (doc._id && isLocalId(doc._id)) {\n      continue;\n    }\n    doc = docInfos[i] = parseDoc(doc, opts.new_edits, dbOpts);\n    if (doc.error && !docInfoError) {\n      docInfoError = doc;\n    }\n  }\n\n  if (docInfoError) {\n    return callback(docInfoError);\n  }\n\n  var allDocsProcessed = false;\n  var docCountDelta = 0;\n  var results = new Array(docInfos.length);\n  var fetchedDocs = new Map();\n  var preconditionErrored = false;\n  var blobType = api._meta.blobSupport ? 'blob' : 'base64';\n\n  preprocessAttachments(docInfos, blobType, function (err) {\n    if (err) {\n      return callback(err);\n    }\n    startTransaction();\n  });\n\n  function startTransaction() {\n\n    var stores = [\n      DOC_STORE, BY_SEQ_STORE,\n      ATTACH_STORE,\n      LOCAL_STORE, ATTACH_AND_SEQ_STORE,\n      META_STORE\n    ];\n    var txnResult = openTransactionSafely(idb, stores, 'readwrite');\n    if (txnResult.error) {\n      return callback(txnResult.error);\n    }\n    txn = txnResult.txn;\n    txn.onabort = idbError(callback);\n    txn.ontimeout = idbError(callback);\n    txn.oncomplete = complete;\n    docStore = txn.objectStore(DOC_STORE);\n    bySeqStore = txn.objectStore(BY_SEQ_STORE);\n    attachStore = txn.objectStore(ATTACH_STORE);\n    attachAndSeqStore = txn.objectStore(ATTACH_AND_SEQ_STORE);\n    metaStore = txn.objectStore(META_STORE);\n\n    metaStore.get(META_STORE).onsuccess = function (e) {\n      metaDoc = e.target.result;\n      updateDocCountIfReady();\n    };\n\n    verifyAttachments(function (err) {\n      if (err) {\n        preconditionErrored = true;\n        return callback(err);\n      }\n      fetchExistingDocs();\n    });\n  }\n\n  function onAllDocsProcessed() {\n    allDocsProcessed = true;\n    updateDocCountIfReady();\n  }\n\n  function idbProcessDocs() {\n    processDocs(dbOpts.revs_limit, docInfos, api, fetchedDocs,\n                txn, results, writeDoc, opts, onAllDocsProcessed);\n  }\n\n  function updateDocCountIfReady() {\n    if (!metaDoc || !allDocsProcessed) {\n      return;\n    }\n    // caching the docCount saves a lot of time in allDocs() and\n    // info(), which is why we go to all the trouble of doing this\n    metaDoc.docCount += docCountDelta;\n    metaStore.put(metaDoc);\n  }\n\n  function fetchExistingDocs() {\n\n    if (!docInfos.length) {\n      return;\n    }\n\n    var numFetched = 0;\n\n    function checkDone() {\n      if (++numFetched === docInfos.length) {\n        idbProcessDocs();\n      }\n    }\n\n    function readMetadata(event) {\n      var metadata = decodeMetadata(event.target.result);\n\n      if (metadata) {\n        fetchedDocs.set(metadata.id, metadata);\n      }\n      checkDone();\n    }\n\n    for (var i = 0, len = docInfos.length; i < len; i++) {\n      var docInfo = docInfos[i];\n      if (docInfo._id && isLocalId(docInfo._id)) {\n        checkDone(); // skip local docs\n        continue;\n      }\n      var req = docStore.get(docInfo.metadata.id);\n      req.onsuccess = readMetadata;\n    }\n  }\n\n  function complete() {\n    if (preconditionErrored) {\n      return;\n    }\n\n    changesHandler.notify(api._meta.name);\n    callback(null, results);\n  }\n\n  function verifyAttachment(digest, callback) {\n\n    var req = attachStore.get(digest);\n    req.onsuccess = function (e) {\n      if (!e.target.result) {\n        var err = createError(MISSING_STUB,\n          'unknown stub attachment with digest ' +\n          digest);\n        err.status = 412;\n        callback(err);\n      } else {\n        callback();\n      }\n    };\n  }\n\n  function verifyAttachments(finish) {\n\n\n    var digests = [];\n    docInfos.forEach(function (docInfo) {\n      if (docInfo.data && docInfo.data._attachments) {\n        Object.keys(docInfo.data._attachments).forEach(function (filename) {\n          var att = docInfo.data._attachments[filename];\n          if (att.stub) {\n            digests.push(att.digest);\n          }\n        });\n      }\n    });\n    if (!digests.length) {\n      return finish();\n    }\n    var numDone = 0;\n    var err;\n\n    function checkDone() {\n      if (++numDone === digests.length) {\n        finish(err);\n      }\n    }\n    digests.forEach(function (digest) {\n      verifyAttachment(digest, function (attErr) {\n        if (attErr && !err) {\n          err = attErr;\n        }\n        checkDone();\n      });\n    });\n  }\n\n  function writeDoc(docInfo, winningRev$$1, winningRevIsDeleted, newRevIsDeleted,\n                    isUpdate, delta, resultsIdx, callback) {\n\n    docInfo.metadata.winningRev = winningRev$$1;\n    docInfo.metadata.deleted = winningRevIsDeleted;\n\n    var doc = docInfo.data;\n    doc._id = docInfo.metadata.id;\n    doc._rev = docInfo.metadata.rev;\n\n    if (newRevIsDeleted) {\n      doc._deleted = true;\n    }\n\n    var hasAttachments = doc._attachments &&\n      Object.keys(doc._attachments).length;\n    if (hasAttachments) {\n      return writeAttachments(docInfo, winningRev$$1, winningRevIsDeleted,\n        isUpdate, resultsIdx, callback);\n    }\n\n    docCountDelta += delta;\n    updateDocCountIfReady();\n\n    finishDoc(docInfo, winningRev$$1, winningRevIsDeleted,\n      isUpdate, resultsIdx, callback);\n  }\n\n  function finishDoc(docInfo, winningRev$$1, winningRevIsDeleted,\n                     isUpdate, resultsIdx, callback) {\n\n    var doc = docInfo.data;\n    var metadata = docInfo.metadata;\n\n    doc._doc_id_rev = metadata.id + '::' + metadata.rev;\n    delete doc._id;\n    delete doc._rev;\n\n    function afterPutDoc(e) {\n      var revsToDelete = docInfo.stemmedRevs || [];\n\n      if (isUpdate && api.auto_compaction) {\n        revsToDelete = revsToDelete.concat(compactTree(docInfo.metadata));\n      }\n\n      if (revsToDelete && revsToDelete.length) {\n        compactRevs(revsToDelete, docInfo.metadata.id, txn);\n      }\n\n      metadata.seq = e.target.result;\n      // Current _rev is calculated from _rev_tree on read\n      // delete metadata.rev;\n      var metadataToStore = encodeMetadata(metadata, winningRev$$1,\n        winningRevIsDeleted);\n      var metaDataReq = docStore.put(metadataToStore);\n      metaDataReq.onsuccess = afterPutMetadata;\n    }\n\n    function afterPutDocError(e) {\n      // ConstraintError, need to update, not put (see #1638 for details)\n      e.preventDefault(); // avoid transaction abort\n      e.stopPropagation(); // avoid transaction onerror\n      var index = bySeqStore.index('_doc_id_rev');\n      var getKeyReq = index.getKey(doc._doc_id_rev);\n      getKeyReq.onsuccess = function (e) {\n        var putReq = bySeqStore.put(doc, e.target.result);\n        putReq.onsuccess = afterPutDoc;\n      };\n    }\n\n    function afterPutMetadata() {\n      results[resultsIdx] = {\n        ok: true,\n        id: metadata.id,\n        rev: metadata.rev\n      };\n      fetchedDocs.set(docInfo.metadata.id, docInfo.metadata);\n      insertAttachmentMappings(docInfo, metadata.seq, callback);\n    }\n\n    var putReq = bySeqStore.put(doc);\n\n    putReq.onsuccess = afterPutDoc;\n    putReq.onerror = afterPutDocError;\n  }\n\n  function writeAttachments(docInfo, winningRev$$1, winningRevIsDeleted,\n                            isUpdate, resultsIdx, callback) {\n\n\n    var doc = docInfo.data;\n\n    var numDone = 0;\n    var attachments = Object.keys(doc._attachments);\n\n    function collectResults() {\n      if (numDone === attachments.length) {\n        finishDoc(docInfo, winningRev$$1, winningRevIsDeleted,\n          isUpdate, resultsIdx, callback);\n      }\n    }\n\n    function attachmentSaved() {\n      numDone++;\n      collectResults();\n    }\n\n    attachments.forEach(function (key) {\n      var att = docInfo.data._attachments[key];\n      if (!att.stub) {\n        var data = att.data;\n        delete att.data;\n        att.revpos = parseInt(winningRev$$1, 10);\n        var digest = att.digest;\n        saveAttachment(digest, data, attachmentSaved);\n      } else {\n        numDone++;\n        collectResults();\n      }\n    });\n  }\n\n  // map seqs to attachment digests, which\n  // we will need later during compaction\n  function insertAttachmentMappings(docInfo, seq, callback) {\n\n    var attsAdded = 0;\n    var attsToAdd = Object.keys(docInfo.data._attachments || {});\n\n    if (!attsToAdd.length) {\n      return callback();\n    }\n\n    function checkDone() {\n      if (++attsAdded === attsToAdd.length) {\n        callback();\n      }\n    }\n\n    function add(att) {\n      var digest = docInfo.data._attachments[att].digest;\n      var req = attachAndSeqStore.put({\n        seq,\n        digestSeq: digest + '::' + seq\n      });\n\n      req.onsuccess = checkDone;\n      req.onerror = function (e) {\n        // this callback is for a constaint error, which we ignore\n        // because this docid/rev has already been associated with\n        // the digest (e.g. when new_edits == false)\n        e.preventDefault(); // avoid transaction abort\n        e.stopPropagation(); // avoid transaction onerror\n        checkDone();\n      };\n    }\n    for (var i = 0; i < attsToAdd.length; i++) {\n      add(attsToAdd[i]); // do in parallel\n    }\n  }\n\n  function saveAttachment(digest, data, callback) {\n\n\n    var getKeyReq = attachStore.count(digest);\n    getKeyReq.onsuccess = function (e) {\n      var count = e.target.result;\n      if (count) {\n        return callback(); // already exists\n      }\n      var newAtt = {\n        digest,\n        body: data\n      };\n      var putReq = attachStore.put(newAtt);\n      putReq.onsuccess = callback;\n    };\n  }\n}\n\n// Abstraction over IDBCursor and getAll()/getAllKeys() that allows us to batch our operations\n// while falling back to a normal IDBCursor operation on browsers that don't support getAll() or\n// getAllKeys(). This allows for a much faster implementation than just straight-up cursors, because\n// we're not processing each document one-at-a-time.\nfunction runBatchedCursor(objectStore, keyRange, descending, batchSize, onBatch) {\n\n  if (batchSize === -1) {\n    batchSize = 1000;\n  }\n\n  // Bail out of getAll()/getAllKeys() in the following cases:\n  // 1) either method is unsupported - we need both\n  // 2) batchSize is 1 (might as well use IDBCursor)\n  // 3) descending – no real way to do this via getAll()/getAllKeys()\n\n  var useGetAll = typeof objectStore.getAll === 'function' &&\n    typeof objectStore.getAllKeys === 'function' &&\n    batchSize > 1 && !descending;\n\n  var keysBatch;\n  var valuesBatch;\n  var pseudoCursor;\n\n  function onGetAll(e) {\n    valuesBatch = e.target.result;\n    if (keysBatch) {\n      onBatch(keysBatch, valuesBatch, pseudoCursor);\n    }\n  }\n\n  function onGetAllKeys(e) {\n    keysBatch = e.target.result;\n    if (valuesBatch) {\n      onBatch(keysBatch, valuesBatch, pseudoCursor);\n    }\n  }\n\n  function continuePseudoCursor() {\n    if (!keysBatch.length) { // no more results\n      return onBatch();\n    }\n    // fetch next batch, exclusive start\n    var lastKey = keysBatch[keysBatch.length - 1];\n    var newKeyRange;\n    if (keyRange && keyRange.upper) {\n      try {\n        newKeyRange = IDBKeyRange.bound(lastKey, keyRange.upper,\n          true, keyRange.upperOpen);\n      } catch (e) {\n        if (e.name === \"DataError\" && e.code === 0) {\n          return onBatch(); // we're done, startkey and endkey are equal\n        }\n      }\n    } else {\n      newKeyRange = IDBKeyRange.lowerBound(lastKey, true);\n    }\n    keyRange = newKeyRange;\n    keysBatch = null;\n    valuesBatch = null;\n    objectStore.getAll(keyRange, batchSize).onsuccess = onGetAll;\n    objectStore.getAllKeys(keyRange, batchSize).onsuccess = onGetAllKeys;\n  }\n\n  function onCursor(e) {\n    var cursor = e.target.result;\n    if (!cursor) { // done\n      return onBatch();\n    }\n    // regular IDBCursor acts like a batch where batch size is always 1\n    onBatch([cursor.key], [cursor.value], cursor);\n  }\n\n  if (useGetAll) {\n    pseudoCursor = {\"continue\": continuePseudoCursor};\n    objectStore.getAll(keyRange, batchSize).onsuccess = onGetAll;\n    objectStore.getAllKeys(keyRange, batchSize).onsuccess = onGetAllKeys;\n  } else if (descending) {\n    objectStore.openCursor(keyRange, 'prev').onsuccess = onCursor;\n  } else {\n    objectStore.openCursor(keyRange).onsuccess = onCursor;\n  }\n}\n\n// simple shim for objectStore.getAll(), falling back to IDBCursor\nfunction getAll(objectStore, keyRange, onSuccess) {\n  if (typeof objectStore.getAll === 'function') {\n    // use native getAll\n    objectStore.getAll(keyRange).onsuccess = onSuccess;\n    return;\n  }\n  // fall back to cursors\n  var values = [];\n\n  function onCursor(e) {\n    var cursor = e.target.result;\n    if (cursor) {\n      values.push(cursor.value);\n      cursor.continue();\n    } else {\n      onSuccess({\n        target: {\n          result: values\n        }\n      });\n    }\n  }\n\n  objectStore.openCursor(keyRange).onsuccess = onCursor;\n}\n\nfunction allDocsKeys(keys, docStore, onBatch) {\n  // It's not guaranteed to be returned in right order\n  var valuesBatch = new Array(keys.length);\n  var count = 0;\n  keys.forEach(function (key, index) {\n    docStore.get(key).onsuccess = function (event) {\n      if (event.target.result) {\n        valuesBatch[index] = event.target.result;\n      } else {\n        valuesBatch[index] = {key, error: 'not_found'};\n      }\n      count++;\n      if (count === keys.length) {\n        onBatch(keys, valuesBatch, {});\n      }\n    };\n  });\n}\n\nfunction createKeyRange(start, end, inclusiveEnd, key, descending) {\n  try {\n    if (start && end) {\n      if (descending) {\n        return IDBKeyRange.bound(end, start, !inclusiveEnd, false);\n      } else {\n        return IDBKeyRange.bound(start, end, false, !inclusiveEnd);\n      }\n    } else if (start) {\n      if (descending) {\n        return IDBKeyRange.upperBound(start);\n      } else {\n        return IDBKeyRange.lowerBound(start);\n      }\n    } else if (end) {\n      if (descending) {\n        return IDBKeyRange.lowerBound(end, !inclusiveEnd);\n      } else {\n        return IDBKeyRange.upperBound(end, !inclusiveEnd);\n      }\n    } else if (key) {\n      return IDBKeyRange.only(key);\n    }\n  } catch (e) {\n    return {error: e};\n  }\n  return null;\n}\n\nfunction idbAllDocs(opts, idb, callback) {\n  var start = 'startkey' in opts ? opts.startkey : false;\n  var end = 'endkey' in opts ? opts.endkey : false;\n  var key = 'key' in opts ? opts.key : false;\n  var keys = 'keys' in opts ? opts.keys : false;\n  var skip = opts.skip || 0;\n  var limit = typeof opts.limit === 'number' ? opts.limit : -1;\n  var inclusiveEnd = opts.inclusive_end !== false;\n\n  var keyRange ;\n  var keyRangeError;\n  if (!keys) {\n    keyRange = createKeyRange(start, end, inclusiveEnd, key, opts.descending);\n    keyRangeError = keyRange && keyRange.error;\n    if (keyRangeError &&\n      !(keyRangeError.name === \"DataError\" && keyRangeError.code === 0)) {\n      // DataError with error code 0 indicates start is less than end, so\n      // can just do an empty query. Else need to throw\n      return callback(createError(IDB_ERROR,\n        keyRangeError.name, keyRangeError.message));\n    }\n  }\n\n  var stores = [DOC_STORE, BY_SEQ_STORE, META_STORE];\n\n  if (opts.attachments) {\n    stores.push(ATTACH_STORE);\n  }\n  var txnResult = openTransactionSafely(idb, stores, 'readonly');\n  if (txnResult.error) {\n    return callback(txnResult.error);\n  }\n  var txn = txnResult.txn;\n  txn.oncomplete = onTxnComplete;\n  txn.onabort = idbError(callback);\n  var docStore = txn.objectStore(DOC_STORE);\n  var seqStore = txn.objectStore(BY_SEQ_STORE);\n  var metaStore = txn.objectStore(META_STORE);\n  var docIdRevIndex = seqStore.index('_doc_id_rev');\n  var results = [];\n  var docCount;\n  var updateSeq;\n\n  metaStore.get(META_STORE).onsuccess = function (e) {\n    docCount = e.target.result.docCount;\n  };\n\n  /* istanbul ignore if */\n  if (opts.update_seq) {\n    // get max updateSeq\n    seqStore.openKeyCursor(null, 'prev').onsuccess = e => {\n      var cursor = e.target.result;\n      if (cursor && cursor.key) {\n        updateSeq = cursor.key;\n      }\n    };\n  }\n\n  // if the user specifies include_docs=true, then we don't\n  // want to block the main cursor while we're fetching the doc\n  function fetchDocAsynchronously(metadata, row, winningRev$$1) {\n    var key = metadata.id + \"::\" + winningRev$$1;\n    docIdRevIndex.get(key).onsuccess =  function onGetDoc(e) {\n      row.doc = decodeDoc(e.target.result) || {};\n      if (opts.conflicts) {\n        var conflicts = collectConflicts(metadata);\n        if (conflicts.length) {\n          row.doc._conflicts = conflicts;\n        }\n      }\n      fetchAttachmentsIfNecessary(row.doc, opts, txn);\n    };\n  }\n\n  function allDocsInner(winningRev$$1, metadata) {\n    var row = {\n      id: metadata.id,\n      key: metadata.id,\n      value: {\n        rev: winningRev$$1\n      }\n    };\n    var deleted = metadata.deleted;\n    if (deleted) {\n      if (keys) {\n        results.push(row);\n        // deleted docs are okay with \"keys\" requests\n        row.value.deleted = true;\n        row.doc = null;\n      }\n    } else if (skip-- <= 0) {\n      results.push(row);\n      if (opts.include_docs) {\n        fetchDocAsynchronously(metadata, row, winningRev$$1);\n      }\n    }\n  }\n\n  function processBatch(batchValues) {\n    for (var i = 0, len = batchValues.length; i < len; i++) {\n      if (results.length === limit) {\n        break;\n      }\n      var batchValue = batchValues[i];\n      if (batchValue.error && keys) {\n        // key was not found with \"keys\" requests\n        results.push(batchValue);\n        continue;\n      }\n      var metadata = decodeMetadata(batchValue);\n      var winningRev$$1 = metadata.winningRev;\n      allDocsInner(winningRev$$1, metadata);\n    }\n  }\n\n  function onBatch(batchKeys, batchValues, cursor) {\n    if (!cursor) {\n      return;\n    }\n    processBatch(batchValues);\n    if (results.length < limit) {\n      cursor.continue();\n    }\n  }\n\n  function onGetAll(e) {\n    var values = e.target.result;\n    if (opts.descending) {\n      values = values.reverse();\n    }\n    processBatch(values);\n  }\n\n  function onResultsReady() {\n    var returnVal = {\n      total_rows: docCount,\n      offset: opts.skip,\n      rows: results\n    };\n\n    /* istanbul ignore if */\n    if (opts.update_seq && updateSeq !== undefined) {\n      returnVal.update_seq = updateSeq;\n    }\n    callback(null, returnVal);\n  }\n\n  function onTxnComplete() {\n    if (opts.attachments) {\n      postProcessAttachments(results, opts.binary).then(onResultsReady);\n    } else {\n      onResultsReady();\n    }\n  }\n\n  // don't bother doing any requests if start > end or limit === 0\n  if (keyRangeError || limit === 0) {\n    return;\n  }\n  if (keys) {\n    return allDocsKeys(keys, docStore, onBatch);\n  }\n  if (limit === -1) { // just fetch everything\n    return getAll(docStore, keyRange, onGetAll);\n  }\n  // else do a cursor\n  // choose a batch size based on the skip, since we'll need to skip that many\n  runBatchedCursor(docStore, keyRange, opts.descending, limit + skip, onBatch);\n}\n\nfunction countDocs(txn, cb) {\n  var index = txn.objectStore(DOC_STORE).index('deletedOrLocal');\n  index.count(IDBKeyRange.only('0')).onsuccess = function (e) {\n    cb(e.target.result);\n  };\n}\n\n// This task queue ensures that IDB open calls are done in their own tick\n\nvar running = false;\nvar queue = [];\n\nfunction tryCode(fun, err, res, PouchDB) {\n  try {\n    fun(err, res);\n  } catch (err) {\n    // Shouldn't happen, but in some odd cases\n    // IndexedDB implementations might throw a sync\n    // error, in which case this will at least log it.\n    PouchDB.emit('error', err);\n  }\n}\n\nfunction applyNext() {\n  if (running || !queue.length) {\n    return;\n  }\n  running = true;\n  queue.shift()();\n}\n\nfunction enqueueTask(action, callback, PouchDB) {\n  queue.push(function runAction() {\n    action(function runCallback(err, res) {\n      tryCode(callback, err, res, PouchDB);\n      running = false;\n      nextTick(function runNext() {\n        applyNext(PouchDB);\n      });\n    });\n  });\n  applyNext();\n}\n\nfunction changes(opts, api, dbName, idb) {\n  opts = clone(opts);\n\n  if (opts.continuous) {\n    var id = dbName + ':' + uuid();\n    changesHandler.addListener(dbName, id, api, opts);\n    changesHandler.notify(dbName);\n    return {\n      cancel: function () {\n        changesHandler.removeListener(dbName, id);\n      }\n    };\n  }\n\n  var docIds = opts.doc_ids && new Set(opts.doc_ids);\n\n  opts.since = opts.since || 0;\n  var lastSeq = opts.since;\n\n  var limit = 'limit' in opts ? opts.limit : -1;\n  if (limit === 0) {\n    limit = 1; // per CouchDB _changes spec\n  }\n\n  var results = [];\n  var numResults = 0;\n  var filter = filterChange(opts);\n  var docIdsToMetadata = new Map();\n\n  var txn;\n  var bySeqStore;\n  var docStore;\n  var docIdRevIndex;\n\n  function onBatch(batchKeys, batchValues, cursor) {\n    if (!cursor || !batchKeys.length) { // done\n      return;\n    }\n\n    var winningDocs = new Array(batchKeys.length);\n    var metadatas = new Array(batchKeys.length);\n\n    function processMetadataAndWinningDoc(metadata, winningDoc) {\n      var change = opts.processChange(winningDoc, metadata, opts);\n      lastSeq = change.seq = metadata.seq;\n\n      var filtered = filter(change);\n      if (typeof filtered === 'object') { // anything but true/false indicates error\n        return Promise.reject(filtered);\n      }\n\n      if (!filtered) {\n        return Promise.resolve();\n      }\n      numResults++;\n      if (opts.return_docs) {\n        results.push(change);\n      }\n      // process the attachment immediately\n      // for the benefit of live listeners\n      if (opts.attachments && opts.include_docs) {\n        return new Promise(function (resolve) {\n          fetchAttachmentsIfNecessary(winningDoc, opts, txn, function () {\n            postProcessAttachments([change], opts.binary).then(function () {\n              resolve(change);\n            });\n          });\n        });\n      } else {\n        return Promise.resolve(change);\n      }\n    }\n\n    function onBatchDone() {\n      var promises = [];\n      for (var i = 0, len = winningDocs.length; i < len; i++) {\n        if (numResults === limit) {\n          break;\n        }\n        var winningDoc = winningDocs[i];\n        if (!winningDoc) {\n          continue;\n        }\n        var metadata = metadatas[i];\n        promises.push(processMetadataAndWinningDoc(metadata, winningDoc));\n      }\n\n      Promise.all(promises).then(function (changes) {\n        for (var i = 0, len = changes.length; i < len; i++) {\n          if (changes[i]) {\n            opts.onChange(changes[i]);\n          }\n        }\n      }).catch(opts.complete);\n\n      if (numResults !== limit) {\n        cursor.continue();\n      }\n    }\n\n    // Fetch all metadatas/winningdocs from this batch in parallel, then process\n    // them all only once all data has been collected. This is done in parallel\n    // because it's faster than doing it one-at-a-time.\n    var numDone = 0;\n    batchValues.forEach(function (value, i) {\n      var doc = decodeDoc(value);\n      var seq = batchKeys[i];\n      fetchWinningDocAndMetadata(doc, seq, function (metadata, winningDoc) {\n        metadatas[i] = metadata;\n        winningDocs[i] = winningDoc;\n        if (++numDone === batchKeys.length) {\n          onBatchDone();\n        }\n      });\n    });\n  }\n\n  function onGetMetadata(doc, seq, metadata, cb) {\n    if (metadata.seq !== seq) {\n      // some other seq is later\n      return cb();\n    }\n\n    if (metadata.winningRev === doc._rev) {\n      // this is the winning doc\n      return cb(metadata, doc);\n    }\n\n    // fetch winning doc in separate request\n    var docIdRev = doc._id + '::' + metadata.winningRev;\n    var req = docIdRevIndex.get(docIdRev);\n    req.onsuccess = function (e) {\n      cb(metadata, decodeDoc(e.target.result));\n    };\n  }\n\n  function fetchWinningDocAndMetadata(doc, seq, cb) {\n    if (docIds && !docIds.has(doc._id)) {\n      return cb();\n    }\n\n    var metadata = docIdsToMetadata.get(doc._id);\n    if (metadata) { // cached\n      return onGetMetadata(doc, seq, metadata, cb);\n    }\n    // metadata not cached, have to go fetch it\n    docStore.get(doc._id).onsuccess = function (e) {\n      metadata = decodeMetadata(e.target.result);\n      docIdsToMetadata.set(doc._id, metadata);\n      onGetMetadata(doc, seq, metadata, cb);\n    };\n  }\n\n  function finish() {\n    opts.complete(null, {\n      results,\n      last_seq: lastSeq\n    });\n  }\n\n  function onTxnComplete() {\n    if (!opts.continuous && opts.attachments) {\n      // cannot guarantee that postProcessing was already done,\n      // so do it again\n      postProcessAttachments(results).then(finish);\n    } else {\n      finish();\n    }\n  }\n\n  var objectStores = [DOC_STORE, BY_SEQ_STORE];\n  if (opts.attachments) {\n    objectStores.push(ATTACH_STORE);\n  }\n  var txnResult = openTransactionSafely(idb, objectStores, 'readonly');\n  if (txnResult.error) {\n    return opts.complete(txnResult.error);\n  }\n  txn = txnResult.txn;\n  txn.onabort = idbError(opts.complete);\n  txn.oncomplete = onTxnComplete;\n\n  bySeqStore = txn.objectStore(BY_SEQ_STORE);\n  docStore = txn.objectStore(DOC_STORE);\n  docIdRevIndex = bySeqStore.index('_doc_id_rev');\n\n  var keyRange = (opts.since && !opts.descending) ?\n    IDBKeyRange.lowerBound(opts.since, true) : null;\n\n  runBatchedCursor(bySeqStore, keyRange, opts.descending, limit, onBatch);\n}\n\nvar cachedDBs = new Map();\nvar blobSupportPromise;\nvar openReqList = new Map();\n\nfunction IdbPouch(opts, callback) {\n  var api = this;\n\n  enqueueTask(function (thisCallback) {\n    init(api, opts, thisCallback);\n  }, callback, api.constructor);\n}\n\nfunction init(api, opts, callback) {\n\n  var dbName = opts.name;\n\n  var idb = null;\n  var idbGlobalFailureError = null;\n  api._meta = null;\n\n  function enrichCallbackError(callback) {\n    return function (error, result) {\n      if (error && error instanceof Error && !error.reason) {\n        if (idbGlobalFailureError) {\n          error.reason = idbGlobalFailureError;\n        }\n      }\n\n      callback(error, result);\n    };\n  }\n\n  // called when creating a fresh new database\n  function createSchema(db) {\n    var docStore = db.createObjectStore(DOC_STORE, {keyPath : 'id'});\n    db.createObjectStore(BY_SEQ_STORE, {autoIncrement: true})\n      .createIndex('_doc_id_rev', '_doc_id_rev', {unique: true});\n    db.createObjectStore(ATTACH_STORE, {keyPath: 'digest'});\n    db.createObjectStore(META_STORE, {keyPath: 'id', autoIncrement: false});\n    db.createObjectStore(DETECT_BLOB_SUPPORT_STORE);\n\n    // added in v2\n    docStore.createIndex('deletedOrLocal', 'deletedOrLocal', {unique : false});\n\n    // added in v3\n    db.createObjectStore(LOCAL_STORE, {keyPath: '_id'});\n\n    // added in v4\n    var attAndSeqStore = db.createObjectStore(ATTACH_AND_SEQ_STORE,\n      {autoIncrement: true});\n    attAndSeqStore.createIndex('seq', 'seq');\n    attAndSeqStore.createIndex('digestSeq', 'digestSeq', {unique: true});\n  }\n\n  // migration to version 2\n  // unfortunately \"deletedOrLocal\" is a misnomer now that we no longer\n  // store local docs in the main doc-store, but whaddyagonnado\n  function addDeletedOrLocalIndex(txn, callback) {\n    var docStore = txn.objectStore(DOC_STORE);\n    docStore.createIndex('deletedOrLocal', 'deletedOrLocal', {unique : false});\n\n    docStore.openCursor().onsuccess = function (event) {\n      var cursor = event.target.result;\n      if (cursor) {\n        var metadata = cursor.value;\n        var deleted = isDeleted(metadata);\n        metadata.deletedOrLocal = deleted ? \"1\" : \"0\";\n        docStore.put(metadata);\n        cursor.continue();\n      } else {\n        callback();\n      }\n    };\n  }\n\n  // migration to version 3 (part 1)\n  function createLocalStoreSchema(db) {\n    db.createObjectStore(LOCAL_STORE, {keyPath: '_id'})\n      .createIndex('_doc_id_rev', '_doc_id_rev', {unique: true});\n  }\n\n  // migration to version 3 (part 2)\n  function migrateLocalStore(txn, cb) {\n    var localStore = txn.objectStore(LOCAL_STORE);\n    var docStore = txn.objectStore(DOC_STORE);\n    var seqStore = txn.objectStore(BY_SEQ_STORE);\n\n    var cursor = docStore.openCursor();\n    cursor.onsuccess = function (event) {\n      var cursor = event.target.result;\n      if (cursor) {\n        var metadata = cursor.value;\n        var docId = metadata.id;\n        var local = isLocalId(docId);\n        var rev$$1 = winningRev(metadata);\n        if (local) {\n          var docIdRev = docId + \"::\" + rev$$1;\n          // remove all seq entries\n          // associated with this docId\n          var start = docId + \"::\";\n          var end = docId + \"::~\";\n          var index = seqStore.index('_doc_id_rev');\n          var range = IDBKeyRange.bound(start, end, false, false);\n          var seqCursor = index.openCursor(range);\n          seqCursor.onsuccess = function (e) {\n            seqCursor = e.target.result;\n            if (!seqCursor) {\n              // done\n              docStore.delete(cursor.primaryKey);\n              cursor.continue();\n            } else {\n              var data = seqCursor.value;\n              if (data._doc_id_rev === docIdRev) {\n                localStore.put(data);\n              }\n              seqStore.delete(seqCursor.primaryKey);\n              seqCursor.continue();\n            }\n          };\n        } else {\n          cursor.continue();\n        }\n      } else if (cb) {\n        cb();\n      }\n    };\n  }\n\n  // migration to version 4 (part 1)\n  function addAttachAndSeqStore(db) {\n    var attAndSeqStore = db.createObjectStore(ATTACH_AND_SEQ_STORE,\n      {autoIncrement: true});\n    attAndSeqStore.createIndex('seq', 'seq');\n    attAndSeqStore.createIndex('digestSeq', 'digestSeq', {unique: true});\n  }\n\n  // migration to version 4 (part 2)\n  function migrateAttsAndSeqs(txn, callback) {\n    var seqStore = txn.objectStore(BY_SEQ_STORE);\n    var attStore = txn.objectStore(ATTACH_STORE);\n    var attAndSeqStore = txn.objectStore(ATTACH_AND_SEQ_STORE);\n\n    // need to actually populate the table. this is the expensive part,\n    // so as an optimization, check first that this database even\n    // contains attachments\n    var req = attStore.count();\n    req.onsuccess = function (e) {\n      var count = e.target.result;\n      if (!count) {\n        return callback(); // done\n      }\n\n      seqStore.openCursor().onsuccess = function (e) {\n        var cursor = e.target.result;\n        if (!cursor) {\n          return callback(); // done\n        }\n        var doc = cursor.value;\n        var seq = cursor.primaryKey;\n        var atts = Object.keys(doc._attachments || {});\n        var digestMap = {};\n        for (var j = 0; j < atts.length; j++) {\n          var att = doc._attachments[atts[j]];\n          digestMap[att.digest] = true; // uniq digests, just in case\n        }\n        var digests = Object.keys(digestMap);\n        for (j = 0; j < digests.length; j++) {\n          var digest = digests[j];\n          attAndSeqStore.put({\n            seq,\n            digestSeq: digest + '::' + seq\n          });\n        }\n        cursor.continue();\n      };\n    };\n  }\n\n  // migration to version 5\n  // Instead of relying on on-the-fly migration of metadata,\n  // this brings the doc-store to its modern form:\n  // - metadata.winningrev\n  // - metadata.seq\n  // - stringify the metadata when storing it\n  function migrateMetadata(txn) {\n\n    function decodeMetadataCompat(storedObject) {\n      if (!storedObject.data) {\n        // old format, when we didn't store it stringified\n        storedObject.deleted = storedObject.deletedOrLocal === '1';\n        return storedObject;\n      }\n      return decodeMetadata(storedObject);\n    }\n\n    // ensure that every metadata has a winningRev and seq,\n    // which was previously created on-the-fly but better to migrate\n    var bySeqStore = txn.objectStore(BY_SEQ_STORE);\n    var docStore = txn.objectStore(DOC_STORE);\n    var cursor = docStore.openCursor();\n    cursor.onsuccess = function (e) {\n      var cursor = e.target.result;\n      if (!cursor) {\n        return; // done\n      }\n      var metadata = decodeMetadataCompat(cursor.value);\n\n      metadata.winningRev = metadata.winningRev ||\n        winningRev(metadata);\n\n      function fetchMetadataSeq() {\n        // metadata.seq was added post-3.2.0, so if it's missing,\n        // we need to fetch it manually\n        var start = metadata.id + '::';\n        var end = metadata.id + '::\\uffff';\n        var req = bySeqStore.index('_doc_id_rev').openCursor(\n          IDBKeyRange.bound(start, end));\n\n        var metadataSeq = 0;\n        req.onsuccess = function (e) {\n          var cursor = e.target.result;\n          if (!cursor) {\n            metadata.seq = metadataSeq;\n            return onGetMetadataSeq();\n          }\n          var seq = cursor.primaryKey;\n          if (seq > metadataSeq) {\n            metadataSeq = seq;\n          }\n          cursor.continue();\n        };\n      }\n\n      function onGetMetadataSeq() {\n        var metadataToStore = encodeMetadata(metadata,\n          metadata.winningRev, metadata.deleted);\n\n        var req = docStore.put(metadataToStore);\n        req.onsuccess = function () {\n          cursor.continue();\n        };\n      }\n\n      if (metadata.seq) {\n        return onGetMetadataSeq();\n      }\n\n      fetchMetadataSeq();\n    };\n\n  }\n\n  api._remote = false;\n  api.type = function () {\n    return 'idb';\n  };\n\n  api._id = toPromise(function (callback) {\n    callback(null, api._meta.instanceId);\n  });\n\n  api._bulkDocs = function idb_bulkDocs(req, reqOpts, callback) {\n    idbBulkDocs(opts, req, reqOpts, api, idb, enrichCallbackError(callback));\n  };\n\n  // First we look up the metadata in the ids database, then we fetch the\n  // current revision(s) from the by sequence store\n  api._get = function idb_get(id, opts, callback) {\n    var doc;\n    var metadata;\n    var err;\n    var txn = opts.ctx;\n    if (!txn) {\n      var txnResult = openTransactionSafely(idb,\n        [DOC_STORE, BY_SEQ_STORE, ATTACH_STORE], 'readonly');\n      if (txnResult.error) {\n        return callback(txnResult.error);\n      }\n      txn = txnResult.txn;\n    }\n\n    function finish() {\n      callback(err, {doc, metadata, ctx: txn});\n    }\n\n    txn.objectStore(DOC_STORE).get(id).onsuccess = function (e) {\n      metadata = decodeMetadata(e.target.result);\n      // we can determine the result here if:\n      // 1. there is no such document\n      // 2. the document is deleted and we don't ask about specific rev\n      // When we ask with opts.rev we expect the answer to be either\n      // doc (possibly with _deleted=true) or missing error\n      if (!metadata) {\n        err = createError(MISSING_DOC, 'missing');\n        return finish();\n      }\n\n      var rev$$1;\n      if (!opts.rev) {\n        rev$$1 = metadata.winningRev;\n        var deleted = isDeleted(metadata);\n        if (deleted) {\n          err = createError(MISSING_DOC, \"deleted\");\n          return finish();\n        }\n      } else {\n        rev$$1 = opts.latest ? latest(opts.rev, metadata) : opts.rev;\n      }\n\n      var objectStore = txn.objectStore(BY_SEQ_STORE);\n      var key = metadata.id + '::' + rev$$1;\n\n      objectStore.index('_doc_id_rev').get(key).onsuccess = function (e) {\n        doc = e.target.result;\n        if (doc) {\n          doc = decodeDoc(doc);\n        }\n        if (!doc) {\n          err = createError(MISSING_DOC, 'missing');\n          return finish();\n        }\n        finish();\n      };\n    };\n  };\n\n  api._getAttachment = function (docId, attachId, attachment, opts, callback) {\n    var txn;\n    if (opts.ctx) {\n      txn = opts.ctx;\n    } else {\n      var txnResult = openTransactionSafely(idb,\n        [DOC_STORE, BY_SEQ_STORE, ATTACH_STORE], 'readonly');\n      if (txnResult.error) {\n        return callback(txnResult.error);\n      }\n      txn = txnResult.txn;\n    }\n    var digest = attachment.digest;\n    var type = attachment.content_type;\n\n    txn.objectStore(ATTACH_STORE).get(digest).onsuccess = function (e) {\n      var body = e.target.result.body;\n      readBlobData(body, type, opts.binary, function (blobData) {\n        callback(null, blobData);\n      });\n    };\n  };\n\n  api._info = function idb_info(callback) {\n    var updateSeq;\n    var docCount;\n\n    var txnResult = openTransactionSafely(idb, [META_STORE, BY_SEQ_STORE], 'readonly');\n    if (txnResult.error) {\n      return callback(txnResult.error);\n    }\n    var txn = txnResult.txn;\n    txn.objectStore(META_STORE).get(META_STORE).onsuccess = function (e) {\n      docCount = e.target.result.docCount;\n    };\n    txn.objectStore(BY_SEQ_STORE).openKeyCursor(null, 'prev').onsuccess = function (e) {\n      var cursor = e.target.result;\n      updateSeq = cursor ? cursor.key : 0;\n    };\n\n    txn.oncomplete = function () {\n      callback(null, {\n        doc_count: docCount,\n        update_seq: updateSeq,\n        // for debugging\n        idb_attachment_format: (api._meta.blobSupport ? 'binary' : 'base64')\n      });\n    };\n  };\n\n  api._allDocs = function idb_allDocs(opts, callback) {\n    idbAllDocs(opts, idb, enrichCallbackError(callback));\n  };\n\n  api._changes = function idbChanges(opts) {\n    return changes(opts, api, dbName, idb);\n  };\n\n  api._close = function (callback) {\n    // https://developer.mozilla.org/en-US/docs/IndexedDB/IDBDatabase#close\n    // \"Returns immediately and closes the connection in a separate thread...\"\n    idb.close();\n    cachedDBs.delete(dbName);\n    callback();\n  };\n\n  api._getRevisionTree = function (docId, callback) {\n    var txnResult = openTransactionSafely(idb, [DOC_STORE], 'readonly');\n    if (txnResult.error) {\n      return callback(txnResult.error);\n    }\n    var txn = txnResult.txn;\n    var req = txn.objectStore(DOC_STORE).get(docId);\n    req.onsuccess = function (event) {\n      var doc = decodeMetadata(event.target.result);\n      if (!doc) {\n        callback(createError(MISSING_DOC));\n      } else {\n        callback(null, doc.rev_tree);\n      }\n    };\n  };\n\n  // This function removes revisions of document docId\n  // which are listed in revs and sets this document\n  // revision to to rev_tree\n  api._doCompaction = function (docId, revs, callback) {\n    var stores = [\n      DOC_STORE,\n      BY_SEQ_STORE,\n      ATTACH_STORE,\n      ATTACH_AND_SEQ_STORE\n    ];\n    var txnResult = openTransactionSafely(idb, stores, 'readwrite');\n    if (txnResult.error) {\n      return callback(txnResult.error);\n    }\n    var txn = txnResult.txn;\n\n    var docStore = txn.objectStore(DOC_STORE);\n\n    docStore.get(docId).onsuccess = function (event) {\n      var metadata = decodeMetadata(event.target.result);\n      traverseRevTree(metadata.rev_tree, function (isLeaf, pos,\n                                                         revHash, ctx, opts) {\n        var rev$$1 = pos + '-' + revHash;\n        if (revs.indexOf(rev$$1) !== -1) {\n          opts.status = 'missing';\n        }\n      });\n      compactRevs(revs, docId, txn);\n      var winningRev$$1 = metadata.winningRev;\n      var deleted = metadata.deleted;\n      txn.objectStore(DOC_STORE).put(\n        encodeMetadata(metadata, winningRev$$1, deleted));\n    };\n    txn.onabort = idbError(callback);\n    txn.oncomplete = function () {\n      callback();\n    };\n  };\n\n\n  api._getLocal = function (id, callback) {\n    var txnResult = openTransactionSafely(idb, [LOCAL_STORE], 'readonly');\n    if (txnResult.error) {\n      return callback(txnResult.error);\n    }\n    var tx = txnResult.txn;\n    var req = tx.objectStore(LOCAL_STORE).get(id);\n\n    req.onerror = idbError(callback);\n    req.onsuccess = function (e) {\n      var doc = e.target.result;\n      if (!doc) {\n        callback(createError(MISSING_DOC));\n      } else {\n        delete doc['_doc_id_rev']; // for backwards compat\n        callback(null, doc);\n      }\n    };\n  };\n\n  api._putLocal = function (doc, opts, callback) {\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n    delete doc._revisions; // ignore this, trust the rev\n    var oldRev = doc._rev;\n    var id = doc._id;\n    if (!oldRev) {\n      doc._rev = '0-1';\n    } else {\n      doc._rev = '0-' + (parseInt(oldRev.split('-')[1], 10) + 1);\n    }\n\n    var tx = opts.ctx;\n    var ret;\n    if (!tx) {\n      var txnResult = openTransactionSafely(idb, [LOCAL_STORE], 'readwrite');\n      if (txnResult.error) {\n        return callback(txnResult.error);\n      }\n      tx = txnResult.txn;\n      tx.onerror = idbError(callback);\n      tx.oncomplete = function () {\n        if (ret) {\n          callback(null, ret);\n        }\n      };\n    }\n\n    var oStore = tx.objectStore(LOCAL_STORE);\n    var req;\n    if (oldRev) {\n      req = oStore.get(id);\n      req.onsuccess = function (e) {\n        var oldDoc = e.target.result;\n        if (!oldDoc || oldDoc._rev !== oldRev) {\n          callback(createError(REV_CONFLICT));\n        } else { // update\n          var req = oStore.put(doc);\n          req.onsuccess = function () {\n            ret = {ok: true, id: doc._id, rev: doc._rev};\n            if (opts.ctx) { // return immediately\n              callback(null, ret);\n            }\n          };\n        }\n      };\n    } else { // new doc\n      req = oStore.add(doc);\n      req.onerror = function (e) {\n        // constraint error, already exists\n        callback(createError(REV_CONFLICT));\n        e.preventDefault(); // avoid transaction abort\n        e.stopPropagation(); // avoid transaction onerror\n      };\n      req.onsuccess = function () {\n        ret = {ok: true, id: doc._id, rev: doc._rev};\n        if (opts.ctx) { // return immediately\n          callback(null, ret);\n        }\n      };\n    }\n  };\n\n  api._removeLocal = function (doc, opts, callback) {\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n    var tx = opts.ctx;\n    if (!tx) {\n      var txnResult = openTransactionSafely(idb, [LOCAL_STORE], 'readwrite');\n      if (txnResult.error) {\n        return callback(txnResult.error);\n      }\n      tx = txnResult.txn;\n      tx.oncomplete = function () {\n        if (ret) {\n          callback(null, ret);\n        }\n      };\n    }\n    var ret;\n    var id = doc._id;\n    var oStore = tx.objectStore(LOCAL_STORE);\n    var req = oStore.get(id);\n\n    req.onerror = idbError(callback);\n    req.onsuccess = function (e) {\n      var oldDoc = e.target.result;\n      if (!oldDoc || oldDoc._rev !== doc._rev) {\n        callback(createError(MISSING_DOC));\n      } else {\n        oStore.delete(id);\n        ret = {ok: true, id, rev: '0-0'};\n        if (opts.ctx) { // return immediately\n          callback(null, ret);\n        }\n      }\n    };\n  };\n\n  api._destroy = function (opts, callback) {\n    changesHandler.removeAllListeners(dbName);\n\n    //Close open request for \"dbName\" database to fix ie delay.\n    var openReq = openReqList.get(dbName);\n    if (openReq && openReq.result) {\n      openReq.result.close();\n      cachedDBs.delete(dbName);\n    }\n    var req = indexedDB.deleteDatabase(dbName);\n\n    req.onsuccess = function () {\n      //Remove open request from the list.\n      openReqList.delete(dbName);\n      if (hasLocalStorage() && (dbName in localStorage)) {\n        delete localStorage[dbName];\n      }\n      callback(null, { 'ok': true });\n    };\n\n    req.onerror = idbError(callback);\n  };\n\n  var cached = cachedDBs.get(dbName);\n\n  if (cached) {\n    idb = cached.idb;\n    api._meta = cached.global;\n    return nextTick(function () {\n      callback(null, api);\n    });\n  }\n\n  var req = indexedDB.open(dbName, ADAPTER_VERSION);\n  openReqList.set(dbName, req);\n\n  req.onupgradeneeded = function (e) {\n    var db = e.target.result;\n    if (e.oldVersion < 1) {\n      return createSchema(db); // new db, initial schema\n    }\n    // do migrations\n\n    var txn = e.currentTarget.transaction;\n    // these migrations have to be done in this function, before\n    // control is returned to the event loop, because IndexedDB\n\n    if (e.oldVersion < 3) {\n      createLocalStoreSchema(db); // v2 -> v3\n    }\n    if (e.oldVersion < 4) {\n      addAttachAndSeqStore(db); // v3 -> v4\n    }\n\n    var migrations = [\n      addDeletedOrLocalIndex, // v1 -> v2\n      migrateLocalStore,      // v2 -> v3\n      migrateAttsAndSeqs,     // v3 -> v4\n      migrateMetadata         // v4 -> v5\n    ];\n\n    var i = e.oldVersion;\n\n    function next() {\n      var migration = migrations[i - 1];\n      i++;\n      if (migration) {\n        migration(txn, next);\n      }\n    }\n\n    next();\n  };\n\n  req.onsuccess = function (e) {\n\n    idb = e.target.result;\n\n    idb.onversionchange = function () {\n      idb.close();\n      cachedDBs.delete(dbName);\n    };\n\n    idb.onabort = function (e) {\n      guardedConsole('error', 'Database has a global failure', e.target.error);\n      idbGlobalFailureError = e.target.error;\n      idb.close();\n      cachedDBs.delete(dbName);\n    };\n\n    // Do a few setup operations (in parallel as much as possible):\n    // 1. Fetch meta doc\n    // 2. Check blob support\n    // 3. Calculate docCount\n    // 4. Generate an instanceId if necessary\n    // 5. Store docCount and instanceId on meta doc\n\n    var txn = idb.transaction([\n      META_STORE,\n      DETECT_BLOB_SUPPORT_STORE,\n      DOC_STORE\n    ], 'readwrite');\n\n    var storedMetaDoc = false;\n    var metaDoc;\n    var docCount;\n    var blobSupport;\n    var instanceId;\n\n    function completeSetup() {\n      if (typeof blobSupport === 'undefined' || !storedMetaDoc) {\n        return;\n      }\n      api._meta = {\n        name: dbName,\n        instanceId,\n        blobSupport\n      };\n\n      cachedDBs.set(dbName, {\n        idb,\n        global: api._meta\n      });\n      callback(null, api);\n    }\n\n    function storeMetaDocIfReady() {\n      if (typeof docCount === 'undefined' || typeof metaDoc === 'undefined') {\n        return;\n      }\n      var instanceKey = dbName + '_id';\n      if (instanceKey in metaDoc) {\n        instanceId = metaDoc[instanceKey];\n      } else {\n        metaDoc[instanceKey] = instanceId = uuid();\n      }\n      metaDoc.docCount = docCount;\n      txn.objectStore(META_STORE).put(metaDoc);\n    }\n\n    //\n    // fetch or generate the instanceId\n    //\n    txn.objectStore(META_STORE).get(META_STORE).onsuccess = function (e) {\n      metaDoc = e.target.result || { id: META_STORE };\n      storeMetaDocIfReady();\n    };\n\n    //\n    // countDocs\n    //\n    countDocs(txn, function (count) {\n      docCount = count;\n      storeMetaDocIfReady();\n    });\n\n    //\n    // check blob support\n    //\n    if (!blobSupportPromise) {\n      // make sure blob support is only checked once\n      blobSupportPromise = checkBlobSupport(txn, DETECT_BLOB_SUPPORT_STORE, 'key');\n    }\n\n    blobSupportPromise.then(function (val) {\n      blobSupport = val;\n      completeSetup();\n    });\n\n    // only when the metadata put transaction has completed,\n    // consider the setup done\n    txn.oncomplete = function () {\n      storedMetaDoc = true;\n      completeSetup();\n    };\n    txn.onabort = idbError(callback);\n  };\n\n  req.onerror = function (e) {\n    var msg = e.target.error && e.target.error.message;\n\n    if (!msg) {\n      msg = 'Failed to open indexedDB, are you in private browsing mode?';\n    } else if (msg.indexOf(\"stored database is a higher version\") !== -1) {\n      msg = new Error('This DB was created with the newer \"indexeddb\" adapter, but you are trying to open it with the older \"idb\" adapter');\n    }\n\n    guardedConsole('error', msg);\n    callback(createError(IDB_ERROR, msg));\n  };\n}\n\nIdbPouch.valid = function () {\n  // Following #7085 buggy idb versions (typically Safari < 10.1) are\n  // considered valid.\n\n  // On Firefox SecurityError is thrown while referencing indexedDB if cookies\n  // are not allowed. `typeof indexedDB` also triggers the error.\n  try {\n    // some outdated implementations of IDB that appear on Samsung\n    // and HTC Android devices <4.4 are missing IDBKeyRange\n    return typeof indexedDB !== 'undefined' && typeof IDBKeyRange !== 'undefined';\n  } catch (e) {\n    return false;\n  }\n};\n\nfunction IDBPouch (PouchDB) {\n  PouchDB.adapter('idb', IdbPouch, true);\n}\n\n// dead simple promise pool, inspired by https://github.com/timdp/es6-promise-pool\n// but much smaller in code size. limits the number of concurrent promises that are executed\n\n\nfunction pool(promiseFactories, limit) {\n  return new Promise(function (resolve, reject) {\n    var running = 0;\n    var current = 0;\n    var done = 0;\n    var len = promiseFactories.length;\n    var err;\n\n    function runNext() {\n      running++;\n      promiseFactories[current++]().then(onSuccess, onError);\n    }\n\n    function doNext() {\n      if (++done === len) {\n        /* istanbul ignore if */\n        if (err) {\n          reject(err);\n        } else {\n          resolve();\n        }\n      } else {\n        runNextBatch();\n      }\n    }\n\n    function onSuccess() {\n      running--;\n      doNext();\n    }\n\n    /* istanbul ignore next */\n    function onError(thisErr) {\n      running--;\n      err = err || thisErr;\n      doNext();\n    }\n\n    function runNextBatch() {\n      while (running < limit && current < len) {\n        runNext();\n      }\n    }\n\n    runNextBatch();\n  });\n}\n\nconst CHANGES_BATCH_SIZE = 25;\nconst MAX_SIMULTANEOUS_REVS = 50;\nconst CHANGES_TIMEOUT_BUFFER = 5000;\nconst DEFAULT_HEARTBEAT = 10000;\n\nconst supportsBulkGetMap = {};\n\nfunction readAttachmentsAsBlobOrBuffer(row) {\n  const doc = row.doc || row.ok;\n  const atts = doc && doc._attachments;\n  if (!atts) {\n    return;\n  }\n  Object.keys(atts).forEach(function (filename) {\n    const att = atts[filename];\n    att.data = b64ToBluffer(att.data, att.content_type);\n  });\n}\n\nfunction encodeDocId(id) {\n  if (/^_design/.test(id)) {\n    return '_design/' + encodeURIComponent(id.slice(8));\n  }\n  if (id.startsWith('_local/')) {\n    return '_local/' + encodeURIComponent(id.slice(7));\n  }\n  return encodeURIComponent(id);\n}\n\nfunction preprocessAttachments$1(doc) {\n  if (!doc._attachments || !Object.keys(doc._attachments)) {\n    return Promise.resolve();\n  }\n\n  return Promise.all(Object.keys(doc._attachments).map(function (key) {\n    const attachment = doc._attachments[key];\n    if (attachment.data && typeof attachment.data !== 'string') {\n      return new Promise(function (resolve) {\n        blobToBase64(attachment.data, resolve);\n      }).then(function (b64) {\n        attachment.data = b64;\n      });\n    }\n  }));\n}\n\nfunction hasUrlPrefix(opts) {\n  if (!opts.prefix) {\n    return false;\n  }\n  const protocol = parseUri(opts.prefix).protocol;\n  return protocol === 'http' || protocol === 'https';\n}\n\n// Get all the information you possibly can about the URI given by name and\n// return it as a suitable object.\nfunction getHost(name, opts) {\n  // encode db name if opts.prefix is a url (#5574)\n  if (hasUrlPrefix(opts)) {\n    const dbName = opts.name.substr(opts.prefix.length);\n    // Ensure prefix has a trailing slash\n    const prefix = opts.prefix.replace(/\\/?$/, '/');\n    name = prefix + encodeURIComponent(dbName);\n  }\n\n  const uri = parseUri(name);\n  if (uri.user || uri.password) {\n    uri.auth = {username: uri.user, password: uri.password};\n  }\n\n  // Split the path part of the URI into parts using '/' as the delimiter\n  // after removing any leading '/' and any trailing '/'\n  const parts = uri.path.replace(/(^\\/|\\/$)/g, '').split('/');\n\n  uri.db = parts.pop();\n  // Prevent double encoding of URI component\n  if (uri.db.indexOf('%') === -1) {\n    uri.db = encodeURIComponent(uri.db);\n  }\n\n  uri.path = parts.join('/');\n\n  return uri;\n}\n\n// Generate a URL with the host data given by opts and the given path\nfunction genDBUrl(opts, path) {\n  return genUrl(opts, opts.db + '/' + path);\n}\n\n// Generate a URL with the host data given by opts and the given path\nfunction genUrl(opts, path) {\n  // If the host already has a path, then we need to have a path delimiter\n  // Otherwise, the path delimiter is the empty string\n  const pathDel = !opts.path ? '' : '/';\n\n  // If the host already has a path, then we need to have a path delimiter\n  // Otherwise, the path delimiter is the empty string\n  return opts.protocol + '://' + opts.host +\n         (opts.port ? (':' + opts.port) : '') +\n         '/' + opts.path + pathDel + path;\n}\n\nfunction paramsToStr(params) {\n  const paramKeys = Object.keys(params);\n  if (paramKeys.length === 0) {\n    return '';\n  }\n\n  return '?' + paramKeys.map(key => key + '=' + encodeURIComponent(params[key])).join('&');\n}\n\nfunction shouldCacheBust(opts) {\n  const ua = (typeof navigator !== 'undefined' && navigator.userAgent) ?\n      navigator.userAgent.toLowerCase() : '';\n  const isIE = ua.indexOf('msie') !== -1;\n  const isTrident = ua.indexOf('trident') !== -1;\n  const isEdge = ua.indexOf('edge') !== -1;\n  const isGET = !('method' in opts) || opts.method === 'GET';\n  return (isIE || isTrident || isEdge) && isGET;\n}\n\n// Implements the PouchDB API for dealing with CouchDB instances over HTTP\nfunction HttpPouch(opts, callback) {\n\n  // The functions that will be publicly available for HttpPouch\n  const api = this;\n\n  const host = getHost(opts.name, opts);\n  const dbUrl = genDBUrl(host, '');\n\n  opts = clone(opts);\n\n  const ourFetch = async function (url, options) {\n\n    options = options || {};\n    options.headers = options.headers || new h();\n\n    options.credentials = 'include';\n\n    if (opts.auth || host.auth) {\n      const nAuth = opts.auth || host.auth;\n      const str = nAuth.username + ':' + nAuth.password;\n      const token = thisBtoa(unescape(encodeURIComponent(str)));\n      options.headers.set('Authorization', 'Basic ' + token);\n    }\n\n    const headers = opts.headers || {};\n    Object.keys(headers).forEach(function (key) {\n      options.headers.append(key, headers[key]);\n    });\n\n    /* istanbul ignore if */\n    if (shouldCacheBust(options)) {\n      url += (url.indexOf('?') === -1 ? '?' : '&') + '_nonce=' + Date.now();\n    }\n\n    const fetchFun = opts.fetch || f$1;\n    return await fetchFun(url, options);\n  };\n\n  function adapterFun$$1(name, fun) {\n    return adapterFun(name, function (...args) {\n      setup().then(function () {\n        return fun.apply(this, args);\n      }).catch(function (e) {\n        const callback = args.pop();\n        callback(e);\n      });\n    }).bind(api);\n  }\n\n  async function fetchJSON(url, options) {\n\n    const result = {};\n\n    options = options || {};\n    options.headers = options.headers || new h();\n\n    if (!options.headers.get('Content-Type')) {\n      options.headers.set('Content-Type', 'application/json');\n    }\n    if (!options.headers.get('Accept')) {\n      options.headers.set('Accept', 'application/json');\n    }\n\n    const response = await ourFetch(url, options);\n    result.ok = response.ok;\n    result.status = response.status;\n    const json = await response.json();\n\n    result.data = json;\n    if (!result.ok) {\n      result.data.status = result.status;\n      const err = generateErrorFromResponse(result.data);\n      throw err;\n    }\n\n    if (Array.isArray(result.data)) {\n      result.data = result.data.map(function (v) {\n        if (v.error || v.missing) {\n          return generateErrorFromResponse(v);\n        } else {\n          return v;\n        }\n      });\n    }\n\n    return result;\n  }\n\n  let setupPromise;\n\n  async function setup() {\n    if (opts.skip_setup) {\n      return Promise.resolve();\n    }\n\n    // If there is a setup in process or previous successful setup\n    // done then we will use that\n    // If previous setups have been rejected we will try again\n    if (setupPromise) {\n      return setupPromise;\n    }\n\n    setupPromise = fetchJSON(dbUrl).catch(function (err) {\n      if (err && err.status && err.status === 404) {\n        // Doesnt exist, create it\n        explainError(404, 'PouchDB is just detecting if the remote exists.');\n        return fetchJSON(dbUrl, {method: 'PUT'});\n      } else {\n        return Promise.reject(err);\n      }\n    }).catch(function (err) {\n      // If we try to create a database that already exists, skipped in\n      // istanbul since its catching a race condition.\n      /* istanbul ignore if */\n      if (err && err.status && err.status === 412) {\n        return true;\n      }\n      return Promise.reject(err);\n    });\n\n    setupPromise.catch(function () {\n      setupPromise = null;\n    });\n\n    return setupPromise;\n  }\n\n  nextTick(function () {\n    callback(null, api);\n  });\n\n  api._remote = true;\n\n  /* istanbul ignore next */\n  api.type = function () {\n    return 'http';\n  };\n\n  api.id = adapterFun$$1('id', async function (callback) {\n    let result;\n    try {\n      const response = await ourFetch(genUrl(host, ''));\n      result = await response.json();\n    } catch (err) {\n      result = {};\n    }\n\n    // Bad response or missing `uuid` should not prevent ID generation.\n    const uuid$$1 = (result && result.uuid) ? (result.uuid + host.db) : genDBUrl(host, '');\n    callback(null, uuid$$1);\n  });\n\n  // Sends a POST request to the host calling the couchdb _compact function\n  //    version: The version of CouchDB it is running\n  api.compact = adapterFun$$1('compact', async function (opts, callback) {\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n    opts = clone(opts);\n\n    await fetchJSON(genDBUrl(host, '_compact'), {method: 'POST'});\n\n    function ping() {\n      api.info(function (err, res) {\n        // CouchDB may send a \"compact_running:true\" if it's\n        // already compacting. PouchDB Server doesn't.\n        /* istanbul ignore else */\n        if (res && !res.compact_running) {\n          callback(null, {ok: true});\n        } else {\n          setTimeout(ping, opts.interval || 200);\n        }\n      });\n    }\n    // Ping the http if it's finished compaction\n    ping();\n  });\n\n  api.bulkGet = adapterFun('bulkGet', function (opts, callback) {\n    const self = this;\n\n    async function doBulkGet(cb) {\n      const params = {};\n      if (opts.revs) {\n        params.revs = true;\n      }\n      if (opts.attachments) {\n        /* istanbul ignore next */\n        params.attachments = true;\n      }\n      if (opts.latest) {\n        params.latest = true;\n      }\n      try {\n        const result = await fetchJSON(genDBUrl(host, '_bulk_get' + paramsToStr(params)), {\n          method: 'POST',\n          body: JSON.stringify({ docs: opts.docs})\n        });\n\n        if (opts.attachments && opts.binary) {\n          result.data.results.forEach(function (res) {\n            res.docs.forEach(readAttachmentsAsBlobOrBuffer);\n          });\n        }\n        cb(null, result.data);\n      } catch (error) {\n        cb(error);\n      }\n    }\n\n    /* istanbul ignore next */\n    function doBulkGetShim() {\n      // avoid \"url too long error\" by splitting up into multiple requests\n      const batchSize = MAX_SIMULTANEOUS_REVS;\n      const numBatches = Math.ceil(opts.docs.length / batchSize);\n      let numDone = 0;\n      const results = new Array(numBatches);\n\n      function onResult(batchNum) {\n        return function (err, res) {\n          // err is impossible because shim returns a list of errs in that case\n          results[batchNum] = res.results;\n          if (++numDone === numBatches) {\n            callback(null, {results: results.flat()});\n          }\n        };\n      }\n\n      for (let i = 0; i < numBatches; i++) {\n        const subOpts = pick(opts, ['revs', 'attachments', 'binary', 'latest']);\n        subOpts.docs = opts.docs.slice(i * batchSize,\n          Math.min(opts.docs.length, (i + 1) * batchSize));\n        bulkGet(self, subOpts, onResult(i));\n      }\n    }\n\n    // mark the whole database as either supporting or not supporting _bulk_get\n    const dbUrl = genUrl(host, '');\n    const supportsBulkGet = supportsBulkGetMap[dbUrl];\n\n    /* istanbul ignore next */\n    if (typeof supportsBulkGet !== 'boolean') {\n      // check if this database supports _bulk_get\n      doBulkGet(function (err, res) {\n        if (err) {\n          supportsBulkGetMap[dbUrl] = false;\n          explainError(\n            err.status,\n            'PouchDB is just detecting if the remote ' +\n            'supports the _bulk_get API.'\n          );\n          doBulkGetShim();\n        } else {\n          supportsBulkGetMap[dbUrl] = true;\n          callback(null, res);\n        }\n      });\n    } else if (supportsBulkGet) {\n      doBulkGet(callback);\n    } else {\n      doBulkGetShim();\n    }\n  });\n\n  // Calls GET on the host, which gets back a JSON string containing\n  //    couchdb: A welcome string\n  //    version: The version of CouchDB it is running\n  api._info = async function (callback) {\n    try {\n      await setup();\n      const response = await ourFetch(genDBUrl(host, ''));\n      const info = await response.json();\n      info.host = genDBUrl(host, '');\n      callback(null, info);\n    } catch (err) {\n      callback(err);\n    }\n  };\n\n  api.fetch = async function (path, options) {\n    await setup();\n    const url = path.substring(0, 1) === '/' ?\n    genUrl(host, path.substring(1)) :\n    genDBUrl(host, path);\n    return ourFetch(url, options);\n  };\n\n  // Get the document with the given id from the database given by host.\n  // The id could be solely the _id in the database, or it may be a\n  // _design/ID or _local/ID path\n  api.get = adapterFun$$1('get', async function (id, opts, callback) {\n    // If no options were given, set the callback to the second parameter\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n    opts = clone(opts);\n\n    // List of parameters to add to the GET request\n    const params = {};\n\n    if (opts.revs) {\n      params.revs = true;\n    }\n\n    if (opts.revs_info) {\n      params.revs_info = true;\n    }\n\n    if (opts.latest) {\n      params.latest = true;\n    }\n\n    if (opts.open_revs) {\n      if (opts.open_revs !== \"all\") {\n        opts.open_revs = JSON.stringify(opts.open_revs);\n      }\n      params.open_revs = opts.open_revs;\n    }\n\n    if (opts.rev) {\n      params.rev = opts.rev;\n    }\n\n    if (opts.conflicts) {\n      params.conflicts = opts.conflicts;\n    }\n\n    /* istanbul ignore if */\n    if (opts.update_seq) {\n      params.update_seq = opts.update_seq;\n    }\n\n    id = encodeDocId(id);\n\n    function fetchAttachments(doc) {\n      const atts = doc._attachments;\n      const filenames = atts && Object.keys(atts);\n      if (!atts || !filenames.length) {\n        return;\n      }\n      // we fetch these manually in separate XHRs, because\n      // Sync Gateway would normally send it back as multipart/mixed,\n      // which we cannot parse. Also, this is more efficient than\n      // receiving attachments as base64-encoded strings.\n      async function fetchData(filename) {\n        const att = atts[filename];\n        const path = encodeDocId(doc._id) + '/' + encodeAttachmentId(filename) +\n            '?rev=' + doc._rev;\n\n        const response = await ourFetch(genDBUrl(host, path));\n\n        let blob;\n        if ('buffer' in response) {\n          blob = await response.buffer();\n        } else {\n          /* istanbul ignore next */\n          blob = await response.blob();\n        }\n\n        let data;\n        if (opts.binary) {\n          const typeFieldDescriptor = Object.getOwnPropertyDescriptor(blob.__proto__, 'type');\n          if (!typeFieldDescriptor || typeFieldDescriptor.set) {\n            blob.type = att.content_type;\n          }\n          data = blob;\n        } else {\n          data = await new Promise(function (resolve) {\n            blobToBase64(blob, resolve);\n          });\n        }\n\n        delete att.stub;\n        delete att.length;\n        att.data = data;\n      }\n\n      const promiseFactories = filenames.map(function (filename) {\n        return function () {\n          return fetchData(filename);\n        };\n      });\n\n      // This limits the number of parallel xhr requests to 5 any time\n      // to avoid issues with maximum browser request limits\n      return pool(promiseFactories, 5);\n    }\n\n    function fetchAllAttachments(docOrDocs) {\n      if (Array.isArray(docOrDocs)) {\n        return Promise.all(docOrDocs.map(function (doc) {\n          if (doc.ok) {\n            return fetchAttachments(doc.ok);\n          }\n        }));\n      }\n      return fetchAttachments(docOrDocs);\n    }\n\n    const url = genDBUrl(host, id + paramsToStr(params));\n    try {\n      const res = await fetchJSON(url);\n      if (opts.attachments) {\n        await fetchAllAttachments(res.data);\n      }\n      callback(null, res.data);\n    } catch (error) {\n      error.docId = id;\n      callback(error);\n    }\n  });\n\n\n  // Delete the document given by doc from the database given by host.\n  api.remove = adapterFun$$1('remove', async function (docOrId, optsOrRev, opts, cb) {\n    let doc;\n    if (typeof optsOrRev === 'string') {\n      // id, rev, opts, callback style\n      doc = {\n        _id: docOrId,\n        _rev: optsOrRev\n      };\n      if (typeof opts === 'function') {\n        cb = opts;\n        opts = {};\n      }\n    } else {\n      // doc, opts, callback style\n      doc = docOrId;\n      if (typeof optsOrRev === 'function') {\n        cb = optsOrRev;\n        opts = {};\n      } else {\n        cb = opts;\n        opts = optsOrRev;\n      }\n    }\n\n    const rev$$1 = (doc._rev || opts.rev);\n    const url = genDBUrl(host, encodeDocId(doc._id)) + '?rev=' + rev$$1;\n\n    try {\n      const result = await fetchJSON(url, {method: 'DELETE'});\n      cb(null, result.data);\n    } catch (error) {\n      cb(error);\n    }\n  });\n\n  function encodeAttachmentId(attachmentId) {\n    return attachmentId.split(\"/\").map(encodeURIComponent).join(\"/\");\n  }\n\n  // Get the attachment\n  api.getAttachment = adapterFun$$1('getAttachment', async function (docId, attachmentId,\n                                                            opts, callback) {\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n    const params = opts.rev ? ('?rev=' + opts.rev) : '';\n    const url = genDBUrl(host, encodeDocId(docId)) + '/' +\n        encodeAttachmentId(attachmentId) + params;\n    let contentType;\n    try {\n      const response = await ourFetch(url, {method: 'GET'});\n\n      if (!response.ok) {\n        throw response;\n      }\n\n      contentType = response.headers.get('content-type');\n      let blob;\n      if (typeof process !== 'undefined' && !process.browser && typeof response.buffer === 'function') {\n        blob = await response.buffer();\n      } else {\n        /* istanbul ignore next */\n        blob = await response.blob();\n      }\n\n      // TODO: also remove\n      if (typeof process !== 'undefined' && !process.browser) {\n        const typeFieldDescriptor = Object.getOwnPropertyDescriptor(blob.__proto__, 'type');\n        if (!typeFieldDescriptor || typeFieldDescriptor.set) {\n          blob.type = contentType;\n        }\n      }\n      callback(null, blob);\n    } catch (err) {\n      callback(err);\n    }\n  });\n\n  // Remove the attachment given by the id and rev\n  api.removeAttachment =  adapterFun$$1('removeAttachment', async function (\n    docId,\n    attachmentId,\n    rev$$1,\n    callback,\n  ) {\n    const url = genDBUrl(host, encodeDocId(docId) + '/' + encodeAttachmentId(attachmentId)) + '?rev=' + rev$$1;\n\n    try {\n      const result = await fetchJSON(url, {method: 'DELETE'});\n      callback(null, result.data);\n    } catch (error) {\n      callback(error);\n    }\n  });\n\n  // Add the attachment given by blob and its contentType property\n  // to the document with the given id, the revision given by rev, and\n  // add it to the database given by host.\n  api.putAttachment = adapterFun$$1('putAttachment', async function (\n    docId,\n    attachmentId,\n    rev$$1,\n    blob,\n    type,\n    callback,\n  ) {\n    if (typeof type === 'function') {\n      callback = type;\n      type = blob;\n      blob = rev$$1;\n      rev$$1 = null;\n    }\n    const id = encodeDocId(docId) + '/' + encodeAttachmentId(attachmentId);\n    let url = genDBUrl(host, id);\n    if (rev$$1) {\n      url += '?rev=' + rev$$1;\n    }\n\n    if (typeof blob === 'string') {\n      // input is assumed to be a base64 string\n      let binary;\n      try {\n        binary = thisAtob(blob);\n      } catch (err) {\n        return callback(createError(BAD_ARG,\n                        'Attachment is not a valid base64 string'));\n      }\n      blob = binary ? binStringToBluffer(binary, type) : '';\n    }\n\n    try {\n      // Add the attachment\n      const result = await fetchJSON(url, {\n        headers: new h({'Content-Type': type}),\n        method: 'PUT',\n        body: blob\n      });\n      callback(null, result.data);\n    } catch (error) {\n      callback(error);\n    }\n  });\n\n  // Update/create multiple documents given by req in the database\n  // given by host.\n  api._bulkDocs = async function (req, opts, callback) {\n    // If new_edits=false then it prevents the database from creating\n    // new revision numbers for the documents. Instead it just uses\n    // the old ones. This is used in database replication.\n    req.new_edits = opts.new_edits;\n\n    try {\n      await setup();\n      await Promise.all(req.docs.map(preprocessAttachments$1));\n\n      // Update/create the documents\n      const result = await fetchJSON(genDBUrl(host, '_bulk_docs'), {\n        method: 'POST',\n        body: JSON.stringify(req)\n      });\n      callback(null, result.data);\n    } catch (error) {\n      callback(error);\n    }\n  };\n\n  // Update/create document\n  api._put = async function (doc, opts, callback) {\n    try {\n      await setup();\n      await preprocessAttachments$1(doc);\n\n      const result = await fetchJSON(genDBUrl(host, encodeDocId(doc._id)), {\n        method: 'PUT',\n        body: JSON.stringify(doc)\n      });\n      callback(null, result.data);\n    } catch (error) {\n      error.docId = doc && doc._id;\n      callback(error);\n    }\n  };\n\n\n  // Get a listing of the documents in the database given\n  // by host and ordered by increasing id.\n  api.allDocs = adapterFun$$1('allDocs', async function (opts, callback) {\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n    opts = clone(opts);\n\n    // List of parameters to add to the GET request\n    const params = {};\n    let body;\n    let method = 'GET';\n\n    if (opts.conflicts) {\n      params.conflicts = true;\n    }\n\n    /* istanbul ignore if */\n    if (opts.update_seq) {\n      params.update_seq = true;\n    }\n\n    if (opts.descending) {\n      params.descending = true;\n    }\n\n    if (opts.include_docs) {\n      params.include_docs = true;\n    }\n\n    // added in CouchDB 1.6.0\n    if (opts.attachments) {\n      params.attachments = true;\n    }\n\n    if (opts.key) {\n      params.key = JSON.stringify(opts.key);\n    }\n\n    if (opts.start_key) {\n      opts.startkey = opts.start_key;\n    }\n\n    if (opts.startkey) {\n      params.startkey = JSON.stringify(opts.startkey);\n    }\n\n    if (opts.end_key) {\n      opts.endkey = opts.end_key;\n    }\n\n    if (opts.endkey) {\n      params.endkey = JSON.stringify(opts.endkey);\n    }\n\n    if (typeof opts.inclusive_end !== 'undefined') {\n      params.inclusive_end = !!opts.inclusive_end;\n    }\n\n    if (typeof opts.limit !== 'undefined') {\n      params.limit = opts.limit;\n    }\n\n    if (typeof opts.skip !== 'undefined') {\n      params.skip = opts.skip;\n    }\n\n    const paramStr = paramsToStr(params);\n\n    if (typeof opts.keys !== 'undefined') {\n      method = 'POST';\n      body = {keys: opts.keys};\n    }\n\n    try {\n      const result = await fetchJSON(genDBUrl(host, '_all_docs' + paramStr), {\n        method,\n        body: JSON.stringify(body)\n      });\n      if (opts.include_docs && opts.attachments && opts.binary) {\n        result.data.rows.forEach(readAttachmentsAsBlobOrBuffer);\n      }\n      callback(null, result.data);\n    } catch (error) {\n      callback(error);\n    }\n  });\n\n  // Get a list of changes made to documents in the database given by host.\n  // TODO According to the README, there should be two other methods here,\n  // api.changes.addListener and api.changes.removeListener.\n  api._changes = function (opts) {\n\n    // We internally page the results of a changes request, this means\n    // if there is a large set of changes to be returned we can start\n    // processing them quicker instead of waiting on the entire\n    // set of changes to return and attempting to process them at once\n    const batchSize = 'batch_size' in opts ? opts.batch_size : CHANGES_BATCH_SIZE;\n\n    opts = clone(opts);\n\n    if (opts.continuous && !('heartbeat' in opts)) {\n      opts.heartbeat = DEFAULT_HEARTBEAT;\n    }\n\n    let requestTimeout = ('timeout' in opts) ? opts.timeout : 30 * 1000;\n\n    // ensure CHANGES_TIMEOUT_BUFFER applies\n    if ('timeout' in opts && opts.timeout &&\n      (requestTimeout - opts.timeout) < CHANGES_TIMEOUT_BUFFER) {\n        requestTimeout = opts.timeout + CHANGES_TIMEOUT_BUFFER;\n    }\n\n    /* istanbul ignore if */\n    if ('heartbeat' in opts && opts.heartbeat &&\n       (requestTimeout - opts.heartbeat) < CHANGES_TIMEOUT_BUFFER) {\n        requestTimeout = opts.heartbeat + CHANGES_TIMEOUT_BUFFER;\n    }\n\n    const params = {};\n    if ('timeout' in opts && opts.timeout) {\n      params.timeout = opts.timeout;\n    }\n\n    const limit = (typeof opts.limit !== 'undefined') ? opts.limit : false;\n    let leftToFetch = limit;\n\n    if (opts.style) {\n      params.style = opts.style;\n    }\n\n    if (opts.include_docs || opts.filter && typeof opts.filter === 'function') {\n      params.include_docs = true;\n    }\n\n    if (opts.attachments) {\n      params.attachments = true;\n    }\n\n    if (opts.continuous) {\n      params.feed = 'longpoll';\n    }\n\n    if (opts.seq_interval) {\n      params.seq_interval = opts.seq_interval;\n    }\n\n    if (opts.conflicts) {\n      params.conflicts = true;\n    }\n\n    if (opts.descending) {\n      params.descending = true;\n    }\n\n    /* istanbul ignore if */\n    if (opts.update_seq) {\n      params.update_seq = true;\n    }\n\n    if ('heartbeat' in opts) {\n      // If the heartbeat value is false, it disables the default heartbeat\n      if (opts.heartbeat) {\n        params.heartbeat = opts.heartbeat;\n      }\n    }\n\n    if (opts.filter && typeof opts.filter === 'string') {\n      params.filter = opts.filter;\n    }\n\n    if (opts.view && typeof opts.view === 'string') {\n      params.filter = '_view';\n      params.view = opts.view;\n    }\n\n    // If opts.query_params exists, pass it through to the changes request.\n    // These parameters may be used by the filter on the source database.\n    if (opts.query_params && typeof opts.query_params === 'object') {\n      for (const param_name in opts.query_params) {\n        /* istanbul ignore else */\n        if (Object.prototype.hasOwnProperty.call(opts.query_params, param_name)) {\n          params[param_name] = opts.query_params[param_name];\n        }\n      }\n    }\n\n    let method = 'GET';\n    let body;\n\n    if (opts.doc_ids) {\n      // set this automagically for the user; it's annoying that couchdb\n      // requires both a \"filter\" and a \"doc_ids\" param.\n      params.filter = '_doc_ids';\n      method = 'POST';\n      body = {doc_ids: opts.doc_ids };\n    }\n    /* istanbul ignore next */\n    else if (opts.selector) {\n      // set this automagically for the user, similar to above\n      params.filter = '_selector';\n      method = 'POST';\n      body = {selector: opts.selector };\n    }\n\n    const controller = new AbortController();\n    let lastFetchedSeq;\n\n    // Get all the changes starting with the one immediately after the\n    // sequence number given by since.\n    const fetchData = async function (since, callback) {\n      if (opts.aborted) {\n        return;\n      }\n      params.since = since;\n      // \"since\" can be any kind of json object in Cloudant/CouchDB 2.x\n      /* istanbul ignore next */\n      if (typeof params.since === \"object\") {\n        params.since = JSON.stringify(params.since);\n      }\n\n      if (opts.descending) {\n        if (limit) {\n          params.limit = leftToFetch;\n        }\n      } else {\n        params.limit = (!limit || leftToFetch > batchSize) ?\n          batchSize : leftToFetch;\n      }\n\n      // Set the options for the ajax call\n      const url = genDBUrl(host, '_changes' + paramsToStr(params));\n      const fetchOpts = {\n        signal: controller.signal,\n        method,\n        body: JSON.stringify(body)\n      };\n      lastFetchedSeq = since;\n\n      /* istanbul ignore if */\n      if (opts.aborted) {\n        return;\n      }\n\n      // Get the changes\n      try {\n        await setup();\n        const result = await fetchJSON(url, fetchOpts);\n        callback(null, result.data);\n      } catch (error) {\n        callback(error);\n      }\n    };\n\n    // If opts.since exists, get all the changes from the sequence\n    // number given by opts.since. Otherwise, get all the changes\n    // from the sequence number 0.\n    const results = {results: []};\n\n    const fetched = function (err, res) {\n      if (opts.aborted) {\n        return;\n      }\n      let raw_results_length = 0;\n      // If the result of the ajax call (res) contains changes (res.results)\n      if (res && res.results) {\n        raw_results_length = res.results.length;\n        results.last_seq = res.last_seq;\n        let pending = null;\n        let lastSeq = null;\n        // Attach 'pending' property if server supports it (CouchDB 2.0+)\n        /* istanbul ignore if */\n        if (typeof res.pending === 'number') {\n          pending = res.pending;\n        }\n        if (typeof results.last_seq === 'string' || typeof results.last_seq === 'number') {\n          lastSeq = results.last_seq;\n        }\n        // For each change\n        const req = {};\n        req.query = opts.query_params;\n        res.results = res.results.filter(function (c) {\n          leftToFetch--;\n          const ret = filterChange(opts)(c);\n          if (ret) {\n            if (opts.include_docs && opts.attachments && opts.binary) {\n              readAttachmentsAsBlobOrBuffer(c);\n            }\n            if (opts.return_docs) {\n              results.results.push(c);\n            }\n            opts.onChange(c, pending, lastSeq);\n          }\n          return ret;\n        });\n      } else if (err) {\n        // In case of an error, stop listening for changes and call\n        // opts.complete\n        opts.aborted = true;\n        opts.complete(err);\n        return;\n      }\n\n      // The changes feed may have timed out with no results\n      // if so reuse last update sequence\n      if (res && res.last_seq) {\n        lastFetchedSeq = res.last_seq;\n      }\n\n      const finished = (limit && leftToFetch <= 0) ||\n        (res && raw_results_length < batchSize) ||\n        (opts.descending);\n\n      if ((opts.continuous && !(limit && leftToFetch <= 0)) || !finished) {\n        // Queue a call to fetch again with the newest sequence number\n        nextTick(function () { fetchData(lastFetchedSeq, fetched); });\n      } else {\n        // We're done, call the callback\n        opts.complete(null, results);\n      }\n    };\n\n    fetchData(opts.since || 0, fetched);\n\n    // Return a method to cancel this method from processing any more\n    return {\n      cancel: function () {\n        opts.aborted = true;\n        controller.abort();\n      }\n    };\n  };\n\n  // Given a set of document/revision IDs (given by req), tets the subset of\n  // those that do NOT correspond to revisions stored in the database.\n  // See http://wiki.apache.org/couchdb/HttpPostRevsDiff\n  api.revsDiff = adapterFun$$1('revsDiff', async function (req, opts, callback) {\n    // If no options were given, set the callback to be the second parameter\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n\n    try {\n      // Get the missing document/revision IDs\n      const result = await fetchJSON(genDBUrl(host, '_revs_diff'), {\n        method: 'POST',\n        body: JSON.stringify(req)\n      });\n      callback(null, result.data);\n    } catch (error) {\n      callback(error);\n    }\n  });\n\n  api._close = function (callback) {\n    callback();\n  };\n\n  api._destroy = async function (options, callback) {\n    try {\n      const json = await fetchJSON(genDBUrl(host, ''), {method: 'DELETE'});\n      callback(null, json);\n    } catch (error) {\n      if (error.status === 404) {\n        callback(null, {ok: true});\n      } else {\n        callback(error);\n      }\n    }\n  };\n}\n\n// HttpPouch is a valid adapter.\nHttpPouch.valid = function () {\n  return true;\n};\n\nfunction HttpPouch$1 (PouchDB) {\n  PouchDB.adapter('http', HttpPouch, false);\n  PouchDB.adapter('https', HttpPouch, false);\n}\n\nclass QueryParseError extends Error {\n  constructor(message) {\n    super();\n    this.status = 400;\n    this.name = 'query_parse_error';\n    this.message = message;\n    this.error = true;\n    try {\n      Error.captureStackTrace(this, QueryParseError);\n    } catch (e) {}\n  }\n}\n\nclass NotFoundError extends Error {\n  constructor(message) {\n    super();\n    this.status = 404;\n    this.name = 'not_found';\n    this.message = message;\n    this.error = true;\n    try {\n      Error.captureStackTrace(this, NotFoundError);\n    } catch (e) {}\n  }\n}\n\nclass BuiltInError extends Error {\n  constructor(message) {\n    super();\n    this.status = 500;\n    this.name = 'invalid_value';\n    this.message = message;\n    this.error = true;\n    try {\n      Error.captureStackTrace(this, BuiltInError);\n    } catch (e) {}\n  }\n}\n\nfunction promisedCallback(promise, callback) {\n  if (callback) {\n    promise.then(function (res) {\n      nextTick(function () {\n        callback(null, res);\n      });\n    }, function (reason) {\n      nextTick(function () {\n        callback(reason);\n      });\n    });\n  }\n  return promise;\n}\n\nfunction callbackify(fun) {\n  return function (...args) {\n    var cb = args.pop();\n    var promise = fun.apply(this, args);\n    if (typeof cb === 'function') {\n      promisedCallback(promise, cb);\n    }\n    return promise;\n  };\n}\n\n// Promise finally util similar to Q.finally\nfunction fin(promise, finalPromiseFactory) {\n  return promise.then(function (res) {\n    return finalPromiseFactory().then(function () {\n      return res;\n    });\n  }, function (reason) {\n    return finalPromiseFactory().then(function () {\n      throw reason;\n    });\n  });\n}\n\nfunction sequentialize(queue, promiseFactory) {\n  return function () {\n    var args = arguments;\n    var that = this;\n    return queue.add(function () {\n      return promiseFactory.apply(that, args);\n    });\n  };\n}\n\n// uniq an array of strings, order not guaranteed\n// similar to underscore/lodash _.uniq\nfunction uniq(arr) {\n  var theSet = new Set(arr);\n  var result = new Array(theSet.size);\n  var index = -1;\n  theSet.forEach(function (value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nfunction mapToKeysArray(map) {\n  var result = new Array(map.size);\n  var index = -1;\n  map.forEach(function (value, key) {\n    result[++index] = key;\n  });\n  return result;\n}\n\nfunction createBuiltInError(name) {\n  var message = 'builtin ' + name +\n    ' function requires map values to be numbers' +\n    ' or number arrays';\n  return new BuiltInError(message);\n}\n\nfunction sum(values) {\n  var result = 0;\n  for (var i = 0, len = values.length; i < len; i++) {\n    var num = values[i];\n    if (typeof num !== 'number') {\n      if (Array.isArray(num)) {\n        // lists of numbers are also allowed, sum them separately\n        result = typeof result === 'number' ? [result] : result;\n        for (var j = 0, jLen = num.length; j < jLen; j++) {\n          var jNum = num[j];\n          if (typeof jNum !== 'number') {\n            throw createBuiltInError('_sum');\n          } else if (typeof result[j] === 'undefined') {\n            result.push(jNum);\n          } else {\n            result[j] += jNum;\n          }\n        }\n      } else { // not array/number\n        throw createBuiltInError('_sum');\n      }\n    } else if (typeof result === 'number') {\n      result += num;\n    } else { // add number to array\n      result[0] += num;\n    }\n  }\n  return result;\n}\n\nvar log = guardedConsole.bind(null, 'log');\nvar isArray = Array.isArray;\nvar toJSON = JSON.parse;\n\nfunction evalFunctionWithEval(func, emit) {\n  return scopeEval(\n    \"return (\" + func.replace(/;\\s*$/, \"\") + \");\",\n    {\n      emit,\n      sum,\n      log,\n      isArray,\n      toJSON\n    }\n  );\n}\n\n/*\n * Simple task queue to sequentialize actions. Assumes\n * callbacks will eventually fire (once).\n */\n\nclass TaskQueue$1 {\n  constructor() {\n    this.promise = Promise.resolve();\n  }\n\n  add(promiseFactory) {\n    this.promise = this.promise\n      // just recover\n      .catch(() => { })\n      .then(() => promiseFactory());\n    return this.promise;\n  }\n\n  finish() {\n    return this.promise;\n  }\n}\n\nfunction stringify(input) {\n  if (!input) {\n    return 'undefined'; // backwards compat for empty reduce\n  }\n  // for backwards compat with mapreduce, functions/strings are stringified\n  // as-is. everything else is JSON-stringified.\n  switch (typeof input) {\n    case 'function':\n      // e.g. a mapreduce map\n      return input.toString();\n    case 'string':\n      // e.g. a mapreduce built-in _reduce function\n      return input.toString();\n    default:\n      // e.g. a JSON object in the case of mango queries\n      return JSON.stringify(input);\n  }\n}\n\n/* create a string signature for a view so we can cache it and uniq it */\nfunction createViewSignature(mapFun, reduceFun) {\n  // the \"undefined\" part is for backwards compatibility\n  return stringify(mapFun) + stringify(reduceFun) + 'undefined';\n}\n\nasync function createView(sourceDB, viewName, mapFun, reduceFun, temporary, localDocName) {\n  const viewSignature = createViewSignature(mapFun, reduceFun);\n\n  let cachedViews;\n  if (!temporary) {\n    // cache this to ensure we don't try to update the same view twice\n    cachedViews = sourceDB._cachedViews = sourceDB._cachedViews || {};\n    if (cachedViews[viewSignature]) {\n      return cachedViews[viewSignature];\n    }\n  }\n\n  const promiseForView = sourceDB.info().then(async function (info) {\n    const depDbName = info.db_name + '-mrview-' +\n    (temporary ? 'temp' : stringMd5(viewSignature));\n\n    // save the view name in the source db so it can be cleaned up if necessary\n    // (e.g. when the _design doc is deleted, remove all associated view data)\n    function diffFunction(doc) {\n      doc.views = doc.views || {};\n      let fullViewName = viewName;\n      if (fullViewName.indexOf('/') === -1) {\n        fullViewName = viewName + '/' + viewName;\n      }\n      const depDbs = doc.views[fullViewName] = doc.views[fullViewName] || {};\n      /* istanbul ignore if */\n      if (depDbs[depDbName]) {\n        return; // no update necessary\n      }\n      depDbs[depDbName] = true;\n      return doc;\n    }\n    await upsert(sourceDB, '_local/' + localDocName, diffFunction);\n    const res = await sourceDB.registerDependentDatabase(depDbName);\n    const db = res.db;\n    db.auto_compaction = true;\n    const view = {\n      name: depDbName,\n      db,\n      sourceDB,\n      adapter: sourceDB.adapter,\n      mapFun,\n      reduceFun\n    };\n\n    let lastSeqDoc;\n    try {\n      lastSeqDoc = await view.db.get('_local/lastSeq');\n    } catch (err) {\n        /* istanbul ignore if */\n      if (err.status !== 404) {\n        throw err;\n      }\n    }\n\n    view.seq = lastSeqDoc ? lastSeqDoc.seq : 0;\n    if (cachedViews) {\n      view.db.once('destroyed', function () {\n        delete cachedViews[viewSignature];\n      });\n    }\n    return view;\n  });\n\n  if (cachedViews) {\n    cachedViews[viewSignature] = promiseForView;\n  }\n  return promiseForView;\n}\n\nconst persistentQueues = {};\nconst tempViewQueue = new TaskQueue$1();\nconst CHANGES_BATCH_SIZE$1 = 50;\n\nfunction parseViewName(name) {\n  // can be either 'ddocname/viewname' or just 'viewname'\n  // (where the ddoc name is the same)\n  return name.indexOf('/') === -1 ? [name, name] : name.split('/');\n}\n\nfunction isGenOne(changes) {\n  // only return true if the current change is 1-\n  // and there are no other leafs\n  return changes.length === 1 && /^1-/.test(changes[0].rev);\n}\n\nfunction emitError(db, e, data) {\n  try {\n    db.emit('error', e);\n  } catch (err) {\n    guardedConsole('error',\n      'The user\\'s map/reduce function threw an uncaught error.\\n' +\n      'You can debug this error by doing:\\n' +\n      'myDatabase.on(\\'error\\', function (err) { debugger; });\\n' +\n      'Please double-check your map/reduce function.');\n    guardedConsole('error', e, data);\n  }\n}\n\n/**\n * Returns an \"abstract\" mapreduce object of the form:\n *\n *   {\n *     query: queryFun,\n *     viewCleanup: viewCleanupFun\n *   }\n *\n * Arguments are:\n *\n * localDoc: string\n *   This is for the local doc that gets saved in order to track the\n *   \"dependent\" DBs and clean them up for viewCleanup. It should be\n *   unique, so that indexer plugins don't collide with each other.\n * mapper: function (mapFunDef, emit)\n *   Returns a map function based on the mapFunDef, which in the case of\n *   normal map/reduce is just the de-stringified function, but may be\n *   something else, such as an object in the case of pouchdb-find.\n * reducer: function (reduceFunDef)\n *   Ditto, but for reducing. Modules don't have to support reducing\n *   (e.g. pouchdb-find).\n * ddocValidator: function (ddoc, viewName)\n *   Throws an error if the ddoc or viewName is not valid.\n *   This could be a way to communicate to the user that the configuration for the\n *   indexer is invalid.\n */\nfunction createAbstractMapReduce(localDocName, mapper, reducer, ddocValidator) {\n\n  function tryMap(db, fun, doc) {\n    // emit an event if there was an error thrown by a map function.\n    // putting try/catches in a single function also avoids deoptimizations.\n    try {\n      fun(doc);\n    } catch (e) {\n      emitError(db, e, {fun, doc});\n    }\n  }\n\n  function tryReduce(db, fun, keys, values, rereduce) {\n    // same as above, but returning the result or an error. there are two separate\n    // functions to avoid extra memory allocations since the tryCode() case is used\n    // for custom map functions (common) vs this function, which is only used for\n    // custom reduce functions (rare)\n    try {\n      return {output : fun(keys, values, rereduce)};\n    } catch (e) {\n      emitError(db, e, {fun, keys, values, rereduce});\n      return {error: e};\n    }\n  }\n\n  function sortByKeyThenValue(x, y) {\n    const keyCompare = collate(x.key, y.key);\n    return keyCompare !== 0 ? keyCompare : collate(x.value, y.value);\n  }\n\n  function sliceResults(results, limit, skip) {\n    skip = skip || 0;\n    if (typeof limit === 'number') {\n      return results.slice(skip, limit + skip);\n    } else if (skip > 0) {\n      return results.slice(skip);\n    }\n    return results;\n  }\n\n  function rowToDocId(row) {\n    const val = row.value;\n    // Users can explicitly specify a joined doc _id, or it\n    // defaults to the doc _id that emitted the key/value.\n    const docId = (val && typeof val === 'object' && val._id) || row.id;\n    return docId;\n  }\n\n  function readAttachmentsAsBlobOrBuffer(res) {\n    for (const row of res.rows) {\n      const atts = row.doc && row.doc._attachments;\n      if (!atts) {\n        continue;\n      }\n      for (const filename of Object.keys(atts)) {\n        const att = atts[filename];\n        atts[filename].data = b64ToBluffer(att.data, att.content_type);\n      }\n    }\n  }\n\n  function postprocessAttachments(opts) {\n    return function (res) {\n      if (opts.include_docs && opts.attachments && opts.binary) {\n        readAttachmentsAsBlobOrBuffer(res);\n      }\n      return res;\n    };\n  }\n\n  function addHttpParam(paramName, opts, params, asJson) {\n    // add an http param from opts to params, optionally json-encoded\n    let val = opts[paramName];\n    if (typeof val !== 'undefined') {\n      if (asJson) {\n        val = encodeURIComponent(JSON.stringify(val));\n      }\n      params.push(paramName + '=' + val);\n    }\n  }\n\n  function coerceInteger(integerCandidate) {\n    if (typeof integerCandidate !== 'undefined') {\n      const asNumber = Number(integerCandidate);\n      // prevents e.g. '1foo' or '1.1' being coerced to 1\n      if (!isNaN(asNumber) && asNumber === parseInt(integerCandidate, 10)) {\n        return asNumber;\n      } else {\n        return integerCandidate;\n      }\n    }\n  }\n\n  function coerceOptions(opts) {\n    opts.group_level = coerceInteger(opts.group_level);\n    opts.limit = coerceInteger(opts.limit);\n    opts.skip = coerceInteger(opts.skip);\n    return opts;\n  }\n\n  function checkPositiveInteger(number) {\n    if (number) {\n      if (typeof number !== 'number') {\n        return  new QueryParseError(`Invalid value for integer: \"${number}\"`);\n      }\n      if (number < 0) {\n        return new QueryParseError(`Invalid value for positive integer: \"${number}\"`);\n      }\n    }\n  }\n\n  function checkQueryParseError(options, fun) {\n    const startkeyName = options.descending ? 'endkey' : 'startkey';\n    const endkeyName = options.descending ? 'startkey' : 'endkey';\n\n    if (typeof options[startkeyName] !== 'undefined' &&\n      typeof options[endkeyName] !== 'undefined' &&\n      collate(options[startkeyName], options[endkeyName]) > 0) {\n      throw new QueryParseError('No rows can match your key range, ' +\n        'reverse your start_key and end_key or set {descending : true}');\n    } else if (fun.reduce && options.reduce !== false) {\n      if (options.include_docs) {\n        throw new QueryParseError('{include_docs:true} is invalid for reduce');\n      } else if (options.keys && options.keys.length > 1 &&\n        !options.group && !options.group_level) {\n        throw new QueryParseError('Multi-key fetches for reduce views must use ' +\n          '{group: true}');\n      }\n    }\n    for (const optionName of ['group_level', 'limit', 'skip']) {\n      const error = checkPositiveInteger(options[optionName]);\n      if (error) {\n        throw error;\n      }\n    }\n  }\n\n  async function httpQuery(db, fun, opts) {\n    // List of parameters to add to the PUT request\n    let params = [];\n    let body;\n    let method = 'GET';\n    let ok;\n\n    // If opts.reduce exists and is defined, then add it to the list\n    // of parameters.\n    // If reduce=false then the results are that of only the map function\n    // not the final result of map and reduce.\n    addHttpParam('reduce', opts, params);\n    addHttpParam('include_docs', opts, params);\n    addHttpParam('attachments', opts, params);\n    addHttpParam('limit', opts, params);\n    addHttpParam('descending', opts, params);\n    addHttpParam('group', opts, params);\n    addHttpParam('group_level', opts, params);\n    addHttpParam('skip', opts, params);\n    addHttpParam('stale', opts, params);\n    addHttpParam('conflicts', opts, params);\n    addHttpParam('startkey', opts, params, true);\n    addHttpParam('start_key', opts, params, true);\n    addHttpParam('endkey', opts, params, true);\n    addHttpParam('end_key', opts, params, true);\n    addHttpParam('inclusive_end', opts, params);\n    addHttpParam('key', opts, params, true);\n    addHttpParam('update_seq', opts, params);\n\n    // Format the list of parameters into a valid URI query string\n    params = params.join('&');\n    params = params === '' ? '' : '?' + params;\n\n    // If keys are supplied, issue a POST to circumvent GET query string limits\n    // see http://wiki.apache.org/couchdb/HTTP_view_API#Querying_Options\n    if (typeof opts.keys !== 'undefined') {\n      const MAX_URL_LENGTH = 2000;\n      // according to http://stackoverflow.com/a/417184/680742,\n      // the de facto URL length limit is 2000 characters\n\n      const keysAsString = `keys=${encodeURIComponent(JSON.stringify(opts.keys))}`;\n      if (keysAsString.length + params.length + 1 <= MAX_URL_LENGTH) {\n        // If the keys are short enough, do a GET. we do this to work around\n        // Safari not understanding 304s on POSTs (see pouchdb/pouchdb#1239)\n        params += (params[0] === '?' ? '&' : '?') + keysAsString;\n      } else {\n        method = 'POST';\n        if (typeof fun === 'string') {\n          body = {keys: opts.keys};\n        } else { // fun is {map : mapfun}, so append to this\n          fun.keys = opts.keys;\n        }\n      }\n    }\n\n    // We are referencing a query defined in the design doc\n    if (typeof fun === 'string') {\n      const parts = parseViewName(fun);\n\n      const response = await db.fetch('_design/' + parts[0] + '/_view/' + parts[1] + params, {\n        headers: new h({'Content-Type': 'application/json'}),\n        method,\n        body: JSON.stringify(body)\n      });\n      ok = response.ok;\n      // status = response.status;\n      const result = await response.json();\n\n      if (!ok) {\n        result.status = response.status;\n        throw generateErrorFromResponse(result);\n      }\n\n      // fail the entire request if the result contains an error\n      for (const row of result.rows) {\n        /* istanbul ignore if */\n        if (row.value && row.value.error && row.value.error === \"builtin_reduce_error\") {\n          throw new Error(row.reason);\n        }\n      }\n\n      return new Promise(function (resolve) {\n        resolve(result);\n      }).then(postprocessAttachments(opts));\n    }\n\n    // We are using a temporary view, terrible for performance, good for testing\n    body = body || {};\n    for (const key of Object.keys(fun)) {\n      if (Array.isArray(fun[key])) {\n        body[key] = fun[key];\n      } else {\n        body[key] = fun[key].toString();\n      }\n    }\n\n    const response = await db.fetch('_temp_view' + params, {\n      headers: new h({'Content-Type': 'application/json'}),\n      method: 'POST',\n      body: JSON.stringify(body)\n    });\n\n    ok = response.ok;\n    // status = response.status;\n    const result = await response.json();\n    if (!ok) {\n      result.status = response.status;\n      throw generateErrorFromResponse(result);\n    }\n\n    return new Promise(function (resolve) {\n      resolve(result);\n    }).then(postprocessAttachments(opts));\n  }\n\n  // custom adapters can define their own api._query\n  // and override the default behavior\n  /* istanbul ignore next */\n  function customQuery(db, fun, opts) {\n    return new Promise(function (resolve, reject) {\n      db._query(fun, opts, function (err, res) {\n        if (err) {\n          return reject(err);\n        }\n        resolve(res);\n      });\n    });\n  }\n\n  // custom adapters can define their own api._viewCleanup\n  // and override the default behavior\n  /* istanbul ignore next */\n  function customViewCleanup(db) {\n    return new Promise(function (resolve, reject) {\n      db._viewCleanup(function (err, res) {\n        if (err) {\n          return reject(err);\n        }\n        resolve(res);\n      });\n    });\n  }\n\n  function defaultsTo(value) {\n    return function (reason) {\n      /* istanbul ignore else */\n      if (reason.status === 404) {\n        return value;\n      } else {\n        throw reason;\n      }\n    };\n  }\n\n  // returns a promise for a list of docs to update, based on the input docId.\n  // the order doesn't matter, because post-3.2.0, bulkDocs\n  // is an atomic operation in all three adapters.\n  async function getDocsToPersist(docId, view, docIdsToChangesAndEmits) {\n    const metaDocId = '_local/doc_' + docId;\n    const defaultMetaDoc = {_id: metaDocId, keys: []};\n    const docData = docIdsToChangesAndEmits.get(docId);\n    const indexableKeysToKeyValues = docData[0];\n    const changes = docData[1];\n\n    function getMetaDoc() {\n      if (isGenOne(changes)) {\n        // generation 1, so we can safely assume initial state\n        // for performance reasons (avoids unnecessary GETs)\n        return Promise.resolve(defaultMetaDoc);\n      }\n      return view.db.get(metaDocId).catch(defaultsTo(defaultMetaDoc));\n    }\n\n    function getKeyValueDocs(metaDoc) {\n      if (!metaDoc.keys.length) {\n        // no keys, no need for a lookup\n        return Promise.resolve({rows: []});\n      }\n      return view.db.allDocs({\n        keys: metaDoc.keys,\n        include_docs: true\n      });\n    }\n\n    function processKeyValueDocs(metaDoc, kvDocsRes) {\n      const kvDocs = [];\n      const oldKeys = new Set();\n\n      for (const row of kvDocsRes.rows) {\n        const doc = row.doc;\n        if (!doc) { // deleted\n          continue;\n        }\n        kvDocs.push(doc);\n        oldKeys.add(doc._id);\n        doc._deleted = !indexableKeysToKeyValues.has(doc._id);\n        if (!doc._deleted) {\n          const keyValue = indexableKeysToKeyValues.get(doc._id);\n          if ('value' in keyValue) {\n            doc.value = keyValue.value;\n          }\n        }\n      }\n      const newKeys = mapToKeysArray(indexableKeysToKeyValues);\n      for (const key of newKeys) {\n        if (!oldKeys.has(key)) {\n          // new doc\n          const kvDoc = {\n            _id: key\n          };\n          const keyValue = indexableKeysToKeyValues.get(key);\n          if ('value' in keyValue) {\n            kvDoc.value = keyValue.value;\n          }\n          kvDocs.push(kvDoc);\n        }\n      }\n      metaDoc.keys = uniq(newKeys.concat(metaDoc.keys));\n      kvDocs.push(metaDoc);\n\n      return kvDocs;\n    }\n\n    const metaDoc = await getMetaDoc();\n    const keyValueDocs = await getKeyValueDocs(metaDoc);\n    return processKeyValueDocs(metaDoc, keyValueDocs);\n  }\n\n  function updatePurgeSeq(view) {\n    // with this approach, we just assume to have processed all missing purges and write the latest\n    // purgeSeq into the _local/purgeSeq doc.\n    return view.sourceDB.get('_local/purges').then(function (res) {\n      const purgeSeq = res.purgeSeq;\n      return view.db.get('_local/purgeSeq').then(function (res) {\n        return res._rev;\n      })\n      .catch(defaultsTo(undefined))\n      .then(function (rev$$1) {\n        return view.db.put({\n          _id: '_local/purgeSeq',\n          _rev: rev$$1,\n          purgeSeq,\n        });\n      });\n    }).catch(function (err) {\n      if (err.status !== 404) {\n        throw err;\n      }\n    });\n  }\n\n  // updates all emitted key/value docs and metaDocs in the mrview database\n  // for the given batch of documents from the source database\n  function saveKeyValues(view, docIdsToChangesAndEmits, seq) {\n    var seqDocId = '_local/lastSeq';\n    return view.db.get(seqDocId)\n      .catch(defaultsTo({_id: seqDocId, seq: 0}))\n      .then(function (lastSeqDoc) {\n        var docIds = mapToKeysArray(docIdsToChangesAndEmits);\n        return Promise.all(docIds.map(function (docId) {\n          return getDocsToPersist(docId, view, docIdsToChangesAndEmits);\n        })).then(function (listOfDocsToPersist) {\n          var docsToPersist = listOfDocsToPersist.flat();\n          lastSeqDoc.seq = seq;\n          docsToPersist.push(lastSeqDoc);\n          // write all docs in a single operation, update the seq once\n          return view.db.bulkDocs({docs : docsToPersist});\n        })\n          // TODO: this should be placed somewhere else, probably? we're querying both docs twice\n          //   (first time when getting the actual purges).\n          .then(() => updatePurgeSeq(view));\n      });\n  }\n\n  function getQueue(view) {\n    const viewName = typeof view === 'string' ? view : view.name;\n    let queue = persistentQueues[viewName];\n    if (!queue) {\n      queue = persistentQueues[viewName] = new TaskQueue$1();\n    }\n    return queue;\n  }\n\n  async function updateView(view, opts) {\n    return sequentialize(getQueue(view), function () {\n      return updateViewInQueue(view, opts);\n    })();\n  }\n\n  async function updateViewInQueue(view, opts) {\n    // bind the emit function once\n    let mapResults;\n    let doc;\n    let taskId;\n\n    function emit(key, value) {\n      const output = {id: doc._id, key: normalizeKey(key)};\n      // Don't explicitly store the value unless it's defined and non-null.\n      // This saves on storage space, because often people don't use it.\n      if (typeof value !== 'undefined' && value !== null) {\n        output.value = normalizeKey(value);\n      }\n      mapResults.push(output);\n    }\n\n    const mapFun = mapper(view.mapFun, emit);\n\n    let currentSeq = view.seq || 0;\n\n    function createTask() {\n      return view.sourceDB.info().then(function (info) {\n        taskId = view.sourceDB.activeTasks.add({\n          name: 'view_indexing',\n          total_items: info.update_seq - currentSeq,\n        });\n      });\n    }\n\n    function processChange(docIdsToChangesAndEmits, seq) {\n      return function () {\n        return saveKeyValues(view, docIdsToChangesAndEmits, seq);\n      };\n    }\n\n    let indexed_docs = 0;\n    const progress = {\n      view: view.name,\n      indexed_docs\n    };\n    view.sourceDB.emit('indexing', progress);\n\n    const queue = new TaskQueue$1();\n\n    async function processNextBatch() {\n      const response = await view.sourceDB.changes({\n        return_docs: true,\n        conflicts: true,\n        include_docs: true,\n        style: 'all_docs',\n        since: currentSeq,\n        limit: opts.changes_batch_size\n      });\n      const purges = await getRecentPurges();\n      return processBatch(response, purges);\n    }\n\n    function getRecentPurges() {\n      return view.db.get('_local/purgeSeq').then(function (res) {\n        return res.purgeSeq;\n      })\n      .catch(defaultsTo(-1))\n      .then(function (purgeSeq) {\n        return view.sourceDB.get('_local/purges').then(function (res) {\n          const recentPurges = res.purges.filter(function (purge, index) {\n            return index > purgeSeq;\n          }).map((purge) => purge.docId);\n\n          const uniquePurges = recentPurges.filter(function (docId, index) {\n            return recentPurges.indexOf(docId) === index;\n          });\n\n          return Promise.all(uniquePurges.map(function (docId) {\n            return view.sourceDB.get(docId).then(function (doc) {\n              return { docId, doc };\n            })\n            .catch(defaultsTo({ docId }));\n          }));\n        })\n        .catch(defaultsTo([]));\n      });\n    }\n\n    function processBatch(response, purges) {\n      const results = response.results;\n      if (!results.length && !purges.length) {\n        return;\n      }\n\n      for (const purge of purges) {\n        const index = results.findIndex(function (change) {\n          return change.id === purge.docId;\n        });\n        if (index < 0) {\n          // mimic a db.remove() on the changes feed\n          const entry = {\n            _id: purge.docId,\n            doc: {\n              _id: purge.docId,\n              _deleted: 1,\n            },\n            changes: [],\n          };\n\n          if (purge.doc) {\n            // update with new winning rev after purge\n            entry.doc = purge.doc;\n            entry.changes.push({ rev: purge.doc._rev });\n          }\n\n          results.push(entry);\n        }\n      }\n\n      const docIdsToChangesAndEmits = createDocIdsToChangesAndEmits(results);\n\n      queue.add(processChange(docIdsToChangesAndEmits, currentSeq));\n\n      indexed_docs = indexed_docs + results.length;\n      const progress = {\n        view: view.name,\n        last_seq: response.last_seq,\n        results_count: results.length,\n        indexed_docs\n      };\n      view.sourceDB.emit('indexing', progress);\n      view.sourceDB.activeTasks.update(taskId, {completed_items: indexed_docs});\n\n      if (results.length < opts.changes_batch_size) {\n        return;\n      }\n      return processNextBatch();\n    }\n\n    function createDocIdsToChangesAndEmits(results) {\n      const docIdsToChangesAndEmits = new Map();\n      for (const change of results) {\n        if (change.doc._id[0] !== '_') {\n          mapResults = [];\n          doc = change.doc;\n\n          if (!doc._deleted) {\n            tryMap(view.sourceDB, mapFun, doc);\n          }\n          mapResults.sort(sortByKeyThenValue);\n\n          const indexableKeysToKeyValues = createIndexableKeysToKeyValues(mapResults);\n          docIdsToChangesAndEmits.set(change.doc._id, [\n            indexableKeysToKeyValues,\n            change.changes\n          ]);\n        }\n        currentSeq = change.seq;\n      }\n      return docIdsToChangesAndEmits;\n    }\n\n    function createIndexableKeysToKeyValues(mapResults) {\n      const indexableKeysToKeyValues = new Map();\n      let lastKey;\n      for (let i = 0, len = mapResults.length; i < len; i++) {\n        const emittedKeyValue = mapResults[i];\n        const complexKey = [emittedKeyValue.key, emittedKeyValue.id];\n        if (i > 0 && collate(emittedKeyValue.key, lastKey) === 0) {\n          complexKey.push(i); // dup key+id, so make it unique\n        }\n        indexableKeysToKeyValues.set(toIndexableString(complexKey), emittedKeyValue);\n        lastKey = emittedKeyValue.key;\n      }\n      return indexableKeysToKeyValues;\n    }\n\n    try {\n      await createTask();\n      await processNextBatch();\n      await queue.finish();\n      view.seq = currentSeq;\n      view.sourceDB.activeTasks.remove(taskId);\n    } catch (error) {\n      view.sourceDB.activeTasks.remove(taskId, error);\n    }\n  }\n\n  function reduceView(view, results, options) {\n    if (options.group_level === 0) {\n      delete options.group_level;\n    }\n\n    const shouldGroup = options.group || options.group_level;\n    const reduceFun = reducer(view.reduceFun);\n    const groups = [];\n    const lvl = isNaN(options.group_level)\n      ? Number.POSITIVE_INFINITY\n      : options.group_level;\n\n    for (const result of results) {\n      const last = groups[groups.length - 1];\n      let groupKey = shouldGroup ? result.key : null;\n\n      // only set group_level for array keys\n      if (shouldGroup && Array.isArray(groupKey)) {\n        groupKey = groupKey.slice(0, lvl);\n      }\n\n      if (last && collate(last.groupKey, groupKey) === 0) {\n        last.keys.push([result.key, result.id]);\n        last.values.push(result.value);\n        continue;\n      }\n      groups.push({\n        keys: [[result.key, result.id]],\n        values: [result.value],\n        groupKey\n      });\n    }\n\n    results = [];\n    for (const group of groups) {\n      const reduceTry = tryReduce(view.sourceDB, reduceFun, group.keys, group.values, false);\n      if (reduceTry.error && reduceTry.error instanceof BuiltInError) {\n        // CouchDB returns an error if a built-in errors out\n        throw reduceTry.error;\n      }\n      results.push({\n        // CouchDB just sets the value to null if a non-built-in errors out\n        value: reduceTry.error ? null : reduceTry.output,\n        key: group.groupKey\n      });\n    }\n    // no total_rows/offset when reducing\n    return { rows: sliceResults(results, options.limit, options.skip) };\n  }\n\n  function queryView(view, opts) {\n    return sequentialize(getQueue(view), function () {\n      return queryViewInQueue(view, opts);\n    })();\n  }\n\n  async function queryViewInQueue(view, opts) {\n    let totalRows;\n    const shouldReduce = view.reduceFun && opts.reduce !== false;\n    const skip = opts.skip || 0;\n    if (typeof opts.keys !== 'undefined' && !opts.keys.length) {\n      // equivalent query\n      opts.limit = 0;\n      delete opts.keys;\n    }\n\n    async function fetchFromView(viewOpts) {\n      viewOpts.include_docs = true;\n      const res = await view.db.allDocs(viewOpts);\n      totalRows = res.total_rows;\n\n      return res.rows.map(function (result) {\n        // implicit migration - in older versions of PouchDB,\n        // we explicitly stored the doc as {id: ..., key: ..., value: ...}\n        // this is tested in a migration test\n        /* istanbul ignore next */\n        if ('value' in result.doc && typeof result.doc.value === 'object' &&\n          result.doc.value !== null) {\n          const keys = Object.keys(result.doc.value).sort();\n          // this detection method is not perfect, but it's unlikely the user\n          // emitted a value which was an object with these 3 exact keys\n          const expectedKeys = ['id', 'key', 'value'];\n          if (!(keys < expectedKeys || keys > expectedKeys)) {\n            return result.doc.value;\n          }\n        }\n\n        const parsedKeyAndDocId = parseIndexableString(result.doc._id);\n        return {\n          key: parsedKeyAndDocId[0],\n          id: parsedKeyAndDocId[1],\n          value: ('value' in result.doc ? result.doc.value : null)\n        };\n      });\n    }\n\n    async function onMapResultsReady(rows) {\n      let finalResults;\n      if (shouldReduce) {\n        finalResults = reduceView(view, rows, opts);\n      } else if (typeof opts.keys === 'undefined') {\n        finalResults = {\n          total_rows: totalRows,\n          offset: skip,\n          rows\n        };\n      } else {\n        // support limit, skip for keys query\n        finalResults = {\n          total_rows: totalRows,\n          offset: skip,\n          rows: sliceResults(rows,opts.limit,opts.skip)\n        };\n      }\n      /* istanbul ignore if */\n      if (opts.update_seq) {\n        finalResults.update_seq = view.seq;\n      }\n      if (opts.include_docs) {\n        const docIds = uniq(rows.map(rowToDocId));\n\n        const allDocsRes = await view.sourceDB.allDocs({\n          keys: docIds,\n          include_docs: true,\n          conflicts: opts.conflicts,\n          attachments: opts.attachments,\n          binary: opts.binary\n        });\n        const docIdsToDocs = new Map();\n        for (const row of allDocsRes.rows) {\n          docIdsToDocs.set(row.id, row.doc);\n        }\n        for (const row of rows) {\n          const docId = rowToDocId(row);\n          const doc = docIdsToDocs.get(docId);\n          if (doc) {\n            row.doc = doc;\n          }\n        }\n      }\n      return finalResults;\n    }\n\n    if (typeof opts.keys !== 'undefined') {\n      const keys = opts.keys;\n      const fetchPromises = keys.map(function (key) {\n        const viewOpts = {\n          startkey : toIndexableString([key]),\n          endkey   : toIndexableString([key, {}])\n        };\n        /* istanbul ignore if */\n        if (opts.update_seq) {\n          viewOpts.update_seq = true;\n        }\n        return fetchFromView(viewOpts);\n      });\n      const result = await Promise.all(fetchPromises);\n      const flattenedResult = result.flat();\n      return onMapResultsReady(flattenedResult);\n    } else { // normal query, no 'keys'\n      const viewOpts = {\n        descending : opts.descending\n      };\n      /* istanbul ignore if */\n      if (opts.update_seq) {\n        viewOpts.update_seq = true;\n      }\n      let startkey;\n      let endkey;\n      if ('start_key' in opts) {\n        startkey = opts.start_key;\n      }\n      if ('startkey' in opts) {\n        startkey = opts.startkey;\n      }\n      if ('end_key' in opts) {\n        endkey = opts.end_key;\n      }\n      if ('endkey' in opts) {\n        endkey = opts.endkey;\n      }\n      if (typeof startkey !== 'undefined') {\n        viewOpts.startkey = opts.descending ?\n          toIndexableString([startkey, {}]) :\n          toIndexableString([startkey]);\n      }\n      if (typeof endkey !== 'undefined') {\n        let inclusiveEnd = opts.inclusive_end !== false;\n        if (opts.descending) {\n          inclusiveEnd = !inclusiveEnd;\n        }\n\n        viewOpts.endkey = toIndexableString(\n          inclusiveEnd ? [endkey, {}] : [endkey]);\n      }\n      if (typeof opts.key !== 'undefined') {\n        const keyStart = toIndexableString([opts.key]);\n        const keyEnd = toIndexableString([opts.key, {}]);\n        if (viewOpts.descending) {\n          viewOpts.endkey = keyStart;\n          viewOpts.startkey = keyEnd;\n        } else {\n          viewOpts.startkey = keyStart;\n          viewOpts.endkey = keyEnd;\n        }\n      }\n      if (!shouldReduce) {\n        if (typeof opts.limit === 'number') {\n          viewOpts.limit = opts.limit;\n        }\n        viewOpts.skip = skip;\n      }\n\n      const result = await fetchFromView(viewOpts);\n      return onMapResultsReady(result);\n    }\n  }\n\n  async function httpViewCleanup(db) {\n    const response = await db.fetch('_view_cleanup', {\n      headers: new h({'Content-Type': 'application/json'}),\n      method: 'POST'\n    });\n    return response.json();\n  }\n\n  async function localViewCleanup(db) {\n    try {\n      const metaDoc = await db.get('_local/' + localDocName);\n      const docsToViews = new Map();\n\n      for (const fullViewName of Object.keys(metaDoc.views)) {\n        const parts = parseViewName(fullViewName);\n        const designDocName = '_design/' + parts[0];\n        const viewName = parts[1];\n        let views = docsToViews.get(designDocName);\n        if (!views) {\n          views = new Set();\n          docsToViews.set(designDocName, views);\n        }\n        views.add(viewName);\n      }\n      const opts = {\n        keys : mapToKeysArray(docsToViews),\n        include_docs : true\n      };\n\n      const res = await db.allDocs(opts);\n      const viewsToStatus = {};\n      for (const row of res.rows) {\n        const ddocName = row.key.substring(8); // cuts off '_design/'\n        for (const viewName of docsToViews.get(row.key)) {\n          let fullViewName = ddocName + '/' + viewName;\n          /* istanbul ignore if */\n          if (!metaDoc.views[fullViewName]) {\n            // new format, without slashes, to support PouchDB 2.2.0\n            // migration test in pouchdb's browser.migration.js verifies this\n            fullViewName = viewName;\n          }\n          const viewDBNames = Object.keys(metaDoc.views[fullViewName]);\n          // design doc deleted, or view function nonexistent\n          const statusIsGood = row.doc && row.doc.views &&\n            row.doc.views[viewName];\n          for (const viewDBName of viewDBNames) {\n            viewsToStatus[viewDBName] = viewsToStatus[viewDBName] || statusIsGood;\n          }\n        }\n      }\n\n      const dbsToDelete = Object.keys(viewsToStatus)\n        .filter(function (viewDBName) { return !viewsToStatus[viewDBName]; });\n\n      const destroyPromises = dbsToDelete.map(function (viewDBName) {\n        return sequentialize(getQueue(viewDBName), function () {\n          return new db.constructor(viewDBName, db.__opts).destroy();\n        })();\n      });\n\n      return Promise.all(destroyPromises).then(function () {\n        return {ok: true};\n      });\n    } catch (err) {\n      if (err.status === 404) {\n        return {ok: true};\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  async function queryPromised(db, fun, opts) {\n    /* istanbul ignore next */\n    if (typeof db._query === 'function') {\n      return customQuery(db, fun, opts);\n    }\n    if (isRemote(db)) {\n      return httpQuery(db, fun, opts);\n    }\n\n    const updateViewOpts = {\n      changes_batch_size: db.__opts.view_update_changes_batch_size || CHANGES_BATCH_SIZE$1\n    };\n\n    if (typeof fun !== 'string') {\n      // temp_view\n      checkQueryParseError(opts, fun);\n\n      tempViewQueue.add(async function () {\n        const view = await createView(\n          /* sourceDB */ db,\n          /* viewName */ 'temp_view/temp_view',\n          /* mapFun */ fun.map,\n          /* reduceFun */ fun.reduce,\n          /* temporary */ true,\n          /* localDocName */ localDocName);\n\n        return fin(updateView(view, updateViewOpts).then(\n          function () { return queryView(view, opts); }),\n          function () { return view.db.destroy(); }\n        );\n      });\n      return tempViewQueue.finish();\n    } else {\n      // persistent view\n      const fullViewName = fun;\n      const parts = parseViewName(fullViewName);\n      const designDocName = parts[0];\n      const viewName = parts[1];\n\n      const doc = await db.get('_design/' + designDocName);\n      fun = doc.views && doc.views[viewName];\n\n      if (!fun) {\n        // basic validator; it's assumed that every subclass would want this\n        throw new NotFoundError(`ddoc ${doc._id} has no view named ${viewName}`);\n      }\n\n      ddocValidator(doc, viewName);\n      checkQueryParseError(opts, fun);\n\n      const view = await createView(\n        /* sourceDB */ db,\n        /* viewName */ fullViewName,\n        /* mapFun */ fun.map,\n        /* reduceFun */ fun.reduce,\n        /* temporary */ false,\n        /* localDocName */ localDocName);\n\n      if (opts.stale === 'ok' || opts.stale === 'update_after') {\n        if (opts.stale === 'update_after') {\n          nextTick(function () {\n            updateView(view, updateViewOpts);\n          });\n        }\n        return queryView(view, opts);\n      } else { // stale not ok\n        await updateView(view, updateViewOpts);\n        return queryView(view, opts);\n      }\n    }\n  }\n\n  function abstractQuery(fun, opts, callback) {\n    const db = this;\n    if (typeof opts === 'function') {\n      callback = opts;\n      opts = {};\n    }\n    opts = opts ? coerceOptions(opts) : {};\n\n    if (typeof fun === 'function') {\n      fun = {map : fun};\n    }\n\n    const promise = Promise.resolve().then(function () {\n      return queryPromised(db, fun, opts);\n    });\n    promisedCallback(promise, callback);\n    return promise;\n  }\n\n  const abstractViewCleanup = callbackify(function () {\n    const db = this;\n    /* istanbul ignore next */\n    if (typeof db._viewCleanup === 'function') {\n      return customViewCleanup(db);\n    }\n    if (isRemote(db)) {\n      return httpViewCleanup(db);\n    }\n    return localViewCleanup(db);\n  });\n\n  return {\n    query: abstractQuery,\n    viewCleanup: abstractViewCleanup\n  };\n}\n\nvar builtInReduce = {\n  _sum: function (keys, values) {\n    return sum(values);\n  },\n\n  _count: function (keys, values) {\n    return values.length;\n  },\n\n  _stats: function (keys, values) {\n    // no need to implement rereduce=true, because Pouch\n    // will never call it\n    function sumsqr(values) {\n      var _sumsqr = 0;\n      for (var i = 0, len = values.length; i < len; i++) {\n        var num = values[i];\n        _sumsqr += (num * num);\n      }\n      return _sumsqr;\n    }\n    return {\n      sum     : sum(values),\n      min     : Math.min.apply(null, values),\n      max     : Math.max.apply(null, values),\n      count   : values.length,\n      sumsqr : sumsqr(values)\n    };\n  }\n};\n\nfunction getBuiltIn(reduceFunString) {\n  if (/^_sum/.test(reduceFunString)) {\n    return builtInReduce._sum;\n  } else if (/^_count/.test(reduceFunString)) {\n    return builtInReduce._count;\n  } else if (/^_stats/.test(reduceFunString)) {\n    return builtInReduce._stats;\n  } else if (/^_/.test(reduceFunString)) {\n    throw new Error(reduceFunString + ' is not a supported reduce function.');\n  }\n}\n\nfunction mapper(mapFun, emit) {\n  // for temp_views one can use emit(doc, emit), see #38\n  if (typeof mapFun === \"function\" && mapFun.length === 2) {\n    var origMap = mapFun;\n    return function (doc) {\n      return origMap(doc, emit);\n    };\n  } else {\n    return evalFunctionWithEval(mapFun.toString(), emit);\n  }\n}\n\nfunction reducer(reduceFun) {\n  var reduceFunString = reduceFun.toString();\n  var builtIn = getBuiltIn(reduceFunString);\n  if (builtIn) {\n    return builtIn;\n  } else {\n    return evalFunctionWithEval(reduceFunString);\n  }\n}\n\nfunction ddocValidator(ddoc, viewName) {\n  var fun = ddoc.views && ddoc.views[viewName];\n  if (typeof fun.map !== 'string') {\n    throw new NotFoundError('ddoc ' + ddoc._id + ' has no string view named ' +\n      viewName + ', instead found object of type: ' + typeof fun.map);\n  }\n}\n\nvar localDocName = 'mrviews';\nvar abstract = createAbstractMapReduce(localDocName, mapper, reducer, ddocValidator);\n\nfunction query(fun, opts, callback) {\n  return abstract.query.call(this, fun, opts, callback);\n}\n\nfunction viewCleanup(callback) {\n  return abstract.viewCleanup.call(this, callback);\n}\n\nvar mapreduce = {\n  query,\n  viewCleanup\n};\n\nfunction fileHasChanged(localDoc, remoteDoc, filename) {\n  return !localDoc._attachments ||\n         !localDoc._attachments[filename] ||\n         localDoc._attachments[filename].digest !== remoteDoc._attachments[filename].digest;\n}\n\nfunction getDocAttachments(db, doc) {\n  var filenames = Object.keys(doc._attachments);\n  return Promise.all(filenames.map(function (filename) {\n    return db.getAttachment(doc._id, filename, {rev: doc._rev});\n  }));\n}\n\nfunction getDocAttachmentsFromTargetOrSource(target, src, doc) {\n  var doCheckForLocalAttachments = isRemote(src) && !isRemote(target);\n  var filenames = Object.keys(doc._attachments);\n\n  if (!doCheckForLocalAttachments) {\n    return getDocAttachments(src, doc);\n  }\n\n  return target.get(doc._id).then(function (localDoc) {\n    return Promise.all(filenames.map(function (filename) {\n      if (fileHasChanged(localDoc, doc, filename)) {\n        return src.getAttachment(doc._id, filename);\n      }\n\n      return target.getAttachment(localDoc._id, filename);\n    }));\n  }).catch(function (error) {\n    /* istanbul ignore if */\n    if (error.status !== 404) {\n      throw error;\n    }\n\n    return getDocAttachments(src, doc);\n  });\n}\n\nfunction createBulkGetOpts(diffs) {\n  var requests = [];\n  Object.keys(diffs).forEach(function (id) {\n    var missingRevs = diffs[id].missing;\n    missingRevs.forEach(function (missingRev) {\n      requests.push({\n        id,\n        rev: missingRev\n      });\n    });\n  });\n\n  return {\n    docs: requests,\n    revs: true,\n    latest: true\n  };\n}\n\n//\n// Fetch all the documents from the src as described in the \"diffs\",\n// which is a mapping of docs IDs to revisions. If the state ever\n// changes to \"cancelled\", then the returned promise will be rejected.\n// Else it will be resolved with a list of fetched documents.\n//\nfunction getDocs(src, target, diffs, state) {\n  diffs = clone(diffs); // we do not need to modify this\n\n  var resultDocs = [],\n      ok = true;\n\n  function getAllDocs() {\n\n    var bulkGetOpts = createBulkGetOpts(diffs);\n\n    if (!bulkGetOpts.docs.length) { // optimization: skip empty requests\n      return;\n    }\n\n    return src.bulkGet(bulkGetOpts).then(function (bulkGetResponse) {\n      /* istanbul ignore if */\n      if (state.cancelled) {\n        throw new Error('cancelled');\n      }\n      return Promise.all(bulkGetResponse.results.map(function (bulkGetInfo) {\n        return Promise.all(bulkGetInfo.docs.map(function (doc) {\n          var remoteDoc = doc.ok;\n\n          if (doc.error) {\n            // when AUTO_COMPACTION is set, docs can be returned which look\n            // like this: {\"missing\":\"1-7c3ac256b693c462af8442f992b83696\"}\n            ok = false;\n          }\n\n          if (!remoteDoc || !remoteDoc._attachments) {\n            return remoteDoc;\n          }\n\n          return getDocAttachmentsFromTargetOrSource(target, src, remoteDoc).then((attachments) => {\n            var filenames = Object.keys(remoteDoc._attachments);\n            attachments.forEach(function (attachment, i) {\n              var att = remoteDoc._attachments[filenames[i]];\n              delete att.stub;\n              delete att.length;\n              att.data = attachment;\n            });\n\n            return remoteDoc;\n          });\n        }));\n      }))\n\n      .then(function (results) {\n        resultDocs = resultDocs.concat(results.flat().filter(Boolean));\n      });\n    });\n  }\n\n  function returnResult() {\n    return { ok, docs:resultDocs };\n  }\n\n  return Promise.resolve()\n    .then(getAllDocs)\n    .then(returnResult);\n}\n\nvar CHECKPOINT_VERSION = 1;\nvar REPLICATOR = \"pouchdb\";\n// This is an arbitrary number to limit the\n// amount of replication history we save in the checkpoint.\n// If we save too much, the checkpoint docs will become very big,\n// if we save fewer, we'll run a greater risk of having to\n// read all the changes from 0 when checkpoint PUTs fail\n// CouchDB 2.0 has a more involved history pruning,\n// but let's go for the simple version for now.\nvar CHECKPOINT_HISTORY_SIZE = 5;\nvar LOWEST_SEQ = 0;\n\nfunction updateCheckpoint(db, id, checkpoint, session, returnValue) {\n  return db.get(id).catch(function (err) {\n    if (err.status === 404) {\n      if (db.adapter === 'http' || db.adapter === 'https') {\n        explainError(\n          404, 'PouchDB is just checking if a remote checkpoint exists.'\n        );\n      }\n      return {\n        session_id: session,\n        _id: id,\n        history: [],\n        replicator: REPLICATOR,\n        version: CHECKPOINT_VERSION\n      };\n    }\n    throw err;\n  }).then(function (doc) {\n    if (returnValue.cancelled) {\n      return;\n    }\n\n    // if the checkpoint has not changed, do not update\n    if (doc.last_seq === checkpoint) {\n      return;\n    }\n\n    // Filter out current entry for this replication\n    doc.history = (doc.history || []).filter(function (item) {\n      return item.session_id !== session;\n    });\n\n    // Add the latest checkpoint to history\n    doc.history.unshift({\n      last_seq: checkpoint,\n      session_id: session\n    });\n\n    // Just take the last pieces in history, to\n    // avoid really big checkpoint docs.\n    // see comment on history size above\n    doc.history = doc.history.slice(0, CHECKPOINT_HISTORY_SIZE);\n\n    doc.version = CHECKPOINT_VERSION;\n    doc.replicator = REPLICATOR;\n\n    doc.session_id = session;\n    doc.last_seq = checkpoint;\n\n    return db.put(doc).catch(function (err) {\n      if (err.status === 409) {\n        // retry; someone is trying to write a checkpoint simultaneously\n        return updateCheckpoint(db, id, checkpoint, session, returnValue);\n      }\n      throw err;\n    });\n  });\n}\n\nclass CheckpointerInternal {\n  constructor(src, target, id, returnValue, opts = {\n    writeSourceCheckpoint: true,\n    writeTargetCheckpoint: true,\n  }) {\n    this.src = src;\n    this.target = target;\n    this.id = id;\n    this.returnValue = returnValue;\n    this.opts = opts;\n\n    if (typeof opts.writeSourceCheckpoint === \"undefined\") {\n      opts.writeSourceCheckpoint = true;\n    }\n\n    if (typeof opts.writeTargetCheckpoint === \"undefined\") {\n      opts.writeTargetCheckpoint = true;\n    }\n  }\n\n  writeCheckpoint(checkpoint, session) {\n    var self = this;\n    return this.updateTarget(checkpoint, session).then(function () {\n      return self.updateSource(checkpoint, session);\n    });\n  }\n\n  updateTarget(checkpoint, session) {\n    if (this.opts.writeTargetCheckpoint) {\n      return updateCheckpoint(this.target, this.id, checkpoint,\n        session, this.returnValue);\n    } else {\n      return Promise.resolve(true);\n    }\n  }\n\n  updateSource(checkpoint, session) {\n    if (this.opts.writeSourceCheckpoint) {\n      var self = this;\n      return updateCheckpoint(this.src, this.id, checkpoint,\n        session, this.returnValue)\n        .catch(function (err) {\n          if (isForbiddenError(err)) {\n            self.opts.writeSourceCheckpoint = false;\n            return true;\n          }\n          throw err;\n        });\n    } else {\n      return Promise.resolve(true);\n    }\n  }\n\n  getCheckpoint() {\n    var self = this;\n\n    if (!self.opts.writeSourceCheckpoint && !self.opts.writeTargetCheckpoint) {\n      return Promise.resolve(LOWEST_SEQ);\n    }\n\n    if (self.opts && self.opts.writeSourceCheckpoint && !self.opts.writeTargetCheckpoint) {\n      return self.src.get(self.id).then(function (sourceDoc) {\n        return sourceDoc.last_seq || LOWEST_SEQ;\n      }).catch(function (err) {\n        /* istanbul ignore if */\n        if (err.status !== 404) {\n          throw err;\n        }\n        return LOWEST_SEQ;\n      });\n    }\n\n    return self.target.get(self.id).then(function (targetDoc) {\n      if (self.opts && self.opts.writeTargetCheckpoint && !self.opts.writeSourceCheckpoint) {\n        return targetDoc.last_seq || LOWEST_SEQ;\n      }\n\n      return self.src.get(self.id).then(function (sourceDoc) {\n        // Since we can't migrate an old version doc to a new one\n        // (no session id), we just go with the lowest seq in this case\n        /* istanbul ignore if */\n        if (targetDoc.version !== sourceDoc.version) {\n          return LOWEST_SEQ;\n        }\n\n        var version;\n        if (targetDoc.version) {\n          version = targetDoc.version.toString();\n        } else {\n          version = \"undefined\";\n        }\n\n        if (version in comparisons) {\n          return comparisons[version](targetDoc, sourceDoc);\n        }\n        /* istanbul ignore next */\n        return LOWEST_SEQ;\n      }, function (err) {\n        if (err.status === 404 && targetDoc.last_seq) {\n          return self.src.put({\n            _id: self.id,\n            last_seq: LOWEST_SEQ\n          }).then(function () {\n            return LOWEST_SEQ;\n          }, function (err) {\n            if (isForbiddenError(err)) {\n              self.opts.writeSourceCheckpoint = false;\n              return targetDoc.last_seq;\n            }\n            /* istanbul ignore next */\n            return LOWEST_SEQ;\n          });\n        }\n        throw err;\n      });\n    }).catch(function (err) {\n      if (err.status !== 404) {\n        throw err;\n      }\n      return LOWEST_SEQ;\n    });\n  }\n}\n\nvar comparisons = {\n  \"undefined\": function (targetDoc, sourceDoc) {\n    // This is the previous comparison function\n    if (collate(targetDoc.last_seq, sourceDoc.last_seq) === 0) {\n      return sourceDoc.last_seq;\n    }\n    /* istanbul ignore next */\n    return 0;\n  },\n  \"1\": function (targetDoc, sourceDoc) {\n    // This is the comparison function ported from CouchDB\n    return compareReplicationLogs(sourceDoc, targetDoc).last_seq;\n  }\n};\n\n// This checkpoint comparison is ported from CouchDBs source\n// they come from here:\n// https://github.com/apache/couchdb-couch-replicator/blob/master/src/couch_replicator.erl#L863-L906\n\nfunction compareReplicationLogs(srcDoc, tgtDoc) {\n  if (srcDoc.session_id === tgtDoc.session_id) {\n    return {\n      last_seq: srcDoc.last_seq,\n      history: srcDoc.history\n    };\n  }\n\n  return compareReplicationHistory(srcDoc.history, tgtDoc.history);\n}\n\nfunction compareReplicationHistory(sourceHistory, targetHistory) {\n  // the erlang loop via function arguments is not so easy to repeat in JS\n  // therefore, doing this as recursion\n  var S = sourceHistory[0];\n  var sourceRest = sourceHistory.slice(1);\n  var T = targetHistory[0];\n  var targetRest = targetHistory.slice(1);\n\n  if (!S || targetHistory.length === 0) {\n    return {\n      last_seq: LOWEST_SEQ,\n      history: []\n    };\n  }\n\n  var sourceId = S.session_id;\n  /* istanbul ignore if */\n  if (hasSessionId(sourceId, targetHistory)) {\n    return {\n      last_seq: S.last_seq,\n      history: sourceHistory\n    };\n  }\n\n  var targetId = T.session_id;\n  if (hasSessionId(targetId, sourceRest)) {\n    return {\n      last_seq: T.last_seq,\n      history: targetRest\n    };\n  }\n\n  return compareReplicationHistory(sourceRest, targetRest);\n}\n\nfunction hasSessionId(sessionId, history) {\n  var props = history[0];\n  var rest = history.slice(1);\n\n  if (!sessionId || history.length === 0) {\n    return false;\n  }\n\n  if (sessionId === props.session_id) {\n    return true;\n  }\n\n  return hasSessionId(sessionId, rest);\n}\n\nfunction isForbiddenError(err) {\n  return typeof err.status === 'number' && Math.floor(err.status / 100) === 4;\n}\n\nfunction Checkpointer(src, target, id, returnValue, opts) {\n  if (!(this instanceof CheckpointerInternal)) {\n    return new CheckpointerInternal(src, target, id, returnValue, opts);\n  }\n  return Checkpointer;\n}\n\nvar STARTING_BACK_OFF = 0;\n\nfunction backOff(opts, returnValue, error, callback) {\n  if (opts.retry === false) {\n    returnValue.emit('error', error);\n    returnValue.removeAllListeners();\n    return;\n  }\n  /* istanbul ignore if */\n  if (typeof opts.back_off_function !== 'function') {\n    opts.back_off_function = defaultBackOff;\n  }\n  returnValue.emit('requestError', error);\n  if (returnValue.state === 'active' || returnValue.state === 'pending') {\n    returnValue.emit('paused', error);\n    returnValue.state = 'stopped';\n    var backOffSet = function backoffTimeSet() {\n      opts.current_back_off = STARTING_BACK_OFF;\n    };\n    var removeBackOffSetter = function removeBackOffTimeSet() {\n      returnValue.removeListener('active', backOffSet);\n    };\n    returnValue.once('paused', removeBackOffSetter);\n    returnValue.once('active', backOffSet);\n  }\n\n  opts.current_back_off = opts.current_back_off || STARTING_BACK_OFF;\n  opts.current_back_off = opts.back_off_function(opts.current_back_off);\n  setTimeout(callback, opts.current_back_off);\n}\n\nfunction sortObjectPropertiesByKey(queryParams) {\n  return Object.keys(queryParams).sort(collate).reduce(function (result, key) {\n    result[key] = queryParams[key];\n    return result;\n  }, {});\n}\n\n// Generate a unique id particular to this replication.\n// Not guaranteed to align perfectly with CouchDB's rep ids.\nfunction generateReplicationId(src, target, opts) {\n  var docIds = opts.doc_ids ? opts.doc_ids.sort(collate) : '';\n  var filterFun = opts.filter ? opts.filter.toString() : '';\n  var queryParams = '';\n  var filterViewName =  '';\n  var selector = '';\n\n  // possibility for checkpoints to be lost here as behaviour of\n  // JSON.stringify is not stable (see #6226)\n  /* istanbul ignore if */\n  if (opts.selector) {\n    selector = JSON.stringify(opts.selector);\n  }\n\n  if (opts.filter && opts.query_params) {\n    queryParams = JSON.stringify(sortObjectPropertiesByKey(opts.query_params));\n  }\n\n  if (opts.filter && opts.filter === '_view') {\n    filterViewName = opts.view.toString();\n  }\n\n  return Promise.all([src.id(), target.id()]).then(function (res) {\n    var queryData = res[0] + res[1] + filterFun + filterViewName +\n      queryParams + docIds + selector;\n    return new Promise(function (resolve) {\n      binaryMd5(queryData, resolve);\n    });\n  }).then(function (md5sum) {\n    // can't use straight-up md5 alphabet, because\n    // the char '/' is interpreted as being for attachments,\n    // and + is also not url-safe\n    md5sum = md5sum.replace(/\\//g, '.').replace(/\\+/g, '_');\n    return '_local/' + md5sum;\n  });\n}\n\nfunction replicate(src, target, opts, returnValue, result) {\n  var batches = [];               // list of batches to be processed\n  var currentBatch;               // the batch currently being processed\n  var pendingBatch = {\n    seq: 0,\n    changes: [],\n    docs: []\n  }; // next batch, not yet ready to be processed\n  var writingCheckpoint = false;  // true while checkpoint is being written\n  var changesCompleted = false;   // true when all changes received\n  var replicationCompleted = false; // true when replication has completed\n  // initial_last_seq is the state of the source db before\n  // replication started, and it is _not_ updated during\n  // replication or used anywhere else, as opposed to last_seq\n  var initial_last_seq = 0;\n  var last_seq = 0;\n  var continuous = opts.continuous || opts.live || false;\n  var batch_size = opts.batch_size || 100;\n  var batches_limit = opts.batches_limit || 10;\n  var style = opts.style || 'all_docs';\n  var changesPending = false;     // true while src.changes is running\n  var doc_ids = opts.doc_ids;\n  var selector = opts.selector;\n  var repId;\n  var checkpointer;\n  var changedDocs = [];\n  // Like couchdb, every replication gets a unique session id\n  var session = uuid();\n  var taskId;\n\n  result = result || {\n    ok: true,\n    start_time: new Date().toISOString(),\n    docs_read: 0,\n    docs_written: 0,\n    doc_write_failures: 0,\n    errors: []\n  };\n\n  var changesOpts = {};\n  returnValue.ready(src, target);\n\n  function initCheckpointer() {\n    if (checkpointer) {\n      return Promise.resolve();\n    }\n    return generateReplicationId(src, target, opts).then(function (res) {\n      repId = res;\n\n      var checkpointOpts = {};\n      if (opts.checkpoint === false) {\n        checkpointOpts = { writeSourceCheckpoint: false, writeTargetCheckpoint: false };\n      } else if (opts.checkpoint === 'source') {\n        checkpointOpts = { writeSourceCheckpoint: true, writeTargetCheckpoint: false };\n      } else if (opts.checkpoint === 'target') {\n        checkpointOpts = { writeSourceCheckpoint: false, writeTargetCheckpoint: true };\n      } else {\n        checkpointOpts = { writeSourceCheckpoint: true, writeTargetCheckpoint: true };\n      }\n\n      checkpointer = new Checkpointer(src, target, repId, returnValue, checkpointOpts);\n    });\n  }\n\n  function writeDocs() {\n    changedDocs = [];\n\n    if (currentBatch.docs.length === 0) {\n      return;\n    }\n    var docs = currentBatch.docs;\n    var bulkOpts = {timeout: opts.timeout};\n    return target.bulkDocs({docs, new_edits: false}, bulkOpts).then(function (res) {\n      /* istanbul ignore if */\n      if (returnValue.cancelled) {\n        completeReplication();\n        throw new Error('cancelled');\n      }\n\n      // `res` doesn't include full documents (which live in `docs`), so we create a map of\n      // (id -> error), and check for errors while iterating over `docs`\n      var errorsById = Object.create(null);\n      res.forEach(function (res) {\n        if (res.error) {\n          errorsById[res.id] = res;\n        }\n      });\n\n      var errorsNo = Object.keys(errorsById).length;\n      result.doc_write_failures += errorsNo;\n      result.docs_written += docs.length - errorsNo;\n\n      docs.forEach(function (doc) {\n        var error = errorsById[doc._id];\n        if (error) {\n          result.errors.push(error);\n          // Normalize error name. i.e. 'Unauthorized' -> 'unauthorized' (eg Sync Gateway)\n          var errorName = (error.name || '').toLowerCase();\n          if (errorName === 'unauthorized' || errorName === 'forbidden') {\n            returnValue.emit('denied', clone(error));\n          } else {\n            throw error;\n          }\n        } else {\n          changedDocs.push(doc);\n        }\n      });\n\n    }, function (err) {\n      result.doc_write_failures += docs.length;\n      throw err;\n    });\n  }\n\n  function finishBatch() {\n    if (currentBatch.error) {\n      throw new Error('There was a problem getting docs.');\n    }\n    result.last_seq = last_seq = currentBatch.seq;\n    var outResult = clone(result);\n    if (changedDocs.length) {\n      outResult.docs = changedDocs;\n      // Attach 'pending' property if server supports it (CouchDB 2.0+)\n      /* istanbul ignore if */\n      if (typeof currentBatch.pending === 'number') {\n        outResult.pending = currentBatch.pending;\n        delete currentBatch.pending;\n      }\n      returnValue.emit('change', outResult);\n    }\n    writingCheckpoint = true;\n\n    src.info().then(function (info) {\n      var task = src.activeTasks.get(taskId);\n      if (!currentBatch || !task) {\n        return;\n      }\n\n      var completed = task.completed_items || 0;\n      var total_items = parseInt(info.update_seq, 10) - parseInt(initial_last_seq, 10);\n      src.activeTasks.update(taskId, {\n        completed_items: completed + currentBatch.changes.length,\n        total_items\n      });\n    });\n\n    return checkpointer.writeCheckpoint(currentBatch.seq,\n        session).then(function () {\n      returnValue.emit('checkpoint', { 'checkpoint': currentBatch.seq });\n      writingCheckpoint = false;\n      /* istanbul ignore if */\n      if (returnValue.cancelled) {\n        completeReplication();\n        throw new Error('cancelled');\n      }\n      currentBatch = undefined;\n      getChanges();\n    }).catch(function (err) {\n      onCheckpointError(err);\n      throw err;\n    });\n  }\n\n  function getDiffs() {\n    var diff = {};\n    currentBatch.changes.forEach(function (change) {\n      returnValue.emit('checkpoint', { 'revs_diff': change });\n      // Couchbase Sync Gateway emits these, but we can ignore them\n      /* istanbul ignore if */\n      if (change.id === \"_user/\") {\n        return;\n      }\n      diff[change.id] = change.changes.map(function (x) {\n        return x.rev;\n      });\n    });\n    return target.revsDiff(diff).then(function (diffs) {\n      /* istanbul ignore if */\n      if (returnValue.cancelled) {\n        completeReplication();\n        throw new Error('cancelled');\n      }\n      // currentBatch.diffs elements are deleted as the documents are written\n      currentBatch.diffs = diffs;\n    });\n  }\n\n  function getBatchDocs() {\n    return getDocs(src, target, currentBatch.diffs, returnValue).then(function (got) {\n      currentBatch.error = !got.ok;\n      got.docs.forEach(function (doc) {\n        delete currentBatch.diffs[doc._id];\n        result.docs_read++;\n        currentBatch.docs.push(doc);\n      });\n    });\n  }\n\n  function startNextBatch() {\n    if (returnValue.cancelled || currentBatch) {\n      return;\n    }\n    if (batches.length === 0) {\n      processPendingBatch(true);\n      return;\n    }\n    currentBatch = batches.shift();\n    returnValue.emit('checkpoint', { 'start_next_batch': currentBatch.seq });\n    getDiffs()\n      .then(getBatchDocs)\n      .then(writeDocs)\n      .then(finishBatch)\n      .then(startNextBatch)\n      .catch(function (err) {\n        abortReplication('batch processing terminated with error', err);\n      });\n  }\n\n\n  function processPendingBatch(immediate) {\n    if (pendingBatch.changes.length === 0) {\n      if (batches.length === 0 && !currentBatch) {\n        if ((continuous && changesOpts.live) || changesCompleted) {\n          returnValue.state = 'pending';\n          returnValue.emit('paused');\n        }\n        if (changesCompleted) {\n          completeReplication();\n        }\n      }\n      return;\n    }\n    if (\n      immediate ||\n      changesCompleted ||\n      pendingBatch.changes.length >= batch_size\n    ) {\n      batches.push(pendingBatch);\n      pendingBatch = {\n        seq: 0,\n        changes: [],\n        docs: []\n      };\n      if (returnValue.state === 'pending' || returnValue.state === 'stopped') {\n        returnValue.state = 'active';\n        returnValue.emit('active');\n      }\n      startNextBatch();\n    }\n  }\n\n\n  function abortReplication(reason, err) {\n    if (replicationCompleted) {\n      return;\n    }\n    if (!err.message) {\n      err.message = reason;\n    }\n    result.ok = false;\n    result.status = 'aborting';\n    batches = [];\n    pendingBatch = {\n      seq: 0,\n      changes: [],\n      docs: []\n    };\n    completeReplication(err);\n  }\n\n\n  function completeReplication(fatalError) {\n    if (replicationCompleted) {\n      return;\n    }\n    /* istanbul ignore if */\n    if (returnValue.cancelled) {\n      result.status = 'cancelled';\n      if (writingCheckpoint) {\n        return;\n      }\n    }\n    result.status = result.status || 'complete';\n    result.end_time = new Date().toISOString();\n    result.last_seq = last_seq;\n    replicationCompleted = true;\n\n    src.activeTasks.remove(taskId, fatalError);\n\n    if (fatalError) {\n      // need to extend the error because Firefox considers \".result\" read-only\n      fatalError = createError(fatalError);\n      fatalError.result = result;\n\n      // Normalize error name. i.e. 'Unauthorized' -> 'unauthorized' (eg Sync Gateway)\n      var errorName = (fatalError.name || '').toLowerCase();\n      if (errorName === 'unauthorized' || errorName === 'forbidden') {\n        returnValue.emit('error', fatalError);\n        returnValue.removeAllListeners();\n      } else {\n        backOff(opts, returnValue, fatalError, function () {\n          replicate(src, target, opts, returnValue);\n        });\n      }\n    } else {\n      returnValue.emit('complete', result);\n      returnValue.removeAllListeners();\n    }\n  }\n\n  function onChange(change, pending, lastSeq) {\n    /* istanbul ignore if */\n    if (returnValue.cancelled) {\n      return completeReplication();\n    }\n    // Attach 'pending' property if server supports it (CouchDB 2.0+)\n    /* istanbul ignore if */\n    if (typeof pending === 'number') {\n      pendingBatch.pending = pending;\n    }\n\n    var filter = filterChange(opts)(change);\n    if (!filter) {\n      // update processed items count by 1\n      var task = src.activeTasks.get(taskId);\n      if (task) {\n        // we can assume that task exists here? shouldn't be deleted by here.\n        var completed = task.completed_items || 0;\n        src.activeTasks.update(taskId, {completed_items: ++completed});\n      }\n      return;\n    }\n    pendingBatch.seq = change.seq || lastSeq;\n    pendingBatch.changes.push(change);\n    returnValue.emit('checkpoint', { 'pending_batch': pendingBatch.seq });\n    nextTick(function () {\n      processPendingBatch(batches.length === 0 && changesOpts.live);\n    });\n  }\n\n\n  function onChangesComplete(changes) {\n    changesPending = false;\n    /* istanbul ignore if */\n    if (returnValue.cancelled) {\n      return completeReplication();\n    }\n\n    // if no results were returned then we're done,\n    // else fetch more\n    if (changes.results.length > 0) {\n      changesOpts.since = changes.results[changes.results.length - 1].seq;\n      getChanges();\n      processPendingBatch(true);\n    } else {\n\n      var complete = function () {\n        if (continuous) {\n          changesOpts.live = true;\n          getChanges();\n        } else {\n          changesCompleted = true;\n        }\n        processPendingBatch(true);\n      };\n\n      // update the checkpoint so we start from the right seq next time\n      if (!currentBatch && changes.results.length === 0) {\n        writingCheckpoint = true;\n        checkpointer.writeCheckpoint(changes.last_seq,\n            session).then(function () {\n          writingCheckpoint = false;\n          result.last_seq = last_seq = changes.last_seq;\n          if (returnValue.cancelled) {\n            completeReplication();\n            throw new Error('cancelled');\n          } else {\n            complete();\n          }\n        })\n        .catch(onCheckpointError);\n      } else {\n        complete();\n      }\n    }\n  }\n\n\n  function onChangesError(err) {\n    changesPending = false;\n    /* istanbul ignore if */\n    if (returnValue.cancelled) {\n      return completeReplication();\n    }\n    abortReplication('changes rejected', err);\n  }\n\n\n  function getChanges() {\n    if (!(\n      !changesPending &&\n      !changesCompleted &&\n      batches.length < batches_limit\n      )) {\n      return;\n    }\n    changesPending = true;\n    function abortChanges() {\n      changes.cancel();\n    }\n    function removeListener() {\n      returnValue.removeListener('cancel', abortChanges);\n    }\n\n    if (returnValue._changes) { // remove old changes() and listeners\n      returnValue.removeListener('cancel', returnValue._abortChanges);\n      returnValue._changes.cancel();\n    }\n    returnValue.once('cancel', abortChanges);\n\n    var changes = src.changes(changesOpts)\n      .on('change', onChange);\n    changes.then(removeListener, removeListener);\n    changes.then(onChangesComplete)\n      .catch(onChangesError);\n\n    if (opts.retry) {\n      // save for later so we can cancel if necessary\n      returnValue._changes = changes;\n      returnValue._abortChanges = abortChanges;\n    }\n  }\n\n  function createTask(checkpoint) {\n    return src.info().then(function (info) {\n      var total_items = typeof opts.since === 'undefined' ?\n        parseInt(info.update_seq, 10) - parseInt(checkpoint, 10) :\n        parseInt(info.update_seq, 10);\n\n      taskId = src.activeTasks.add({\n        name: `${continuous ? 'continuous ' : ''}replication from ${info.db_name}` ,\n        total_items,\n      });\n\n      return checkpoint;\n    });\n  }\n\n  function startChanges() {\n    initCheckpointer().then(function () {\n      /* istanbul ignore if */\n      if (returnValue.cancelled) {\n        completeReplication();\n        return;\n      }\n      return checkpointer.getCheckpoint().then(createTask).then(function (checkpoint) {\n        last_seq = checkpoint;\n        initial_last_seq = checkpoint;\n        changesOpts = {\n          since: last_seq,\n          limit: batch_size,\n          batch_size,\n          style,\n          doc_ids,\n          selector,\n          return_docs: true // required so we know when we're done\n        };\n        if (opts.filter) {\n          if (typeof opts.filter !== 'string') {\n            // required for the client-side filter in onChange\n            changesOpts.include_docs = true;\n          } else { // ddoc filter\n            changesOpts.filter = opts.filter;\n          }\n        }\n        if ('heartbeat' in opts) {\n          changesOpts.heartbeat = opts.heartbeat;\n        }\n        if ('timeout' in opts) {\n          changesOpts.timeout = opts.timeout;\n        }\n        if (opts.query_params) {\n          changesOpts.query_params = opts.query_params;\n        }\n        if (opts.view) {\n          changesOpts.view = opts.view;\n        }\n        getChanges();\n      });\n    }).catch(function (err) {\n      abortReplication('getCheckpoint rejected with ', err);\n    });\n  }\n\n  /* istanbul ignore next */\n  function onCheckpointError(err) {\n    writingCheckpoint = false;\n    abortReplication('writeCheckpoint completed with error', err);\n  }\n\n  /* istanbul ignore if */\n  if (returnValue.cancelled) { // cancelled immediately\n    completeReplication();\n    return;\n  }\n\n  if (!returnValue._addedListeners) {\n    returnValue.once('cancel', completeReplication);\n\n    if (typeof opts.complete === 'function') {\n      returnValue.once('error', opts.complete);\n      returnValue.once('complete', function (result) {\n        opts.complete(null, result);\n      });\n    }\n    returnValue._addedListeners = true;\n  }\n\n  if (typeof opts.since === 'undefined') {\n    startChanges();\n  } else {\n    initCheckpointer().then(function () {\n      writingCheckpoint = true;\n      return checkpointer.writeCheckpoint(opts.since, session);\n    }).then(function () {\n      writingCheckpoint = false;\n      /* istanbul ignore if */\n      if (returnValue.cancelled) {\n        completeReplication();\n        return;\n      }\n      last_seq = opts.since;\n      startChanges();\n    }).catch(onCheckpointError);\n  }\n}\n\n// We create a basic promise so the caller can cancel the replication possibly\n// before we have actually started listening to changes etc\nclass Replication extends EE {\n  constructor() {\n    super();\n    this.cancelled = false;\n    this.state = 'pending';\n    const promise = new Promise((fulfill, reject) => {\n      this.once('complete', fulfill);\n      this.once('error', reject);\n    });\n    this.then = function (resolve, reject) {\n      return promise.then(resolve, reject);\n    };\n    this.catch = function (reject) {\n      return promise.catch(reject);\n    };\n    // As we allow error handling via \"error\" event as well,\n    // put a stub in here so that rejecting never throws UnhandledError.\n    this.catch(function () {});\n  }\n\n  cancel() {\n    this.cancelled = true;\n    this.state = 'cancelled';\n    this.emit('cancel');\n  }\n\n  ready(src, target) {\n    if (this._readyCalled) {\n      return;\n    }\n    this._readyCalled = true;\n\n    const onDestroy = () => {\n      this.cancel();\n    };\n    src.once('destroyed', onDestroy);\n    target.once('destroyed', onDestroy);\n    function cleanup() {\n      src.removeListener('destroyed', onDestroy);\n      target.removeListener('destroyed', onDestroy);\n    }\n    this.once('complete', cleanup);\n    this.once('error', cleanup);\n  }\n}\n\nfunction toPouch(db, opts) {\n  var PouchConstructor = opts.PouchConstructor;\n  if (typeof db === 'string') {\n    return new PouchConstructor(db, opts);\n  } else {\n    return db;\n  }\n}\n\nfunction replicateWrapper(src, target, opts, callback) {\n\n  if (typeof opts === 'function') {\n    callback = opts;\n    opts = {};\n  }\n  if (typeof opts === 'undefined') {\n    opts = {};\n  }\n\n  if (opts.doc_ids && !Array.isArray(opts.doc_ids)) {\n    throw createError(BAD_REQUEST,\n                       \"`doc_ids` filter parameter is not a list.\");\n  }\n\n  opts.complete = callback;\n  opts = clone(opts);\n  opts.continuous = opts.continuous || opts.live;\n  opts.retry = ('retry' in opts) ? opts.retry : false;\n  opts.PouchConstructor = opts.PouchConstructor || this;\n  var replicateRet = new Replication(opts);\n  var srcPouch = toPouch(src, opts);\n  var targetPouch = toPouch(target, opts);\n  replicate(srcPouch, targetPouch, opts, replicateRet);\n  return replicateRet;\n}\n\nfunction sync(src, target, opts, callback) {\n  if (typeof opts === 'function') {\n    callback = opts;\n    opts = {};\n  }\n  if (typeof opts === 'undefined') {\n    opts = {};\n  }\n  opts = clone(opts);\n  opts.PouchConstructor = opts.PouchConstructor || this;\n  src = toPouch(src, opts);\n  target = toPouch(target, opts);\n  return new Sync(src, target, opts, callback);\n}\n\nclass Sync extends EE {\n  constructor(src, target, opts, callback) {\n    super();\n    this.canceled = false;\n\n    const optsPush = opts.push ? Object.assign({}, opts, opts.push) : opts;\n    const optsPull = opts.pull ? Object.assign({}, opts, opts.pull) : opts;\n\n    this.push = replicateWrapper(src, target, optsPush);\n    this.pull = replicateWrapper(target, src, optsPull);\n\n    this.pushPaused = true;\n    this.pullPaused = true;\n\n    const pullChange = (change) => {\n      this.emit('change', {\n        direction: 'pull',\n        change\n      });\n    };\n    const pushChange = (change) => {\n      this.emit('change', {\n        direction: 'push',\n        change\n      });\n    };\n    const pushDenied = (doc) => {\n      this.emit('denied', {\n        direction: 'push',\n        doc\n      });\n    };\n    const pullDenied = (doc) => {\n      this.emit('denied', {\n        direction: 'pull',\n        doc\n      });\n    };\n    const pushPaused = () => {\n      this.pushPaused = true;\n      /* istanbul ignore if */\n      if (this.pullPaused) {\n        this.emit('paused');\n      }\n    };\n    const pullPaused = () => {\n      this.pullPaused = true;\n      /* istanbul ignore if */\n      if (this.pushPaused) {\n        this.emit('paused');\n      }\n    };\n    const pushActive = () => {\n      this.pushPaused = false;\n      /* istanbul ignore if */\n      if (this.pullPaused) {\n        this.emit('active', {\n          direction: 'push'\n        });\n      }\n    };\n    const pullActive = () => {\n      this.pullPaused = false;\n      /* istanbul ignore if */\n      if (this.pushPaused) {\n        this.emit('active', {\n          direction: 'pull'\n        });\n      }\n    };\n\n    let removed = {};\n\n    const removeAll = (type) => { // type is 'push' or 'pull'\n      return (event, func) => {\n        const isChange = event === 'change' &&\n          (func === pullChange || func === pushChange);\n        const isDenied = event === 'denied' &&\n          (func === pullDenied || func === pushDenied);\n        const isPaused = event === 'paused' &&\n          (func === pullPaused || func === pushPaused);\n        const isActive = event === 'active' &&\n          (func === pullActive || func === pushActive);\n\n        if (isChange || isDenied || isPaused || isActive) {\n          if (!(event in removed)) {\n            removed[event] = {};\n          }\n          removed[event][type] = true;\n          if (Object.keys(removed[event]).length === 2) {\n            // both push and pull have asked to be removed\n            this.removeAllListeners(event);\n          }\n        }\n      };\n    };\n\n    if (opts.live) {\n      this.push.on('complete', this.pull.cancel.bind(this.pull));\n      this.pull.on('complete', this.push.cancel.bind(this.push));\n    }\n\n    function addOneListener(ee, event, listener) {\n      if (ee.listeners(event).indexOf(listener) == -1) {\n        ee.on(event, listener);\n      }\n    }\n\n    this.on('newListener', function (event) {\n      if (event === 'change') {\n        addOneListener(this.pull, 'change', pullChange);\n        addOneListener(this.push, 'change', pushChange);\n      } else if (event === 'denied') {\n        addOneListener(this.pull, 'denied', pullDenied);\n        addOneListener(this.push, 'denied', pushDenied);\n      } else if (event === 'active') {\n        addOneListener(this.pull, 'active', pullActive);\n        addOneListener(this.push, 'active', pushActive);\n      } else if (event === 'paused') {\n        addOneListener(this.pull, 'paused', pullPaused);\n        addOneListener(this.push, 'paused', pushPaused);\n      }\n    });\n\n    this.on('removeListener', function (event) {\n      if (event === 'change') {\n        this.pull.removeListener('change', pullChange);\n        this.push.removeListener('change', pushChange);\n      } else if (event === 'denied') {\n        this.pull.removeListener('denied', pullDenied);\n        this.push.removeListener('denied', pushDenied);\n      } else if (event === 'active') {\n        this.pull.removeListener('active', pullActive);\n        this.push.removeListener('active', pushActive);\n      } else if (event === 'paused') {\n        this.pull.removeListener('paused', pullPaused);\n        this.push.removeListener('paused', pushPaused);\n      }\n    });\n\n    this.pull.on('removeListener', removeAll('pull'));\n    this.push.on('removeListener', removeAll('push'));\n\n    const promise = Promise.all([\n      this.push,\n      this.pull\n    ]).then((resp) => {\n      const out = {\n        push: resp[0],\n        pull: resp[1]\n      };\n      this.emit('complete', out);\n      if (callback) {\n        callback(null, out);\n      }\n      this.removeAllListeners();\n      return out;\n    }, (err) => {\n      this.cancel();\n      if (callback) {\n        // if there's a callback, then the callback can receive\n        // the error event\n        callback(err);\n      } else {\n        // if there's no callback, then we're safe to emit an error\n        // event, which would otherwise throw an unhandled error\n        // due to 'error' being a special event in EventEmitters\n        this.emit('error', err);\n      }\n      this.removeAllListeners();\n      if (callback) {\n        // no sense throwing if we're already emitting an 'error' event\n        throw err;\n      }\n    });\n\n    this.then = function (success, err) {\n      return promise.then(success, err);\n    };\n\n    this.catch = function (err) {\n      return promise.catch(err);\n    };\n  }\n\n  cancel() {\n    if (!this.canceled) {\n      this.canceled = true;\n      this.push.cancel();\n      this.pull.cancel();\n    }\n  }\n}\n\nfunction replication(PouchDB) {\n  PouchDB.replicate = replicateWrapper;\n  PouchDB.sync = sync;\n\n  Object.defineProperty(PouchDB.prototype, 'replicate', {\n    get: function () {\n      var self = this;\n      if (typeof this.replicateMethods === 'undefined') {\n        this.replicateMethods = {\n          from: function (other, opts, callback) {\n            return self.constructor.replicate(other, self, opts, callback);\n          },\n          to: function (other, opts, callback) {\n            return self.constructor.replicate(self, other, opts, callback);\n          }\n        };\n      }\n      return this.replicateMethods;\n    }\n  });\n\n  PouchDB.prototype.sync = function (dbName, opts, callback) {\n    return this.constructor.sync(this, dbName, opts, callback);\n  };\n}\n\nPouchDB.plugin(IDBPouch)\n  .plugin(HttpPouch$1)\n  .plugin(mapreduce)\n  .plugin(replication);\n\nexport default PouchDB;\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAOA,YAAQ,YAAY,SAASA,WAAU,OAAO;AAC5C,UAAIC,SAAQ,CAAC;AACb,MAAAA,OAAM,KAAK,EAAC,KAAK,MAAK,CAAC;AAEvB,UAAI,MAAM;AACV,UAAI,MAAM,KAAK,QAAQ,KAAK,GAAG,aAAaC,OAAM,GAAG,KAAK,OAAO;AACjE,aAAQ,OAAOD,OAAM,IAAI,GAAI;AAC3B,cAAM,KAAK;AACX,iBAAS,KAAK,UAAU;AACxB,cAAM,KAAK,OAAO;AAClB,eAAO;AACP,YAAI,KAAK;AACP,iBAAO;AAAA,QACT,WAAW,OAAO,QAAQ,UAAU;AAClC,iBAAO,OAAO,QAAQ,cAAc,OAAO,KAAK,UAAU,GAAG;AAAA,QAC/D,WAAW,QAAQ,MAAM;AACvB,iBAAO;AAAA,QACT,WAAW,MAAM,QAAQ,GAAG,GAAG;AAC7B,UAAAA,OAAM,KAAK,EAAC,KAAK,IAAG,CAAC;AACrB,eAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,0BAAc,MAAM,IAAI,KAAK;AAC7B,YAAAA,OAAM,KAAK,EAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,YAAW,CAAC;AAAA,UAC/C;AACA,UAAAA,OAAM,KAAK,EAAC,KAAK,IAAG,CAAC;AAAA,QACvB,OAAO;AACL,UAAAC,QAAO,CAAC;AACR,eAAK,KAAK,KAAK;AACb,gBAAI,IAAI,eAAe,CAAC,GAAG;AACzB,cAAAA,MAAK,KAAK,CAAC;AAAA,YACb;AAAA,UACF;AACA,UAAAD,OAAM,KAAK,EAAC,KAAK,IAAG,CAAC;AACrB,eAAK,IAAIC,MAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,kBAAMA,MAAK,CAAC;AACZ,oBAAQ,IAAI,GAAG;AACf,wBAAa,IAAI,IAAI,MAAM;AAC3B,yBAAa,KAAK,UAAU,GAAG,IAAI;AACnC,YAAAD,OAAM,KAAK,EAAC,KAAK,OAAO,QAAQ,UAAS,CAAC;AAAA,UAC5C;AACA,UAAAA,OAAM,KAAK,EAAC,KAAK,IAAG,CAAC;AAAA,QACvB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,aAASE,KAAI,KAAK,OAAO,WAAW;AAClC,UAAI,kBAAkB,UAAU,UAAU,SAAS,CAAC;AACpD,UAAI,QAAQ,gBAAgB,SAAS;AAEnC,kBAAU,IAAI;AACd,0BAAkB,UAAU,UAAU,SAAS,CAAC;AAAA,MAClD;AACA,UAAI,UAAU,gBAAgB;AAC9B,UAAI,mBAAmB,gBAAgB;AACvC,UAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,gBAAQ,KAAK,GAAG;AAAA,MAClB,WAAW,qBAAqB,MAAM,SAAS,GAAG;AAChD,YAAI,MAAM,MAAM,IAAI;AACpB,gBAAQ,GAAG,IAAI;AAAA,MACjB,OAAO;AACL,cAAM,KAAK,GAAG;AAAA,MAChB;AAAA,IACF;AAEA,YAAQ,QAAQ,SAAU,KAAK;AAC7B,UAAI,QAAQ,CAAC;AACb,UAAI,YAAY,CAAC;AACjB,UAAI,IAAI;AACR,UAAIC,iBAAe,WAAU;AAC7B,UAAI,cAAa,QAAO,uBAAsB;AAC9C,UAAI,cAAc;AAClB,aAAO,MAAM;AACX,QAAAA,kBAAiB,IAAI,GAAG;AACxB,YAAIA,oBAAmB,OACnBA,oBAAmB,OACnB,OAAOA,oBAAmB,aAAa;AACzC,cAAI,MAAM,WAAW,GAAG;AACtB,mBAAO,MAAM,IAAI;AAAA,UACnB,OAAO;AACL,YAAAD,KAAI,MAAM,IAAI,GAAG,OAAO,SAAS;AACjC;AAAA,UACF;AAAA,QACF;AACA,gBAAQC,iBAAgB;AAAA,UACtB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH;AAAA,UACF,KAAK;AACH,iBAAK;AACL,YAAAD,KAAI,MAAM,OAAO,SAAS;AAC1B;AAAA,UACF,KAAK;AACH,iBAAK;AACL,YAAAA,KAAI,MAAM,OAAO,SAAS;AAC1B;AAAA,UACF,KAAK;AACH,iBAAK;AACL,YAAAA,KAAI,OAAO,OAAO,SAAS;AAC3B;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,wBAAY;AACZ;AACA,mBAAO,MAAM;AACX,wBAAU,IAAI,GAAG;AACjB,kBAAI,cAAc,KAAK,OAAO,GAAG;AAC/B,6BAAa;AAAA,cACf,OAAO;AACL;AACA;AAAA,cACF;AAAA,YACF;AACA,YAAAA,KAAI,WAAW,SAAS,GAAG,OAAO,SAAS;AAC3C;AAAA,UACF,KAAK;AACH,2BAAe;AACf,qBAAS;AACT,oCAAwB;AACxB,mBAAO,MAAM;AACX,mBAAK,IAAI,GAAG;AACZ,kBAAI,OAAO,OAAQ,WAAW,QAC1B,wBAAwB,MAAM,GAAI;AACpC,gCAAgB;AAChB,yBAAS;AACT,oBAAI,WAAW,MAAM;AACnB;AAAA,gBACF,OAAO;AACL,0CAAwB;AAAA,gBAC1B;AAAA,cACF,OAAO;AACL;AAAA,cACF;AAAA,YACF;AACA,YAAAA,KAAI,KAAK,MAAM,MAAM,eAAe,GAAG,GAAG,OAAO,SAAS;AAC1D;AAAA,UACF,KAAK;AACH,2BAAe,EAAE,SAAS,CAAC,GAAG,OAAO,MAAM,OAAO;AAClD,kBAAM,KAAK,aAAa,OAAO;AAC/B,sBAAU,KAAK,YAAY;AAC3B;AAAA,UACF,KAAK;AACH,yBAAa,EAAE,SAAS,CAAC,GAAG,OAAO,MAAM,OAAO;AAChD,kBAAM,KAAK,WAAW,OAAO;AAC7B,sBAAU,KAAK,UAAU;AACzB;AAAA,UACF;AACE,kBAAM,IAAI;AAAA,cACR,wCAAwCC;AAAA,YAAc;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC5KA,uBAAgB;AAEhB,sBAAqB;AACrB,oBAAe;AAEf,SAAS,eAAe,QAAQ;AAC9B,SAAQ,OAAO,gBAAgB,eAAe,kBAAkB,eAC7D,OAAO,SAAS,eAAe,kBAAkB;AACtD;AAOA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,kBAAkB,cACrB,OAAO,MAAM,CAAC,IACd,OAAO,MAAM,GAAG,OAAO,MAAM,OAAO,IAAI;AAC9C;AAMA,IAAI,eAAe,SAAS,UAAU;AACtC,IAAI,mBAAmB,aAAa,KAAK,MAAM;AAE/C,SAAS,cAAc,OAAO;AAC5B,MAAI,QAAQ,OAAO,eAAe,KAAK;AAEvC,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM;AACjB,SAAQ,OAAO,QAAQ,cACrB,gBAAgB,QAAQ,aAAa,KAAK,IAAI,KAAK;AACvD;AAEA,SAAS,MAAM,QAAQ;AACrB,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,gBAAY,CAAC;AACb,SAAK,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAU,CAAC,IAAI,MAAM,OAAO,CAAC,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAIA,MAAI,kBAAkB,QAAQ,SAAS,MAAM,GAAG;AAC9C,WAAO,OAAO,YAAY;AAAA,EAC5B;AAEA,MAAI,eAAe,MAAM,GAAG;AAC1B,WAAO,kBAAkB,MAAM;AAAA,EACjC;AAEA,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,cAAY,CAAC;AACb,OAAK,KAAK,QAAQ;AAEhB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,CAAC,GAAG;AACnD,UAAI,QAAQ,MAAM,OAAO,CAAC,CAAC;AAC3B,UAAI,OAAO,UAAU,aAAa;AAChC,kBAAU,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,KAAK,KAAK;AACjB,MAAI,SAAS;AACb,SAAO,YAAa,MAAM;AAExB,QAAI,QAAQ;AAEV,YAAM,IAAI,MAAM,4BAA4B;AAAA,IAC9C,OAAO;AACL,eAAS;AACT,UAAI,MAAM,MAAM,IAAI;AAAA,IACtB;AAAA,EACF;AACF;AAEA,SAAS,UAAU,MAAM;AAEvB,SAAO,YAAa,MAAM;AAExB,WAAO,MAAM,IAAI;AACjB,QAAIC,QAAO;AAEX,QAAI,SAAU,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,aAAc,KAAK,IAAI,IAAI;AAC1E,QAAI,UAAU,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACnD,UAAI;AACJ,UAAI;AACF,YAAI,WAAW,KAAK,SAAU,KAAK,MAAM;AACvC,cAAI,KAAK;AACP,mBAAO,GAAG;AAAA,UACZ,OAAO;AACL,oBAAQ,IAAI;AAAA,UACd;AAAA,QACF,CAAC;AAGD,aAAK,KAAK,QAAQ;AAClB,eAAO,KAAK,MAAMA,OAAM,IAAI;AAC5B,YAAI,QAAQ,OAAO,KAAK,SAAS,YAAY;AAC3C,kBAAQ,IAAI;AAAA,QACd;AAAA,MACF,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF,CAAC;AAED,QAAI,QAAQ;AACV,cAAQ,KAAK,SAAU,QAAQ;AAC7B,eAAO,MAAM,MAAM;AAAA,MACrB,GAAG,MAAM;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,WAAWA,OAAM,MAAM,MAAM;AAEpC,MAAIA,MAAK,YAAY,UAAU,OAAO,EAAE,QAAQ;AAC9C,QAAI,UAAU,CAAC,OAAOA,MAAK,MAAM,IAAI;AACrC,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,cAAQ,KAAK,KAAK,CAAC,CAAC;AAAA,IACtB;AACA,IAAAA,MAAK,YAAY,KAAK,SAAS,OAAO;AAGtC,QAAI,eAAe,KAAK,KAAK,SAAS,CAAC;AACvC,SAAK,KAAK,SAAS,CAAC,IAAI,SAAU,KAAK,KAAK;AAC1C,UAAI,eAAe,CAAC,OAAOA,MAAK,MAAM,IAAI;AAC1C,qBAAe,aAAa;AAAA,QAC1B,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,GAAG;AAAA,MACxC;AACA,MAAAA,MAAK,YAAY,KAAK,SAAS,YAAY;AAC3C,mBAAa,KAAK,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAEA,SAAS,WAAW,MAAM,UAAU;AAClC,SAAO,UAAU,YAAa,MAAM;AAClC,QAAI,KAAK,SAAS;AAChB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACvD;AACA,QAAI,KAAK,YAAY;AACnB,aAAO,QAAQ,OAAO,IAAI,MAAM,uBAAuB,CAAC;AAAA,IAC1D;AACA,QAAIA,QAAO;AACX,eAAWA,OAAM,MAAM,IAAI;AAC3B,QAAI,CAAC,KAAK,UAAU,SAAS;AAC3B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAAA,MAAK,UAAU,QAAQ,SAAU,QAAQ;AACvC,cAAI,QAAQ;AACV,mBAAO,MAAM;AAAA,UACf,OAAO;AACL,oBAAQA,MAAK,IAAI,EAAE,MAAMA,OAAM,IAAI,CAAC;AAAA,UACtC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO,SAAS,MAAM,MAAM,IAAI;AAAA,EAClC,CAAC;AACH;AAGA,SAAS,KAAK,KAAK,KAAK;AACtB,MAAI,MAAM,CAAC;AACX,WAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,QAAI,OAAO,IAAI,CAAC;AAChB,QAAI,QAAQ,KAAK;AACf,UAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AAKA,IAAI,8BAA8B;AAElC,SAAS,iBAAiB,GAAG;AAC3B,SAAO;AACT;AAEA,SAAS,2BAA2B,QAAQ;AAC1C,SAAO,CAAC;AAAA,IACN,IAAI;AAAA,EACN,CAAC;AACH;AAGA,SAAS,QAAQ,IAAI,MAAM,UAAU;AACnC,MAAI,WAAW,KAAK;AAGpB,MAAI,eAAe,oBAAI,IAAI;AAC3B,WAAS,QAAQ,SAAU,SAAS;AAClC,QAAI,aAAa,IAAI,QAAQ,EAAE,GAAG;AAChC,mBAAa,IAAI,QAAQ,EAAE,EAAE,KAAK,OAAO;AAAA,IAC3C,OAAO;AACL,mBAAa,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC;AAAA,IACxC;AAAA,EACF,CAAC;AAED,MAAI,UAAU,aAAa;AAC3B,MAAI,UAAU;AACd,MAAI,gBAAgB,IAAI,MAAM,OAAO;AAErC,WAAS,2BAA2B;AAClC,QAAI,UAAU,CAAC;AACf,kBAAc,QAAQ,SAAU,KAAK;AACnC,UAAI,KAAK,QAAQ,SAAU,MAAM;AAC/B,gBAAQ,KAAK;AAAA,UACX,IAAI,IAAI;AAAA,UACR,MAAM,CAAC,IAAI;AAAA,QACb,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,aAAS,MAAM,EAAC,QAAO,CAAC;AAAA,EAC1B;AAEA,WAAS,YAAY;AACnB,QAAI,EAAE,YAAY,SAAS;AACzB,+BAAyB;AAAA,IAC3B;AAAA,EACF;AAEA,WAAS,UAAU,UAAU,IAAI,MAAM;AACrC,kBAAc,QAAQ,IAAI,EAAC,IAAI,KAAI;AACnC,cAAU;AAAA,EACZ;AAEA,MAAI,cAAc,CAAC;AACnB,eAAa,QAAQ,SAAU,OAAO,KAAK;AACzC,gBAAY,KAAK,GAAG;AAAA,EACtB,CAAC;AAED,MAAI,IAAI;AAER,WAAS,YAAY;AAEnB,QAAI,KAAK,YAAY,QAAQ;AAC3B;AAAA,IACF;AAEA,QAAI,OAAO,KAAK,IAAI,IAAI,6BAA6B,YAAY,MAAM;AACvE,QAAI,QAAQ,YAAY,MAAM,GAAG,IAAI;AACrC,iBAAa,OAAO,CAAC;AACrB,SAAK,MAAM;AAAA,EACb;AAEA,WAAS,aAAa,OAAO,QAAQ;AACnC,UAAM,QAAQ,SAAU,OAAO,GAAG;AAChC,UAAI,SAAS,SAAS;AACtB,UAAI,cAAc,aAAa,IAAI,KAAK;AAQxC,UAAI,UAAU,KAAK,YAAY,CAAC,GAAG,CAAC,cAAc,aAAa,CAAC;AAChE,cAAQ,YAAY,YAAY,IAAI,SAAU,SAAS;AAErD,eAAO,QAAQ;AAAA,MACjB,CAAC;AAGD,cAAQ,YAAY,QAAQ,UAAU,OAAO,gBAAgB;AAE7D,UAAI,eAAe;AAEnB,UAAI,QAAQ,UAAU,WAAW,GAAG;AAClC,eAAO,QAAQ;AAKf,uBAAe;AAAA,MACjB;AAGA,OAAC,QAAQ,eAAe,UAAU,QAAQ,QAAQ,EAAE,QAAQ,SAAU,OAAO;AAC3E,YAAI,SAAS,MAAM;AACjB,kBAAQ,KAAK,IAAI,KAAK,KAAK;AAAA,QAC7B;AAAA,MACF,CAAC;AACD,SAAG,IAAI,OAAO,SAAS,SAAU,KAAK,KAAK;AACzC,YAAI;AAEJ,YAAI,KAAK;AACP,mBAAS,CAAC,EAAC,OAAO,IAAG,CAAC;AAAA,QACxB,OAAO;AACL,mBAAS,aAAa,GAAG;AAAA,QAC3B;AACA,kBAAU,QAAQ,OAAO,MAAM;AAC/B,kBAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,YAAU;AAEZ;AAEA,IAAI;AAEJ,IAAI;AACF,eAAa,QAAQ,6BAA6B,CAAC;AACnD,aAAW,CAAC,CAAC,aAAa,QAAQ,2BAA2B;AAC/D,SAAS,GAAG;AACV,aAAW;AACb;AAEA,SAAS,kBAAkB;AACzB,SAAO;AACT;AAEA,IAAM,WAAW,OAAO,mBAAmB,aACvC,iBACA,SAASC,UAAS,IAAI;AACtB,UAAQ,QAAQ,EAAE,KAAK,EAAE;AAC3B;AAEF,IAAM,UAAN,cAAsB,cAAAC,QAAG;AAAA,EACvB,cAAc;AACZ,UAAM;AAEN,SAAK,aAAa,CAAC;AAEnB,QAAI,gBAAgB,GAAG;AACrB,uBAAiB,WAAW,CAAC,MAAM;AACjC,aAAK,KAAK,EAAE,GAAG;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,YAAY,QAAQ,IAAI,IAAI,MAAM;AAChC,QAAI,KAAK,WAAW,EAAE,GAAG;AACvB;AAAA,IACF;AACA,QAAI,aAAa;AACjB,QAAIF,QAAO;AACX,aAAS,gBAAgB;AACvB,UAAI,CAACA,MAAK,WAAW,EAAE,GAAG;AACxB;AAAA,MACF;AACA,UAAI,YAAY;AACd,qBAAa;AACb;AAAA,MACF;AACA,mBAAa;AACb,UAAI,cAAc,KAAK,MAAM;AAAA,QAC3B;AAAA,QAAS;AAAA,QAAgB;AAAA,QAAe;AAAA,QAAa;AAAA,QACrD;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAgB;AAAA,QAAU;AAAA,MACxD,CAAC;AAED,eAAS,UAAU;AACjB,qBAAa;AAAA,MACf;AAEA,SAAG,QAAQ,WAAW,EAAE,GAAG,UAAU,SAAU,GAAG;AAChD,YAAI,EAAE,MAAM,KAAK,SAAS,CAAC,KAAK,WAAW;AACzC,eAAK,QAAQ,EAAE;AACf,eAAK,SAAS,CAAC;AAAA,QACjB;AAAA,MACF,CAAC,EAAE,GAAG,YAAY,WAAY;AAC5B,YAAI,eAAe,WAAW;AAC5B,mBAAS,aAAa;AAAA,QACxB;AACA,qBAAa;AAAA,MACf,CAAC,EAAE,GAAG,SAAS,OAAO;AAAA,IACxB;AACA,SAAK,WAAW,EAAE,IAAI;AACtB,SAAK,GAAG,QAAQ,aAAa;AAAA,EAC/B;AAAA,EAEA,eAAe,QAAQ,IAAI;AACzB,QAAI,EAAE,MAAM,KAAK,aAAa;AAC5B;AAAA,IACF;AACA,UAAM,eAAe,QAAQ,KAAK,WAAW,EAAE,CAAC;AAChD,WAAO,KAAK,WAAW,EAAE;AAAA,EAC3B;AAAA,EAEA,mBAAmB,QAAQ;AAGzB,QAAI,gBAAgB,GAAG;AACrB,mBAAa,MAAM,IAAK,aAAa,MAAM,MAAM,MAAO,MAAM;AAAA,IAChE;AAAA,EACF;AAAA,EAEA,OAAO,QAAQ;AACb,SAAK,KAAK,MAAM;AAChB,SAAK,mBAAmB,MAAM;AAAA,EAChC;AACF;AAEA,SAAS,eAAe,QAAQ;AAE9B,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,MAAM,MAAM,YAAY;AAC3E,QAAI,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAClD,YAAQ,MAAM,EAAE,MAAM,SAAS,IAAI;AAAA,EACrC;AACF;AAEA,SAAS,aAAa,KAAK,KAAK;AAC9B,MAAI,aAAa;AACjB,QAAM,SAAS,KAAK,EAAE,KAAK;AAC3B,QAAM,SAAS,KAAK,EAAE;AACtB,MAAI,QAAQ,OAAO,OAAO,KAAK;AAC7B,WAAO,OAAO,MAAM;AAAA,EACtB,OAAO;AACL,UAAM,MAAM;AAAA,EACd;AAEA,MAAI,MAAM,YAAY;AACpB,UAAM,cAAc;AACpB,UAAM;AAAA,EACR;AACA,MAAI,QAAQ,KAAK,OAAO;AACxB,MAAI,QAAQ,MAAM;AAElB,SAAO,CAAC,EAAE,QAAQ,QAAQ;AAC5B;AAEA,SAAS,eAAe,KAAK;AAC3B,MAAI,MAAM;AACV,MAAI,CAAC,KAAK;AACR,UAAM;AAAA,EACR;AACA,SAAO,aAAa,KAAK,GAAG;AAC9B;AAIA,SAAS,aAAa,QAAQ,KAAK;AACjC,iBAAe,QAAQ,eAAe,SAAS,yBAAyB,GAAG;AAC7E;AAEA,IAAM,aAAN,cAAyB,MAAM;AAAA,EAC7B,YAAY,QAAQ,OAAO,QAAQ;AACjC,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,UAAU;AAAA,MACpB,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAEA,IAAI,eAAe,IAAI,WAAW,KAAK,gBAAgB,gCAAgC;AACvF,IAAI,oBAAoB,IAAI,WAAW,KAAK,eAAe,6BAA6B;AACxF,IAAI,cAAc,IAAI,WAAW,KAAK,aAAa,SAAS;AAC5D,IAAI,eAAe,IAAI,WAAW,KAAK,YAAY,0BAA0B;AAC7E,IAAI,aAAa,IAAI,WAAW,KAAK,eAAe,iCAAiC;AACrF,IAAI,aAAa,IAAI,WAAW,KAAK,cAAc,0BAA0B;AAC7E,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,uDAAuD;AAC5G,IAAI,WAAW,IAAI,WAAW,KAAK,uBAAuB,mBAAmB;AAC7E,IAAI,gBAAgB,IAAI,WAAW,KAAK,iBAAiB,uCAAuC;AAChG,IAAI,UAAU,IAAI,WAAW,KAAK,UAAU,gCAAgC;AAC5E,IAAI,kBAAkB,IAAI,WAAW,KAAK,mBAAmB,qBAAqB;AAClF,IAAI,oBAAoB,IAAI,WAAW,KAAK,qBAAqB,iCAAiC;AAClG,IAAI,iBAAiB,IAAI,WAAW,KAAK,kBAAkB,6BAA6B;AACxF,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,kCAAkC;AACvF,IAAI,gBAAgB,IAAI,WAAW,KAAK,eAAe,gCAAgC;AACvF,IAAI,aAAa,IAAI,WAAW,KAAK,aAAa,oBAAoB;AACtE,IAAI,YAAY,IAAI,WAAW,KAAK,uBAAuB,SAAS;AACpE,IAAI,YAAY,IAAI,WAAW,KAAK,oBAAoB,SAAS;AACjE,IAAI,YAAY,IAAI,WAAW,KAAK,yBAAyB,SAAS;AACtE,IAAI,YAAY,IAAI,WAAW,KAAK,aAAa,sDAAsD;AACvG,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,oBAAoB;AACzE,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,6DAA6D;AAClH,IAAI,eAAe,IAAI,WAAW,KAAK,gBAAgB,6CAA8C;AACrG,IAAI,cAAc,IAAI,WAAW,KAAK,eAAe,yBAAyB;AAE9E,SAAS,YAAY,OAAO,QAAQ;AAClC,WAAS,iBAAiBG,SAAQ;AAGhC,QAAI,QAAQ,OAAO,oBAAoB,KAAK;AAC5C,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,UAAI,OAAO,MAAM,MAAM,CAAC,CAAC,MAAM,YAAY;AACzC,aAAK,MAAM,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC,CAAC;AAAA,MACjC;AAAA,IACF;AAEA,QAAI,KAAK,UAAU,QAAW;AAC5B,WAAK,QAAS,IAAI,MAAM,EAAG;AAAA,IAC7B;AAEA,QAAIA,YAAW,QAAW;AACxB,WAAK,SAASA;AAAA,IAChB;AAAA,EACF;AACA,mBAAiB,YAAY,WAAW;AACxC,SAAO,IAAI,iBAAiB,MAAM;AACpC;AAEA,SAAS,0BAA0B,KAAK;AAEtC,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,OAAO;AACX,UAAM;AACN,QAAI,OAAO;AAAA,EACb;AAEA,MAAI,WAAW,OAAO,IAAI,UAAU,YAAY;AAC9C,QAAI,OAAO;AACX,QAAI,SAAS;AAAA,EACf;AAEA,MAAI,EAAE,UAAU,MAAM;AACpB,QAAI,OAAO,IAAI,SAAS;AAAA,EAC1B;AAEA,MAAI,EAAE,YAAY,MAAM;AACtB,QAAI,SAAS;AAAA,EACf;AAEA,MAAI,EAAE,aAAa,MAAM;AACvB,QAAI,UAAU,IAAI,WAAW,IAAI;AAAA,EACnC;AAEA,MAAI,EAAE,WAAW,MAAM;AACrB,QAAI,QAAS,IAAI,MAAM,EAAG;AAAA,EAC5B;AAEA,SAAO;AACT;AAEA,SAAS,UAAUC,SAAQ,KAAK,KAAK;AACnC,MAAI;AACF,WAAO,CAACA,QAAO,KAAK,GAAG;AAAA,EACzB,SAAS,KAAK;AACZ,QAAI,MAAM,4BAA4B,IAAI,SAAS;AACnD,WAAO,YAAY,aAAa,GAAG;AAAA,EACrC;AACF;AAEA,SAAS,aAAa,MAAM;AAC1B,MAAI,MAAM,CAAC;AACX,MAAI,YAAY,KAAK,UAAU,OAAO,KAAK,WAAW;AACtD,MAAI,QAAQ,KAAK;AAEjB,SAAO,SAASA,QAAO,QAAQ;AAC7B,QAAI,CAAC,OAAO,KAAK;AAGf,aAAO,MAAM,CAAC;AAAA,IAChB;AAEA,QAAI,eAAe,aAAa,UAAU,KAAK,QAAQ,OAAO,KAAK,GAAG;AAEtE,QAAI,OAAO,iBAAiB,UAAU;AACpC,aAAO;AAAA,IACT;AAEA,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,KAAK,cAAc;AACtB,aAAO,OAAO;AAAA,IAChB,WAAW,CAAC,KAAK,aAAa;AAC5B,eAAS,OAAO,OAAO,IAAI,cAAc;AAEvC,YAAI,OAAO,UAAU,eAAe,KAAK,OAAO,IAAI,cAAc,GAAG,GAAG;AACtE,iBAAO,IAAI,aAAa,GAAG,EAAE,OAAO;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AASA,SAAS,eAAe,IAAI;AAC1B,MAAI;AACJ,MAAI,CAAC,IAAI;AACP,UAAM,YAAY,UAAU;AAAA,EAC9B,WAAW,OAAO,OAAO,UAAU;AACjC,UAAM,YAAY,UAAU;AAAA,EAC9B,WAAW,KAAK,KAAK,EAAE,KAAK,CAAE,mBAAoB,KAAK,EAAE,GAAG;AAC1D,UAAM,YAAY,WAAW;AAAA,EAC/B;AACA,MAAI,KAAK;AACP,UAAM;AAAA,EACR;AACF;AAIA,SAAS,SAAS,IAAI;AACpB,MAAI,OAAO,GAAG,YAAY,WAAW;AACnC,WAAO,GAAG;AAAA,EACZ;AAEA,MAAI,OAAO,GAAG,SAAS,YAAY;AACjC;AAAA,MAAe;AAAA,MACb;AAAA,IAC6B;AAC/B,WAAO,GAAG,KAAK,MAAM;AAAA,EACvB;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,IAAI,MAAM;AAC/B,SAAO,mBAAmB,KAAK,GAAG,cAAc,IAAI,IACrB,cAAAF,QAAG,cAAc,IAAI,IAAI;AAC1D;AAEA,SAAS,2BAA2B,GAAG;AACrC,MAAI,CAAC,GAAG;AACN,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,EAAE,MAAM,GAAG;AACvB,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACA,SAAO;AACT;AAEA,SAAS,+BAA+B,GAAG;AACzC,MAAI,aAAa,2BAA2B,CAAC;AAC7C,SAAO,aAAa,WAAW,KAAK,GAAG,IAAI;AAC7C;AAKA,IAAI,OAAO;AAAA,EAAC;AAAA,EAAU;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAQ;AAAA,EAC/D;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAS;AAAQ;AAC9E,IAAI,QAAO;AACX,IAAI,UAAU;AAId,IAAI,SAAS;AAEb,SAAS,SAAS,KAAK;AACrB,MAAI,IAAI,OAAO,KAAK,GAAG;AACvB,MAAI,MAAM,CAAC;AACX,MAAI,IAAI;AAER,SAAO,KAAK;AACV,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,QAAQ,EAAE,CAAC,KAAK;AACpB,QAAI,UAAU,CAAC,QAAQ,UAAU,EAAE,QAAQ,GAAG,MAAM;AACpD,QAAI,GAAG,IAAI,UAAU,mBAAmB,KAAK,IAAI;AAAA,EACnD;AAEA,MAAI,KAAK,IAAI,CAAC;AACd,MAAI,KAAK,EAAE,CAAC,EAAE,QAAQ,SAAS,SAAU,IAAI,IAAI,IAAI;AACnD,QAAI,IAAI;AACN,UAAI,KAAK,EAAE,EAAE,IAAI;AAAA,IACnB;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAMA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAIG,QAAO,CAAC;AACZ,MAAI,SAAS,CAAC;AACd,WAAS,OAAO,OAAO;AACrB,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AACpD,MAAAA,MAAK,KAAK,GAAG;AACb,aAAO,KAAK,MAAM,GAAG,CAAC;AAAA,IACxB;AAAA,EACF;AACA,EAAAA,MAAK,KAAK,MAAM;AAChB,SAAO,SAAS,MAAM,MAAMA,KAAI,EAAE,MAAM,MAAM,MAAM;AACtD;AAKA,SAAS,OAAO,IAAI,OAAO,SAAS;AAClC,SAAO,GAAG,IAAI,KAAK,EAChB,MAAM,SAAU,KAAK;AAEpB,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM;AAAA,IACR;AACA,WAAO,CAAC;AAAA,EACV,CAAC,EACA,KAAK,SAAU,KAAK;AAEnB,QAAI,SAAS,IAAI;AACjB,QAAI,SAAS,QAAQ,GAAG;AAExB,QAAI,CAAC,QAAQ;AAGX,aAAO,EAAC,SAAS,OAAO,KAAK,OAAM;AAAA,IACrC;AAIA,WAAO,MAAM;AACb,WAAO,OAAO;AACd,WAAO,UAAU,IAAI,QAAQ,OAAO;AAAA,EACtC,CAAC;AACL;AAEA,SAAS,UAAU,IAAI,KAAK,SAAS;AACnC,SAAO,GAAG,IAAI,GAAG,EAAE,KAAK,SAAU,KAAK;AACrC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,KAAK,IAAI;AAAA,IACX;AAAA,EACF,GAAG,SAAU,KAAK;AAEhB,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM;AAAA,IACR;AACA,WAAO,OAAO,IAAI,IAAI,KAAK,OAAO;AAAA,EACpC,CAAC;AACH;AAEA,IAAI,WAAW,SAAU,KAAK;AAC5B,SAAO,KAAK,GAAG;AACjB;AAEA,IAAI,WAAW,SAAU,KAAK;AAC5B,SAAO,KAAK,GAAG;AACjB;AAKA,SAAS,WAAW,OAAO,YAAY;AAErC,UAAQ,SAAS,CAAC;AAClB,eAAa,cAAc,CAAC;AAC5B,MAAI;AACF,WAAO,IAAI,KAAK,OAAO,UAAU;AAAA,EACnC,SAAS,GAAG;AACV,QAAI,EAAE,SAAS,aAAa;AAC1B,YAAM;AAAA,IACR;AACA,QAAI,UAAU,OAAO,gBAAgB,cAAc,cACrC,OAAO,kBAAkB,cAAc,gBACvC,OAAO,mBAAmB,cAAc,iBACxC;AACd,QAAI,UAAU,IAAI,QAAQ;AAC1B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,cAAQ,OAAO,MAAM,CAAC,CAAC;AAAA,IACzB;AACA,WAAO,QAAQ,QAAQ,WAAW,IAAI;AAAA,EACxC;AACF;AAIA,SAAS,0BAA0B,KAAK;AACtC,MAAI,SAAS,IAAI;AACjB,MAAI,MAAM,IAAI,YAAY,MAAM;AAChC,MAAI,MAAM,IAAI,WAAW,GAAG;AAC5B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,QAAI,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,EAC3B;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,WAAW,MAAM;AAC3C,SAAO,WAAW,CAAC,0BAA0B,SAAS,CAAC,GAAG,EAAC,KAAI,CAAC;AAClE;AAEA,SAAS,aAAa,KAAK,MAAM;AAC/B,SAAO,mBAAmB,SAAS,GAAG,GAAG,IAAI;AAC/C;AAKA,SAAS,0BAA0B,QAAQ;AACzC,MAAI,SAAS;AACb,MAAI,QAAQ,IAAI,WAAW,MAAM;AACjC,MAAI,SAAS,MAAM;AACnB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,OAAO,aAAa,MAAM,CAAC,CAAC;AAAA,EACxC;AACA,SAAO;AACT;AAGA,SAAS,mBAAmB,MAAM,UAAU;AAC1C,MAAI,SAAS,IAAI,WAAW;AAC5B,MAAI,kBAAkB,OAAO,OAAO,uBAAuB;AAC3D,SAAO,YAAY,SAAU,GAAG;AAC9B,QAAI,SAAS,EAAE,OAAO,UAAU;AAChC,QAAI,iBAAiB;AACnB,aAAO,SAAS,MAAM;AAAA,IACxB;AACA,aAAS,0BAA0B,MAAM,CAAC;AAAA,EAC5C;AACA,MAAI,iBAAiB;AACnB,WAAO,mBAAmB,IAAI;AAAA,EAChC,OAAO;AACL,WAAO,kBAAkB,IAAI;AAAA,EAC/B;AACF;AAEA,SAAS,mBAAmB,cAAc,UAAU;AAClD,qBAAmB,cAAc,SAAU,KAAK;AAC9C,aAAS,GAAG;AAAA,EACd,CAAC;AACH;AAEA,SAAS,aAAa,cAAc,UAAU;AAC5C,qBAAmB,cAAc,SAAU,QAAQ;AACjD,aAAS,SAAS,MAAM,CAAC;AAAA,EAC3B,CAAC;AACH;AAGA,SAAS,kBAAkB,MAAM,UAAU;AACzC,MAAI,SAAS,IAAI,WAAW;AAC5B,SAAO,YAAY,SAAU,GAAG;AAC9B,QAAI,SAAS,EAAE,OAAO,UAAU,IAAI,YAAY,CAAC;AACjD,aAAS,MAAM;AAAA,EACjB;AACA,SAAO,kBAAkB,IAAI;AAC/B;AAIA,IAAI,mBAAmB,KAAK,gBAAgB,KAAK;AACjD,IAAI,iBAAiB;AAErB,SAAS,YAAY,KAAK;AACxB,SAAO,SAAS,GAAG;AACrB;AAEA,SAAS,WAAW,QAAQ,MAAM,OAAO,KAAK,UAAU;AACtD,MAAI,QAAQ,KAAK,MAAM,KAAK,MAAM;AAEhC,WAAO,KAAK,MAAM,OAAO,GAAG;AAAA,EAC9B;AACA,oBAAkB,MAAM,SAAU,aAAa;AAC7C,WAAO,OAAO,WAAW;AACzB,aAAS;AAAA,EACX,CAAC;AACH;AAEA,SAAS,aAAa,QAAQ,QAAQ,OAAO,KAAK,UAAU;AAC1D,MAAI,QAAQ,KAAK,MAAM,OAAO,QAAQ;AAEpC,aAAS,OAAO,UAAU,OAAO,GAAG;AAAA,EACtC;AACA,SAAO,aAAa,MAAM;AAC1B,WAAS;AACX;AAEA,SAAS,UAAU,MAAM,UAAU;AACjC,MAAI,gBAAgB,OAAO,SAAS;AACpC,MAAI,MAAM,gBAAgB,KAAK,SAAS,KAAK;AAC7C,MAAI,YAAY,KAAK,IAAI,gBAAgB,GAAG;AAC5C,MAAI,SAAS,KAAK,KAAK,MAAM,SAAS;AACtC,MAAI,eAAe;AACnB,MAAI,SAAS,gBAAgB,IAAI,iBAAAC,QAAI,IAAI,IAAI,iBAAAA,QAAI,YAAY;AAE7D,MAAI,SAAS,gBAAgB,eAAe;AAE5C,WAAS,OAAO;AACd,qBAAiB,aAAa;AAAA,EAChC;AAEA,WAAS,OAAO;AACd,QAAI,MAAM,OAAO,IAAI,IAAI;AACzB,QAAI,SAAS,YAAY,GAAG;AAC5B,aAAS,MAAM;AACf,WAAO,QAAQ;AAAA,EACjB;AAEA,WAAS,gBAAgB;AACvB,QAAI,QAAQ,eAAe;AAC3B,QAAI,MAAM,QAAQ;AAClB;AACA,QAAI,eAAe,QAAQ;AACzB,aAAO,QAAQ,MAAM,OAAO,KAAK,IAAI;AAAA,IACvC,OAAO;AACL,aAAO,QAAQ,MAAM,OAAO,KAAK,IAAI;AAAA,IACvC;AAAA,EACF;AACA,gBAAc;AAChB;AAEA,SAAS,UAAU,QAAQ;AACzB,SAAO,iBAAAA,QAAI,KAAK,MAAM;AACxB;AAMA,SAAS,IAAI,KAAK,oBAAoB;AACpC,MAAI,CAAC,oBAAoB;AACvB,WAAO,WAAG,EAAE,QAAQ,MAAM,EAAE,EAAE,YAAY;AAAA,EAC5C;AAEA,MAAI,gBAAgB,OAAO,OAAO,CAAC,GAAG,GAAG;AACzC,SAAO,cAAc;AACrB,SAAO,UAAU,KAAK,UAAU,aAAa,CAAC;AAChD;AAEA,IAAI,OAAO;AAOX,SAAS,WAAW,UAAU;AAC5B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,SAAS,SAAS,MAAM;AACtC,MAAI;AACJ,SAAQ,OAAO,QAAQ,IAAI,GAAI;AAC7B,QAAI,OAAO,KAAK;AAChB,QAAI,WAAW,KAAK,CAAC;AACrB,QAAI,MAAM,KAAK;AACf,QAAI,SAAS,QAAQ;AACnB,eAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACnD,gBAAQ,KAAK,EAAC,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,EAAC,CAAC;AAAA,MAC/C;AACA;AAAA,IACF;AACA,QAAI,UAAU,CAAC,CAAC,KAAK,CAAC,EAAE;AACxB,QAAI,KAAK,KAAK,CAAC;AAEf,QAAI,CAAC,cAAc,mBAAmB,UAAU,iBAC5C,eAAe,MAAM,aAAa,MAAM,YAAY,KAAK;AAC3D,kBAAY;AACZ,mBAAa;AACb,uBAAiB;AAAA,IACnB;AAAA,EACF;AAEA,SAAO,aAAa,MAAM;AAC5B;AAMA,SAAS,gBAAgB,MAAM,UAAU;AACvC,MAAI,UAAU,KAAK,MAAM;AAEzB,MAAI;AACJ,SAAQ,OAAO,QAAQ,IAAI,GAAI;AAC7B,QAAI,MAAM,KAAK;AACf,QAAI,OAAO,KAAK;AAChB,QAAI,WAAW,KAAK,CAAC;AACrB,QAAI,SACF,SAAS,SAAS,WAAW,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC,CAAC;AACjE,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACnD,cAAQ,KAAK,EAAC,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,GAAG,KAAK,OAAM,CAAC;AAAA,IAC5D;AAAA,EACF;AACF;AAEA,SAAS,UAAU,GAAG,GAAG;AACvB,SAAO,EAAE,MAAM,EAAE;AACnB;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,SAAS,CAAC;AACd,kBAAgB,MAAM,SAAU,QAAQ,KAAK,IAAI,KAAK,MAAM;AAC1D,QAAI,QAAQ;AACV,aAAO,KAAK,EAAC,KAAK,MAAM,MAAM,IAAI,KAAK,KAAI,CAAC;AAAA,IAC9C;AAAA,EACF,CAAC;AACD,SAAO,KAAK,SAAS,EAAE,QAAQ;AAC/B,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,WAAO,OAAO,CAAC,EAAE;AAAA,EACnB;AACA,SAAO;AACT;AAKA,SAAS,iBAAiB,UAAU;AAClC,MAAI,MAAM,WAAW,QAAQ;AAC7B,MAAI,SAAS,cAAc,SAAS,QAAQ;AAC5C,MAAI,YAAY,CAAC;AACjB,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,QAAI,OAAO,OAAO,CAAC;AACnB,QAAI,KAAK,QAAQ,OAAO,CAAC,KAAK,KAAK,SAAS;AAC1C,gBAAU,KAAK,KAAK,GAAG;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;AAIA,SAAS,YAAY,UAAU;AAC7B,MAAI,OAAO,CAAC;AACZ,kBAAgB,SAAS,UAAU,SAAU,QAAQ,KACR,SAAS,KAAK,MAAM;AAC/D,QAAI,KAAK,WAAW,eAAe,CAAC,QAAQ;AAC1C,WAAK,KAAK,MAAM,MAAM,OAAO;AAC7B,WAAK,SAAS;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAkBA,SAAS,eAAe,MAAM,WAAW;AACvC,MAAI,OAAO,CAAC;AACZ,QAAM,UAAU,KAAK,MAAM;AAE3B,MAAI;AACJ,SAAQ,OAAO,QAAQ,IAAI,GAAI;AAC7B,UAAM,EAAE,KAAK,KAAK,KAAK,IAAI;AAC3B,UAAMC,OAAM,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC;AAC7B,UAAM,WAAW,KAAK,CAAC;AAGvB,SAAK,KAAKA,IAAG;AAGb,QAAIA,SAAQ,WAAW;AAErB,UAAI,SAAS,WAAW,GAAG;AACzB,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AACA,aAAO,KAAK,QAAQ;AAAA,IACtB;AAKA,QAAI,SAAS,WAAW,KAAK,SAAS,SAAS,GAAG;AAChD,aAAO,CAAC;AAAA,IACV;AAGA,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACnD,cAAQ,KAAK,EAAE,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,CAAC;AAAA,IACjD;AAAA,EACF;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,IAAI,MAAM,uCAAuC;AAAA,EACzD;AACA,SAAO,KAAK,QAAQ;AACtB;AAGA,SAAS,WAAW,MAAM;AACxB,MAAI,QAAQ,CAAC;AACb,MAAI,UAAU,KAAK,MAAM;AACzB,MAAI;AACJ,SAAQ,OAAO,QAAQ,IAAI,GAAI;AAC7B,QAAI,MAAM,KAAK;AACf,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,KAAK,CAAC;AACf,QAAI,OAAO,KAAK,CAAC;AACjB,QAAI,WAAW,KAAK,CAAC;AACrB,QAAI,SAAS,SAAS,WAAW;AAEjC,QAAI,UAAU,KAAK,UAAU,KAAK,QAAQ,MAAM,IAAI,CAAC;AACrD,YAAQ,KAAK,EAAC,IAAI,KAAI,CAAC;AACvB,QAAI,QAAQ;AACV,YAAM,KAAK,EAAC,KAAM,MAAM,IAAI,QAAQ,QAAS,KAAK,QAAO,CAAC;AAAA,IAC5D;AACA,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACnD,cAAQ,KAAK,EAAC,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,GAAG,QAAO,CAAC;AAAA,IACxD;AAAA,EACF;AACA,SAAO,MAAM,QAAQ;AACvB;AAIA,SAAS,YAAY,GAAG,GAAG;AACzB,SAAO,EAAE,MAAM,EAAE;AACnB;AAGA,SAAS,aAAa,KAAK,MAAM,YAAY;AAC3C,MAAI,MAAM;AACV,MAAI,OAAO,IAAI;AACf,MAAI;AACJ,SAAO,MAAM,MAAM;AACjB,UAAO,MAAM,SAAU;AACvB,QAAI,WAAW,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG;AAClC,YAAM,MAAM;AAAA,IACd,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,aAAa,KAAK,MAAM,YAAY;AAC3C,MAAI,MAAM,aAAa,KAAK,MAAM,UAAU;AAC5C,MAAI,OAAO,KAAK,GAAG,IAAI;AACzB;AAKA,SAAS,WAAW,MAAM,YAAY;AACpC,MAAI;AACJ,MAAI;AACJ,WAAS,IAAI,YAAY,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AACxD,QAAI,OAAO,KAAK,CAAC;AACjB,QAAI,cAAc,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC,CAAC;AACzC,QAAI,MAAM;AACR,WAAK,CAAC,EAAE,KAAK,WAAW;AACxB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,YAAY,GAAG,GAAG;AACzB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK;AAC5B;AAIA,SAAS,UAAU,UAAU,UAAU;AACrC,MAAIC,SAAQ,CAAC,EAAC,OAAO,UAAU,OAAO,SAAQ,CAAC;AAC/C,MAAI,YAAY;AAChB,SAAOA,OAAM,SAAS,GAAG;AACvB,QAAI,OAAOA,OAAM,IAAI;AACrB,QAAI,QAAQ,KAAK;AACjB,QAAI,QAAQ,KAAK;AAEjB,QAAI,MAAM,CAAC,EAAE,UAAU,MAAM,CAAC,EAAE,QAAQ;AACtC,YAAM,CAAC,EAAE,SACN,MAAM,CAAC,EAAE,WAAY,eACtB,MAAM,CAAC,EAAE,WAAW,cAAe,cAAc;AAAA,IACrD;AAEA,aAAS,IAAI,GAAG,IAAI,MAAM,CAAC,EAAE,QAAQ,KAAK;AACxC,UAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG;AAChB,oBAAY;AACZ,cAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC;AACxB;AAAA,MACF;AAEA,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,MAAM,CAAC,EAAE,QAAQ,KAAK;AACxC,YAAI,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG;AACrC,UAAAA,OAAM,KAAK,EAAC,OAAO,MAAM,CAAC,EAAE,CAAC,GAAG,OAAO,MAAM,CAAC,EAAE,CAAC,EAAC,CAAC;AACnD,mBAAS;AAAA,QACX;AAAA,MACF;AACA,UAAI,CAAC,QAAQ;AACX,oBAAY;AACZ,qBAAa,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,GAAG,WAAW;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,SAAO,EAAC,WAAW,MAAM,SAAQ;AACnC;AAEA,SAAS,QAAQ,MAAM,MAAM,YAAY;AACvC,MAAI,UAAU,CAAC;AACf,MAAI,YAAY;AAChB,MAAI,SAAS;AACb,MAAI;AAEJ,MAAI,CAAC,KAAK,QAAQ;AAChB,WAAO,EAAC,MAAM,CAAC,IAAI,GAAG,WAAW,WAAU;AAAA,EAC7C;AAEA,WAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,QAAI,SAAS,KAAK,CAAC;AACnB,QAAI,OAAO,QAAQ,KAAK,OAAO,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG;AAG5D,YAAM,UAAU,OAAO,KAAK,KAAK,GAAG;AACpC,cAAQ,KAAK,EAAC,KAAK,OAAO,KAAK,KAAK,IAAI,KAAI,CAAC;AAC7C,kBAAY,aAAa,IAAI;AAC7B,eAAS;AAAA,IACX,WAAW,eAAe,MAAM;AAM9B,UAAI,KAAK,OAAO,MAAM,KAAK,MAAM,SAAS;AAC1C,UAAI,KAAK,OAAO,MAAM,KAAK,MAAM,OAAO;AACxC,UAAI,OAAO,GAAG,MAAM,GAAG;AAEvB,UAAI,mBAAmB,CAAC;AAExB,UAAI,QAAQ,CAAC;AACb,YAAM,KAAK,EAAC,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM,WAAW,KAAI,CAAC;AAC7D,aAAO,MAAM,SAAS,GAAG;AACvB,YAAI,OAAO,MAAM,IAAI;AACrB,YAAI,KAAK,SAAS,GAAG;AACnB,cAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG;AAC7B,6BAAiB,KAAK,IAAI;AAAA,UAC5B;AACA;AAAA,QACF;AACA,YAAI,WAAW,KAAK,IAAI,CAAC;AACzB,iBAAS,IAAI,GAAG,cAAc,SAAS,QAAQ,IAAI,aAAa,KAAK;AACnE,gBAAM,KAAK;AAAA,YACT,KAAK,SAAS,CAAC;AAAA,YACf,MAAM,KAAK,OAAO;AAAA,YAClB,QAAQ,KAAK;AAAA,YACb,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAEA,UAAI,KAAK,iBAAiB,CAAC;AAE3B,UAAI,CAAC,IAAI;AACP,gBAAQ,KAAK,MAAM;AAAA,MACrB,OAAO;AACL,cAAM,UAAU,GAAG,KAAK,GAAG,GAAG;AAC9B,WAAG,OAAO,CAAC,EAAE,GAAG,SAAS,IAAI,IAAI;AACjC,gBAAQ,KAAK,EAAC,KAAK,GAAG,KAAK,KAAK,GAAG,IAAG,CAAC;AACvC,oBAAY,aAAa,IAAI;AAC7B,iBAAS;AAAA,MACX;AAAA,IACF,OAAO;AACL,cAAQ,KAAK,MAAM;AAAA,IACrB;AAAA,EACF;AAGA,MAAI,CAAC,QAAQ;AACX,YAAQ,KAAK,IAAI;AAAA,EACnB;AAEA,UAAQ,KAAK,WAAW;AAExB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW,aAAa;AAAA,EAC1B;AACF;AAGA,SAAS,KAAK,MAAM,OAAO;AAEzB,MAAI,QAAQ,WAAW,IAAI;AAC3B,MAAI;AAEJ,MAAI;AACJ,WAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAGhD,QAAI,OAAO,MAAM,CAAC;AAClB,QAAI,UAAU,KAAK;AACnB,QAAI;AACJ,QAAI,QAAQ,SAAS,OAAO;AAE1B,UAAI,CAAC,aAAa;AAChB,sBAAc,CAAC;AAAA,MACjB;AACA,UAAI,aAAa,QAAQ,SAAS;AAClC,aAAO;AAAA,QACL,KAAK,KAAK,MAAM;AAAA,QAChB,KAAK,WAAW,SAAS,UAAU;AAAA,MACrC;AAEA,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,YAAID,OAAO,KAAK,MAAM,IAAK,MAAM,QAAQ,CAAC,EAAE;AAC5C,oBAAYA,IAAG,IAAI;AAAA,MACrB;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,KAAK,KAAK;AAAA,QACV,KAAK,WAAW,SAAS,CAAC;AAAA,MAC5B;AAAA,IACF;AAIA,QAAI,QAAQ;AACV,eAAS,QAAQ,QAAQ,MAAM,IAAI,EAAE;AAAA,IACvC,OAAO;AACL,eAAS,CAAC,IAAI;AAAA,IAChB;AAAA,EACF;AAGA,MAAI,aAAa;AACf,oBAAgB,QAAQ,SAAU,QAAQ,KAAK,SAAS;AAEtD,aAAO,YAAY,MAAM,MAAM,OAAO;AAAA,IACxC,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM,cAAc,OAAO,KAAK,WAAW,IAAI,CAAC;AAAA,EAClD;AACF;AAEA,SAAS,MAAM,MAAM,MAAM,OAAO;AAChC,MAAI,UAAU,QAAQ,MAAM,IAAI;AAChC,MAAI,UAAU,KAAK,QAAQ,MAAM,KAAK;AACtC,SAAO;AAAA,IACL,MAAM,QAAQ;AAAA,IACd,aAAa,QAAQ;AAAA,IACrB,WAAW,QAAQ;AAAA,EACrB;AACF;AAGA,SAAS,UAAU,MAAMA,MAAK;AAC5B,MAAI,UAAU,KAAK,MAAM;AACzB,MAAI,WAAWA,KAAI,MAAM,GAAG;AAC5B,MAAI,YAAY,SAAS,SAAS,CAAC,GAAG,EAAE;AACxC,MAAI,WAAW,SAAS,CAAC;AAEzB,MAAI;AACJ,SAAQ,OAAO,QAAQ,IAAI,GAAI;AAC7B,QAAI,KAAK,QAAQ,aAAa,KAAK,IAAI,CAAC,MAAM,UAAU;AACtD,aAAO;AAAA,IACT;AACA,QAAI,WAAW,KAAK,IAAI,CAAC;AACzB,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACnD,cAAQ,KAAK,EAAC,KAAK,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,EAAC,CAAC;AAAA,IACpD;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,SAAS,MAAM;AACtB,SAAO,KAAK;AACd;AAKA,SAAS,UAAU,UAAUA,MAAK;AAChC,MAAI,CAACA,MAAK;AACR,IAAAA,OAAM,WAAW,QAAQ;AAAA,EAC3B;AACA,MAAI,KAAKA,KAAI,UAAUA,KAAI,QAAQ,GAAG,IAAI,CAAC;AAC3C,MAAI,UAAU,SAAS,SAAS,IAAI,QAAQ;AAE5C,MAAI;AACJ,SAAQ,OAAO,QAAQ,IAAI,GAAI;AAC7B,QAAI,KAAK,CAAC,MAAM,IAAI;AAClB,aAAO,CAAC,CAAC,KAAK,CAAC,EAAE;AAAA,IACnB;AACA,cAAU,QAAQ,OAAO,KAAK,CAAC,CAAC;AAAA,EAClC;AACF;AAEA,SAAS,UAAU,IAAI;AACrB,SAAO,OAAO,OAAO,YAAY,GAAG,WAAW,SAAS;AAC1D;AAGA,SAAS,OAAOA,MAAK,UAAU;AAC7B,MAAI,UAAU,SAAS,SAAS,MAAM;AACtC,MAAI;AACJ,SAAQ,OAAO,QAAQ,IAAI,GAAI;AAC7B,QAAI,MAAM,KAAK;AACf,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,KAAK,CAAC;AACf,QAAI,OAAO,KAAK,CAAC;AACjB,QAAI,WAAW,KAAK,CAAC;AACrB,QAAI,SAAS,SAAS,WAAW;AAEjC,QAAI,UAAU,KAAK,UAAU,KAAK,QAAQ,MAAM,IAAI,CAAC;AACrD,YAAQ,KAAK,EAAC,IAAI,KAAK,KAAI,CAAC;AAE5B,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,YAAI,cAAc,QAAQ,CAAC;AAC3B,YAAI,aAAa,YAAY,MAAM,MAAM,YAAY;AAErD,YAAI,eAAeA,MAAK;AAEtB,iBAAO,MAAM,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAEA,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,cAAQ,KAAK,EAAC,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,GAAG,QAAO,CAAC;AAAA,IACxD;AAAA,EACF;AAGA,QAAM,IAAI,MAAM,8CAA8C,SAAS,KAAK,WAAWA,IAAG;AAC5F;AAEA,SAAS,yBAAyBP,OAAM,QAAQ,SAAS,SAAS;AAEhE,MAAI;AACF,IAAAA,MAAK,KAAK,UAAU,QAAQ,SAAS,OAAO;AAAA,EAC9C,SAAS,GAAG;AACV,mBAAe,SAAS,qCAAqC,CAAC;AAAA,EAChE;AACF;AAEA,SAAS,cAAc,KAAK,UAAU,MAAM;AAC1C,MAAI,aAAa,CAAC,EAAC,KAAK,IAAI,KAAI,CAAC;AACjC,MAAI,KAAK,UAAU,YAAY;AAC7B,iBAAa,cAAc,SAAS,QAAQ,EAC3C,IAAI,SAAU,GAAG;AAAE,aAAO,EAAC,KAAK,EAAE,IAAG;AAAA,IAAG,CAAC;AAAA,EAC5C;AACA,MAAI,SAAS;AAAA,IACX,IAAI,SAAS;AAAA,IACb,SAAS;AAAA,IACT;AAAA,EACF;AAEA,MAAI,UAAU,UAAU,IAAI,IAAI,GAAG;AACjC,WAAO,UAAU;AAAA,EACnB;AACA,MAAI,KAAK,WAAW;AAClB,WAAO,IAAI,aAAa,iBAAiB,QAAQ;AACjD,QAAI,CAAC,OAAO,IAAI,WAAW,QAAQ;AACjC,aAAO,OAAO,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,YAAN,cAAwB,cAAAE,QAAG;AAAA,EACzB,YAAY,IAAI,MAAM,UAAU;AAC9B,UAAM;AACN,SAAK,KAAK;AACV,WAAO,OAAO,MAAM,IAAI,IAAI,CAAC;AAC7B,QAAI,WAAW,KAAK,WAAW,KAAK,CAAC,KAAK,SAAS;AACjD,UAAI,KAAK;AACP,YAAI,cAAc,MAAM,OAAO,IAAI,GAAG;AACpC,eAAK,KAAK,SAAS,GAAG;AAAA,QACxB;AAAA,MACF,OAAO;AACL,aAAK,KAAK,YAAY,IAAI;AAAA,MAC5B;AACA,WAAK,mBAAmB;AACxB,SAAG,eAAe,aAAa,SAAS;AAAA,IAC1C,CAAC;AACD,QAAI,UAAU;AACZ,WAAK,GAAG,YAAY,SAAU,MAAM;AAClC,iBAAS,MAAM,IAAI;AAAA,MACrB,CAAC;AACD,WAAK,GAAG,SAAS,QAAQ;AAAA,IAC3B;AACA,UAAM,YAAY,MAAM;AACtB,WAAK,OAAO;AAAA,IACd;AACA,OAAG,KAAK,aAAa,SAAS;AAE9B,SAAK,WAAW,CAAC,QAAQ,SAAS,YAAY;AAE5C,UAAI,KAAK,aAAa;AACpB;AAAA,MACF;AACA,+BAAyB,MAAM,QAAQ,SAAS,OAAO;AAAA,IACzD;AAEA,QAAI,UAAU,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACnD,WAAK,WAAW,SAAU,KAAK,KAAK;AAClC,YAAI,KAAK;AACP,iBAAO,GAAG;AAAA,QACZ,OAAO;AACL,kBAAQ,GAAG;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,KAAK,UAAU,WAAY;AAC9B,SAAG,eAAe,aAAa,SAAS;AACxC,WAAK,SAAS,MAAM,EAAC,QAAQ,YAAW,CAAC;AAAA,IAC3C,CAAC;AACD,SAAK,OAAO,QAAQ,KAAK,KAAK,OAAO;AACrC,SAAK,OAAO,IAAI,QAAQ,OAAO,EAAE,KAAK,OAAO;AAC7C,SAAK,KAAK,SAAU,QAAQ;AAC1B,eAAS,MAAM,MAAM;AAAA,IACvB,GAAG,QAAQ;AAIX,QAAI,CAAC,GAAG,UAAU,SAAS;AACzB,SAAG,UAAU,QAAQ,CAAC,WAAW;AAC/B,YAAI,QAAQ;AACV,eAAK,SAAS,MAAM;AAAA,QACtB,WAAW,KAAK,aAAa;AAC3B,eAAK,KAAK,QAAQ;AAAA,QACpB,OAAO;AACL,eAAK,gBAAgB,IAAI;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,WAAK,gBAAgB,IAAI;AAAA,IAC3B;AAAA,EACF;AAAA,EAEA,SAAS;AACP,SAAK,cAAc;AACnB,QAAI,KAAK,GAAG,UAAU,SAAS;AAC7B,WAAK,KAAK,QAAQ;AAAA,IACpB;AAAA,EACF;AAAA,EAEA,gBAAgB,MAAM;AACpB,QAAI,WAAW,KAAK;AAGpB,QAAI,QAAQ,sBAAsB;AAChC,cAAQ,qBAAqB,SAAS,MAAM,CAAC,QAAQ;AACnD,YAAI,KAAK;AACP,iBAAO,SAAS,GAAG;AAAA,QACrB;AACA,aAAK,UAAU,IAAI;AAAA,MACrB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,UAAU,IAAI;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,UAAU,MAAM;AACd,QAAI,WAAW,KAAK;AAEpB,WAAO,MAAM,IAAI;AACjB,QAAI,UAAU,QAAQ,EAAE,gBAAgB,OAAO;AAC7C,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,SAAK,gBAAgB;AAErB,QAAI,KAAK,UAAU,UAAU;AAC3B,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,KAAK,UAAU,OAAO;AACxB,WAAK,GAAG,KAAK,EAAE,KAAK,CAAC,SAAS;AAE5B,YAAI,KAAK,aAAa;AACpB,mBAAS,MAAM,EAAC,QAAQ,YAAW,CAAC;AACpC;AAAA,QACF;AACA,aAAK,QAAQ,KAAK;AAClB,aAAK,UAAU,IAAI;AAAA,MACrB,GAAG,QAAQ;AACX;AAAA,IACF;AAGA,QAAI,QAAQ,sBAAsB;AAChC,cAAQ,qBAAqB,UAAU,IAAI;AAC3C,UAAI,QAAQ,qBAAqB,aAAa,MAAM,IAAI,GAAG;AACzD,eAAO,QAAQ,qBAAqB,OAAO,MAAM,IAAI;AAAA,MACvD;AAAA,IACF,OAAO;AACL,OAAC,WAAW,UAAU,YAAY,MAAM,EAAE,QAAQ,SAAU,KAAK;AAC/D,YAAI,OAAO,MAAM;AACf;AAAA,YAAe;AAAA,YACb,UAAU,MAAM;AAAA,UAGlB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,EAAE,gBAAgB,OAAO;AAC3B,WAAK,aAAa;AAAA,IACpB;AAGA,SAAK,QAAQ,KAAK,UAAU,IAAI,IAAI,KAAK;AACzC,SAAK,WAAW;AAChB,QAAI,aAAa,KAAK,GAAG,SAAS,IAAI;AAEtC,QAAI,cAAc,OAAO,WAAW,WAAW,YAAY;AACzD,YAAM,SAAS,KAAK;AACpB,WAAK,SAAS,IAAI,SAAS;AACzB,mBAAW,OAAO;AAClB,eAAO,MAAM,MAAM,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACF;AAQA,SAAS,UAAU,UAAU,OAAO;AAClC,SAAO,SAAU,KAAK,SAAS;AAC7B,QAAI,OAAQ,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE,OAAQ;AAC3C,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,QAAQ;AACZ,eAAS,GAAG;AAAA,IACd,OAAO;AACL,eAAS,MAAM,QAAQ,SAAS,QAAQ,CAAC,IAAK,OAAO;AAAA,IACvD;AAAA,EACF;AACF;AAGA,SAAS,UAAU,MAAM;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,IAAI,UAAU;AAChB,aAAO,IAAI;AAAA,IACb,WAAW,IAAI,cAAc;AAE3B,UAAI,OAAO,OAAO,KAAK,IAAI,YAAY;AACvC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,aAAa,GAAG,IAAI;AAAA,UAAK,IAAI,aAAa,GAAG;AAAA,UAC/C,CAAC,QAAQ,UAAU,gBAAgB,UAAU,UAAU,MAAM;AAAA,QAAC;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AACF;AAGA,SAAS,mBAAmB,GAAG,GAAG;AAChC,MAAI,EAAE,QAAQ,EAAE,KAAK;AACnB,UAAM,SAAS,EAAE,aAAa,EAAE,WAAW,QAAQ;AACnD,UAAM,SAAS,EAAE,aAAa,EAAE,WAAW,QAAQ;AACnD,WAAO,SAAS;AAAA,EAClB;AACA,SAAO,EAAE,MAAM,EAAE,MAAM,KAAK;AAC9B;AAIA,SAAS,cAAc,MAAM;AAC3B,MAAI,SAAS,CAAC;AACd,MAAI,QAAQ,CAAC;AACb,kBAAgB,MAAM,SAAU,QAAQ,KAAK,IAAI,MAAM;AACrD,QAAI,SAAS,MAAM,MAAM;AACzB,QAAI,QAAQ;AACV,aAAO,MAAM,IAAI;AAAA,IACnB;AACA,QAAI,SAAS,QAAW;AACtB,YAAM,KAAK,EAAC,MAAM,MAAM,IAAI,OAAM,CAAC;AAAA,IACrC;AACA,WAAO;AAAA,EACT,CAAC;AAED,QAAM,QAAQ;AACd,QAAM,QAAQ,SAAU,MAAM;AAC5B,QAAI,OAAO,KAAK,IAAI,MAAM,QAAW;AACnC,aAAO,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,EAAE;AAAA,IACxC,OAAO;AACL,aAAO,KAAK,IAAI,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,GAAG,IAAI,OAAO,KAAK,EAAE,CAAC;AAAA,IACrE;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAIG,QAAS,WAAW,OACtB,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,QAAQ,KAAK,IAAI,IAChD,KAAK,OAAO,IAAK,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK;AACtD,OAAK,OAAOA;AACZ,OAAK,OAAO;AACZ,SAAO,KAAK;AACZ,MAAI,KAAK,YAAY;AACnB,IAAAA,MAAK,QAAQ;AACb,SAAK,aAAa;AAAA,EACpB;AACF;AAIA,SAAS,iBAAiBL,OAAM;AAC9B,MAAI,OAAOA,MAAK,iBAAiB,CAAC;AAClC,MAAI,OAAO,KAAK;AAChB,MAAI,WAAW,KAAK;AACpB,EAAAA,MAAK,IAAI,mBAAmB,EAAE,MAAM,WAAY;AAC9C,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,SAAU,KAAK;AACrB,QAAI,OAAO,IAAI,UAAU;AACvB,WAAK,WAAW,IAAI;AAAA,IACtB;AACA,IAAAA,MAAK,SAAS,MAAM,SAAU,KAAK,KAAK;AAEtC,UAAI,KAAK;AACP,iBAAS,GAAG;AAAA,MACd,OAAO;AACL,iBAAS,MAAM,GAAG;AAAA,MACpB;AACA,eAAS,WAAY;AACnB,QAAAA,MAAK,iBAAiB,MAAM;AAC5B,YAAIA,MAAK,iBAAiB,QAAQ;AAChC,2BAAiBA,KAAI;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,eAAe,IAAI,OAAO,QAAQ;AACzC,SAAO,GAAG,IAAI,eAAe,EAAE,KAAK,SAAU,KAAK;AACjD,UAAM,WAAW,IAAI,WAAW;AAChC,QAAI,OAAO,KAAK;AAAA,MACd;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACF,CAAC;AACD,QAAI,IAAI,OAAO,SAAS,KAAK,oBAAoB;AAC/C,UAAI,OAAO,OAAO,GAAG,IAAI,OAAO,SAAS,KAAK,kBAAkB;AAAA,IAClE;AACA,QAAI,WAAW;AACf,WAAO;AAAA,EACT,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM;AAAA,IACR;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL,QAAQ,CAAC;AAAA,QACP;AAAA,QACA,KAAK;AAAA,QACL,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,UAAU;AAAA,IACZ;AAAA,EACF,CAAC,EAAE,KAAK,SAAU,KAAK;AACrB,WAAO,GAAG,IAAI,GAAG;AAAA,EACnB,CAAC;AACH;AAEA,SAAS,oBAAoB,MAAM;AACjC,MAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,WAAO,OAAO;AAAA,EAEhB;AACA,SAAO;AACT;AAEA,SAAS,eAAe,KAAK;AAC3B,SAAO,QAAQ,QAAQ,OAAO,QAAQ,YAAY,MAAM,QAAQ,GAAG;AACrE;AAEA,IAAM,gBAAgB;AACtB,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,WAAW,YAAY,cAAc,KAAK,MAAM;AAChE;AAEA,IAAM,kBAAN,cAA8B,cAAAE,QAAG;AAAA,EAC/B,SAAS;AACP,SAAK,OAAO,WAAW,QAAQ,SAAU,KAAK,MAAM,UAAU;AAC5D,UAAI,OAAO,SAAS,YAAY;AAC9B,mBAAW;AACX,eAAO,CAAC;AAAA,MACV;AACA,UAAI,eAAe,GAAG,GAAG;AACvB,eAAO,SAAS,YAAY,aAAa,CAAC;AAAA,MAC5C;AACA,WAAK,SAAS,EAAC,MAAM,CAAC,GAAG,EAAC,GAAG,MAAM,UAAU,UAAU,IAAI,GAAG,CAAC;AAAA,IACjE,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,MAAM,WAAW,OAAO,SAAU,KAAK,MAAM,IAAI;AACpD,UAAI,OAAO,SAAS,YAAY;AAC9B,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AACA,UAAI,eAAe,GAAG,GAAG;AACvB,eAAO,GAAG,YAAY,aAAa,CAAC;AAAA,MACtC;AACA,qBAAe,IAAI,GAAG;AACtB,UAAI,UAAU,OAAO,CAAC,WAAW,IAAI,IAAI,GAAG;AAC1C,eAAO,GAAG,YAAY,WAAW,CAAC;AAAA,MACpC;AACA,UAAI,UAAU,IAAI,GAAG,KAAK,OAAO,KAAK,cAAc,YAAY;AAC9D,YAAI,IAAI,UAAU;AAChB,iBAAO,KAAK,aAAa,KAAK,EAAE;AAAA,QAClC,OAAO;AACL,iBAAO,KAAK,UAAU,KAAK,EAAE;AAAA,QAC/B;AAAA,MACF;AAEA,YAAM,SAAS,CAAC,SAAS;AACvB,YAAI,OAAO,KAAK,SAAS,cAAc,KAAK,cAAc,OAAO;AAC/D,eAAK,KAAK,KAAK,MAAM,IAAI;AAAA,QAC3B,OAAO;AACL,eAAK,SAAS,EAAC,MAAM,CAAC,GAAG,EAAC,GAAG,MAAM,UAAU,MAAM,IAAI,GAAG,CAAC;AAAA,QAC7D;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,IAAI,MAAM;AAC1B,6CAAqC;AACrC,eAAO,SAAU,KAAK;AACpB,cAAI,SAAS,MAAM,OAAO,EAAC,IAAI,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,KAAI;AAC/D,aAAG,KAAK,MAAM;AAAA,QAChB,CAAC;AAAA,MACH,OAAO;AACL,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,uCAAuC;AAC9C,YAAI,QAAQ,IAAI,KAAK,MAAM,GAAG;AAC9B,YAAI,WAAW,MAAM,CAAC;AACtB,YAAI,YAAY,SAAS,MAAM,CAAC,GAAG,EAAE;AAErC,YAAI,YAAY,YAAY;AAC5B,YAAI,WAAW,IAAI;AAEnB,YAAI,aAAa;AAAA,UACf,OAAO;AAAA,UACP,KAAK,CAAC,UAAU,QAAQ;AAAA,QAC1B;AACA,YAAI,OAAO,YAAY,MAAM;AAC7B,aAAK,YAAY;AAAA,MACnB;AAAA,IACF,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,gBAAgB,WAAW,iBAAiB,SAAU,OAAO,cAAc,QAAQ,MAAM,MAAM;AAClG,UAAI,MAAM;AACV,UAAI,OAAO,SAAS,YAAY;AAC9B,eAAO;AACP,eAAO;AACP,iBAAS;AAAA,MACX;AAGA,UAAI,OAAO,SAAS,aAAa;AAC/B,eAAO;AACP,eAAO;AACP,iBAAS;AAAA,MACX;AACA,UAAI,CAAC,MAAM;AACT,uBAAe,QAAQ,cAAc,cAAc,eAAe,OAAO,yBAAyB;AAAA,MACpG;AAEA,eAAS,iBAAiB,KAAK;AAC7B,YAAI,aAAa,UAAU,MAAM,SAAS,IAAI,MAAM,EAAE,IAAI;AAC1D,YAAI,eAAe,IAAI,gBAAgB,CAAC;AACxC,YAAI,aAAa,YAAY,IAAI;AAAA,UAC/B,cAAc;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,EAAE;AAAA,QACZ;AACA,eAAO,IAAI,IAAI,GAAG;AAAA,MACpB;AAEA,aAAO,IAAI,IAAI,KAAK,EAAE,KAAK,SAAU,KAAK;AACxC,YAAI,IAAI,SAAS,QAAQ;AACvB,gBAAM,YAAY,YAAY;AAAA,QAChC;AAEA,eAAO,iBAAiB,GAAG;AAAA,MAC7B,GAAG,SAAU,KAAK;AAGhB,YAAI,IAAI,WAAW,YAAY,SAAS;AACtC,iBAAO,iBAAiB,EAAC,KAAK,MAAK,CAAC;AAAA,QACtC,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,mBAAmB,WAAW,oBAAoB,SAAU,OAAO,cAAc,QAAQ,UAAU;AACtG,WAAK,IAAI,OAAO,CAAC,KAAK,QAAQ;AAE5B,YAAI,KAAK;AACP,mBAAS,GAAG;AACZ;AAAA,QACF;AACA,YAAI,IAAI,SAAS,QAAQ;AACvB,mBAAS,YAAY,YAAY,CAAC;AAClC;AAAA,QACF;AAEA,YAAI,CAAC,IAAI,cAAc;AACrB,iBAAO,SAAS;AAAA,QAClB;AACA,eAAO,IAAI,aAAa,YAAY;AACpC,YAAI,OAAO,KAAK,IAAI,YAAY,EAAE,WAAW,GAAG;AAC9C,iBAAO,IAAI;AAAA,QACb;AACA,aAAK,IAAI,KAAK,QAAQ;AAAA,MACxB,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,SAAS,WAAW,UAAU,SAAU,SAAS,WAAW,MAAM,UAAU;AAC/E,UAAI;AACJ,UAAI,OAAO,cAAc,UAAU;AAEjC,cAAM;AAAA,UACJ,KAAK;AAAA,UACL,MAAM;AAAA,QACR;AACA,YAAI,OAAO,SAAS,YAAY;AAC9B,qBAAW;AACX,iBAAO,CAAC;AAAA,QACV;AAAA,MACF,OAAO;AAEL,cAAM;AACN,YAAI,OAAO,cAAc,YAAY;AACnC,qBAAW;AACX,iBAAO,CAAC;AAAA,QACV,OAAO;AACL,qBAAW;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,QAAQ,CAAC;AAChB,WAAK,aAAa;AAClB,UAAI,SAAS,EAAC,KAAK,IAAI,KAAK,MAAO,IAAI,QAAQ,KAAK,IAAI;AACxD,aAAO,WAAW;AAClB,UAAI,UAAU,OAAO,GAAG,KAAK,OAAO,KAAK,iBAAiB,YAAY;AACpE,eAAO,KAAK,aAAa,KAAK,QAAQ;AAAA,MACxC;AACA,WAAK,SAAS,EAAC,MAAM,CAAC,MAAM,EAAC,GAAG,MAAM,UAAU,UAAU,OAAO,GAAG,CAAC;AAAA,IACvE,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,WAAW,WAAW,YAAY,SAAU,KAAK,MAAM,UAAU;AACpE,UAAI,OAAO,SAAS,YAAY;AAC9B,mBAAW;AACX,eAAO,CAAC;AAAA,MACV;AACA,UAAI,MAAM,OAAO,KAAK,GAAG;AAEzB,UAAI,CAAC,IAAI,QAAQ;AACf,eAAO,SAAS,MAAM,CAAC,CAAC;AAAA,MAC1B;AAEA,UAAI,QAAQ;AACZ,UAAI,UAAU,oBAAI,IAAI;AAEtB,eAAS,aAAa,IAAI,OAAO;AAC/B,YAAI,CAAC,QAAQ,IAAI,EAAE,GAAG;AACpB,kBAAQ,IAAI,IAAI,EAAC,SAAS,CAAC,EAAC,CAAC;AAAA,QAC/B;AACA,gBAAQ,IAAI,EAAE,EAAE,QAAQ,KAAK,KAAK;AAAA,MACpC;AAEA,eAAS,WAAW,IAAI,UAAU;AAEhC,YAAI,eAAe,IAAI,EAAE,EAAE,MAAM,CAAC;AAClC,wBAAgB,UAAU,SAAU,QAAQ,KAAK,SAAS,KACxDO,OAAM;AACJ,cAAI,SAAS,MAAM,MAAM;AACzB,cAAI,MAAM,aAAa,QAAQ,MAAM;AACrC,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,uBAAa,OAAO,KAAK,CAAC;AAE1B,cAAIA,MAAK,WAAW,aAAa;AAC/B,yBAAa,IAAI,MAAM;AAAA,UACzB;AAAA,QACF,CAAC;AAIH,qBAAa,QAAQ,SAAU,QAAQ;AACrC,uBAAa,IAAI,MAAM;AAAA,QACzB,CAAC;AAAA,MACH;AAEA,UAAI,QAAQ,SAAU,IAAI;AACxB,aAAK,iBAAiB,IAAI,SAAU,KAAK,UAAU;AACjD,cAAI,OAAO,IAAI,WAAW,OAAO,IAAI,YAAY,WAAW;AAC1D,oBAAQ,IAAI,IAAI,EAAC,SAAS,IAAI,EAAE,EAAC,CAAC;AAAA,UACpC,WAAW,KAAK;AAEd,mBAAO,SAAS,GAAG;AAAA,UACrB,OAAO;AACL,uBAAW,IAAI,QAAQ;AAAA,UACzB;AAEA,cAAI,EAAE,UAAU,IAAI,QAAQ;AAE1B,gBAAI,aAAa,CAAC;AAClB,oBAAQ,QAAQ,SAAU,OAAO,KAAK;AACpC,yBAAW,GAAG,IAAI;AAAA,YACpB,CAAC;AACD,mBAAO,SAAS,MAAM,UAAU;AAAA,UAClC;AAAA,QACF,CAAC;AAAA,MACH,GAAG,IAAI;AAAA,IACT,CAAC,EAAE,KAAK,IAAI;AASZ,SAAK,UAAU,WAAW,WAAW,SAAU,MAAM,UAAU;AAC7D,cAAQ,MAAM,MAAM,QAAQ;AAAA,IAC9B,CAAC,EAAE,KAAK,IAAI;AAKZ,SAAK,kBAAkB,WAAW,mBAAmB,SAAU,OAAO,WAAW,UAAU;AACzF,WAAK,iBAAiB,OAAO,CAAC,KAAK,YAAY;AAE7C,YAAI,KAAK;AACP,iBAAO,SAAS,GAAG;AAAA,QACrB;AACA,YAAI,SAAS,cAAc,OAAO;AAClC,YAAI,aAAa,CAAC;AAClB,YAAI,OAAO,CAAC;AACZ,eAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,QAAQ;AAC5C,cAAI,OAAO,MAAM,IAAI,WAAW;AAC9B,uBAAW,KAAK,MAAM;AAAA,UACxB;AAAA,QACF,CAAC;AAED,wBAAgB,SAAS,SAAU,QAAQ,KAAK,SAAS,KAAK,MAAM;AAClE,cAAI,SAAS,MAAM,MAAM;AACzB,cAAI,KAAK,WAAW,eAAe,WAAW,QAAQ,MAAM,MAAM,IAAI;AACpE,iBAAK,KAAK,MAAM;AAAA,UAClB;AAAA,QACF,CAAC;AACD,aAAK,cAAc,OAAO,MAAM,QAAQ;AAAA,MAC1C,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,IAAI;AAIZ,SAAK,UAAU,WAAW,WAAW,SAAU,MAAM,UAAU;AAC7D,UAAI,OAAO,SAAS,YAAY;AAC9B,mBAAW;AACX,eAAO,CAAC;AAAA,MACV;AAEA,aAAO,QAAQ,CAAC;AAEhB,WAAK,mBAAmB,KAAK,oBAAoB,CAAC;AAClD,WAAK,iBAAiB,KAAK,EAAC,MAAM,SAAQ,CAAC;AAC3C,UAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,yBAAiB,IAAI;AAAA,MACvB;AAAA,IACF,CAAC,EAAE,KAAK,IAAI;AAGZ,SAAK,MAAM,WAAW,OAAO,SAAU,IAAI,MAAM,IAAI;AACnD,UAAI,OAAO,SAAS,YAAY;AAC9B,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AACA,aAAO,QAAQ,CAAC;AAChB,UAAI,OAAO,OAAO,UAAU;AAC1B,eAAO,GAAG,YAAY,UAAU,CAAC;AAAA,MACnC;AACA,UAAI,UAAU,EAAE,KAAK,OAAO,KAAK,cAAc,YAAY;AACzD,eAAO,KAAK,UAAU,IAAI,EAAE;AAAA,MAC9B;AACA,UAAI,SAAS,CAAC;AAEd,YAAM,iBAAiB,MAAM;AAC3B,YAAI,SAAS,CAAC;AACd,YAAI,QAAQ,OAAO;AAEnB,YAAI,CAAC,OAAO;AACV,iBAAO,GAAG,MAAM,MAAM;AAAA,QACxB;AAGA,eAAO,QAAQ,CAAC,SAAS;AACvB,eAAK,IAAI,IAAI;AAAA,YACX,KAAK;AAAA,YACL,MAAM,KAAK;AAAA,YACX,QAAQ,KAAK;AAAA,YACb,aAAa,KAAK;AAAA,YAClB,QAAQ,KAAK;AAAA,UACf,GAAG,SAAU,KAAK,KAAK;AACrB,gBAAI,CAAC,KAAK;AAER,kBAAI;AACJ,uBAASC,KAAI,GAAGC,KAAI,OAAO,QAAQD,KAAIC,IAAGD,MAAK;AAC7C,oBAAI,OAAOA,EAAC,EAAE,MAAM,OAAOA,EAAC,EAAE,GAAG,SAAS,IAAI,MAAM;AAClD,6BAAW;AACX;AAAA,gBACF;AAAA,cACF;AACA,kBAAI,CAAC,UAAU;AACb,uBAAO,KAAK,EAAC,IAAI,IAAG,CAAC;AAAA,cACvB;AAAA,YACF,OAAO;AACL,qBAAO,KAAK,EAAC,SAAS,KAAI,CAAC;AAAA,YAC7B;AACA;AACA,gBAAI,CAAC,OAAO;AACV,iBAAG,MAAM,MAAM;AAAA,YACjB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,UAAI,KAAK,WAAW;AAClB,YAAI,KAAK,cAAc,OAAO;AAC5B,eAAK,iBAAiB,IAAI,SAAU,KAAK,UAAU;AAEjD,gBAAI,KAAK;AACP,qBAAO,GAAG,GAAG;AAAA,YACf;AACA,qBAAS,cAAc,QAAQ,EAAE,IAAI,SAAU,MAAM;AACnD,qBAAO,KAAK;AAAA,YACd,CAAC;AACD,2BAAe;AAAA,UACjB,CAAC;AAAA,QACH,OAAO;AACL,cAAI,MAAM,QAAQ,KAAK,SAAS,GAAG;AACjC,qBAAS,KAAK;AACd,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAI,IAAI,OAAO,CAAC;AAEhB,kBAAI,CAAC,WAAW,CAAC,GAAG;AAClB,uBAAO,GAAG,YAAY,WAAW,CAAC;AAAA,cACpC;AAAA,YACF;AACA,2BAAe;AAAA,UACjB,OAAO;AACL,mBAAO,GAAG,YAAY,eAAe,iBAAiB,CAAC;AAAA,UACzD;AAAA,QACF;AACA;AAAA,MACF;AAEA,aAAO,KAAK,KAAK,IAAI,MAAM,CAAC,KAAK,WAAW;AAC1C,YAAI,KAAK;AACP,cAAI,QAAQ;AACZ,iBAAO,GAAG,GAAG;AAAA,QACf;AAEA,YAAI,MAAM,OAAO;AACjB,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,OAAO;AAEjB,YAAI,KAAK,WAAW;AAClB,cAAI,YAAY,iBAAiB,QAAQ;AACzC,cAAI,UAAU,QAAQ;AACpB,gBAAI,aAAa;AAAA,UACnB;AAAA,QACF;AAEA,YAAI,UAAU,UAAU,IAAI,IAAI,GAAG;AACjC,cAAI,WAAW;AAAA,QACjB;AAEA,YAAI,KAAK,QAAQ,KAAK,WAAW;AAC/B,cAAI,cAAc,IAAI,KAAK,MAAM,GAAG;AACpC,cAAI,QAAc,SAAS,YAAY,CAAC,GAAG,EAAE;AAC7C,cAAI,UAAc,YAAY,CAAC;AAE/B,cAAI,QAAQ,WAAW,SAAS,QAAQ;AACxC,cAAI,OAAO;AAEX,mBAASA,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,gBAAI,cAAc,MAAMA,EAAC;AACzB,kBAAM,YAAY,YAAY,IAAI,UAAU,OAAK,EAAE,OAAO,OAAO;AACjE,gBAAI,oBAAoB,cAAe,QAAQ;AAE/C,gBAAI,qBAAsB,CAAC,QAAQ,cAAc,IAAK;AACpD,qBAAO;AAAA,YACT;AAAA,UACF;AAGA,cAAI,CAAC,MAAM;AACT,kBAAM,IAAI,MAAM,kBAAkB;AAClC,gBAAI,QAAQ;AACZ,mBAAO,GAAG,GAAG;AAAA,UACf;AAEA,gBAAM,SAAS,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC;AACpC,gBAAM,aAAa,KAAK,IAAI,UAAU,OAAK,EAAE,OAAO,MAAM,IAAI;AAC9D,cAAI,UAAU,KAAK,IAAI,SAAS;AAChC,eAAK,IAAI,OAAO,YAAY,OAAO;AACnC,eAAK,IAAI,QAAQ;AAEjB,cAAI,KAAK,MAAM;AACb,gBAAI,aAAa;AAAA,cACf,OAAQ,KAAK,MAAM,KAAK,IAAI,SAAU;AAAA,cACtC,KAAK,KAAK,IAAI,IAAI,SAAU,QAAQ;AAClC,uBAAO,OAAO;AAAA,cAChB,CAAC;AAAA,YACH;AAAA,UACF;AACA,cAAI,KAAK,WAAW;AAClB,gBAAI,MAAO,KAAK,MAAM,KAAK,IAAI;AAC/B,gBAAI,aAAa,KAAK,IAAI,IAAI,SAAU,QAAQ;AAC9C;AACA,qBAAO;AAAA,gBACL,KAAK,MAAM,MAAM,OAAO;AAAA,gBACxB,QAAQ,OAAO,KAAK;AAAA,cACtB;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAEA,YAAI,KAAK,eAAe,IAAI,cAAc;AACxC,cAAI,cAAc,IAAI;AACtB,cAAI,QAAQ,OAAO,KAAK,WAAW,EAAE;AACrC,cAAI,UAAU,GAAG;AACf,mBAAO,GAAG,MAAM,GAAG;AAAA,UACrB;AACA,iBAAO,KAAK,WAAW,EAAE,QAAQ,CAACE,SAAQ;AACxC,iBAAK,eAAe,IAAI,KAAKA,MAAK,YAAYA,IAAG,GAAG;AAAA,cAClD,QAAQ,KAAK;AAAA,cACb;AAAA,cACA;AAAA,YACF,GAAG,SAAUC,MAAK,MAAM;AACtB,kBAAI,MAAM,IAAI,aAAaD,IAAG;AAC9B,kBAAI,OAAO;AACX,qBAAO,IAAI;AACX,qBAAO,IAAI;AACX,kBAAI,CAAC,EAAE,OAAO;AACZ,mBAAG,MAAM,GAAG;AAAA,cACd;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH,OAAO;AACL,cAAI,IAAI,cAAc;AACpB,qBAAS,OAAO,IAAI,cAAc;AAEhC,kBAAI,OAAO,UAAU,eAAe,KAAK,IAAI,cAAc,GAAG,GAAG;AAC/D,oBAAI,aAAa,GAAG,EAAE,OAAO;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AACA,aAAG,MAAM,GAAG;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,IAAI;AAKZ,SAAK,gBAAgB,WAAW,iBAAiB,SAAU,OAAO,cAAc,MAAM,UAAU;AAC9F,UAAI,gBAAgB,UAAU;AAC5B,mBAAW;AACX,eAAO,CAAC;AAAA,MACV;AACA,WAAK,KAAK,OAAO,MAAM,CAAC,KAAK,QAAQ;AACnC,YAAI,KAAK;AACP,iBAAO,SAAS,GAAG;AAAA,QACrB;AACA,YAAI,IAAI,IAAI,gBAAgB,IAAI,IAAI,aAAa,YAAY,GAAG;AAC9D,eAAK,MAAM,IAAI;AACf,eAAK,SAAS;AACd,eAAK,WAAW,IAAI;AACpB,eAAK;AAAA,YAAe;AAAA,YAAO;AAAA,YACP,IAAI,IAAI,aAAa,YAAY;AAAA,YAAG;AAAA,YAAM;AAAA,UAAQ;AAAA,QACxE,OAAO;AACL,iBAAO,SAAS,YAAY,WAAW,CAAC;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,UAAU,WAAW,WAAW,SAAU,MAAM,UAAU;AAC7D,UAAI,OAAO,SAAS,YAAY;AAC9B,mBAAW;AACX,eAAO,CAAC;AAAA,MACV;AACA,WAAK,OAAO,OAAO,KAAK,SAAS,cAAc,KAAK,OAAO;AAC3D,UAAI,KAAK,WAAW;AAClB,aAAK,WAAW,KAAK;AAAA,MACvB;AACA,UAAI,KAAK,SAAS;AAChB,aAAK,SAAS,KAAK;AAAA,MACrB;AACA,UAAI,UAAU,MAAM;AAClB,YAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC7B,iBAAO,SAAS,IAAI,UAAU,+BAA+B,CAAC;AAAA,QAChE;AACA,YAAI,kBACF,CAAC,YAAY,UAAU,KAAK,EAAE,OAAO,SAAUE,kBAAiB;AAChE,iBAAOA,oBAAmB;AAAA,QAC5B,CAAC,EAAE,CAAC;AACJ,YAAI,iBAAiB;AACnB,mBAAS;AAAA,YAAY;AAAA,YACnB,sBAAsB,kBACtB;AAAA,UACF,CAAC;AACD;AAAA,QACF;AACA,YAAI,CAAC,SAAS,IAAI,GAAG;AACnB,2BAAiB,IAAI;AACrB,cAAI,KAAK,KAAK,WAAW,GAAG;AAC1B,mBAAO,KAAK,SAAS,EAAC,OAAO,EAAC,GAAG,QAAQ;AAAA,UAC3C;AAAA,QACF;AAAA,MACF;AAEA,aAAO,KAAK,SAAS,MAAM,QAAQ;AAAA,IACrC,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,QAAQ,WAAW,SAAS,SAAU,UAAU;AACnD,WAAK,UAAU;AACf,WAAK,KAAK,QAAQ;AAClB,aAAO,KAAK,OAAO,QAAQ;AAAA,IAC7B,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,OAAO,WAAW,QAAQ,SAAU,UAAU;AACjD,WAAK,MAAM,CAAC,KAAK,SAAS;AACxB,YAAI,KAAK;AACP,iBAAO,SAAS,GAAG;AAAA,QACrB;AAEA,aAAK,UAAU,KAAK,WAAW,KAAK;AACpC,aAAK,kBAAkB,CAAC,EAAE,KAAK,mBAAmB,CAAC,SAAS,IAAI;AAChE,aAAK,UAAU,KAAK;AACpB,iBAAS,MAAM,IAAI;AAAA,MACrB,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,KAAK,WAAW,MAAM,SAAU,UAAU;AAC7C,aAAO,KAAK,IAAI,QAAQ;AAAA,IAC1B,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,WAAW,WAAW,YAAY,SAAU,KAAK,MAAM,UAAU;AACpE,UAAI,OAAO,SAAS,YAAY;AAC9B,mBAAW;AACX,eAAO,CAAC;AAAA,MACV;AAEA,aAAO,QAAQ,CAAC;AAEhB,UAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,cAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAEA,UAAI,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,IAAI,GAAG;AACjD,eAAO,SAAS,YAAY,iBAAiB,CAAC;AAAA,MAChD;AAEA,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK,QAAQ,EAAE,GAAG;AACxC,cAAM,MAAM,IAAI,KAAK,CAAC;AACtB,YAAI,eAAe,GAAG,GAAG;AACvB,iBAAO,SAAS,YAAY,aAAa,CAAC;AAAA,QAC5C;AACA,YAAI,UAAU,OAAO,CAAC,WAAW,IAAI,IAAI,GAAG;AAC1C,iBAAO,SAAS,YAAY,WAAW,CAAC;AAAA,QAC1C;AAAA,MACF;AAEA,UAAI;AACJ,UAAI,KAAK,QAAQ,SAAU,KAAK;AAC9B,YAAI,IAAI,cAAc;AACpB,iBAAO,KAAK,IAAI,YAAY,EAAE,QAAQ,SAAU,MAAM;AACpD,8BAAkB,mBAAmB,oBAAoB,IAAI;AAC7D,gBAAI,CAAC,IAAI,aAAa,IAAI,EAAE,cAAc;AACxC,6BAAe,QAAQ,cAAc,MAAM,eAAe,IAAI,KAAK,yBAAyB;AAAA,YAC9F;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,iBAAiB;AACnB,eAAO,SAAS,YAAY,aAAa,eAAe,CAAC;AAAA,MAC3D;AAEA,UAAI,EAAE,eAAe,OAAO;AAC1B,YAAI,eAAe,KAAK;AACtB,eAAK,YAAY,IAAI;AAAA,QACvB,OAAO;AACL,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AAEA,UAAI,UAAU;AACd,UAAI,CAAC,KAAK,aAAa,CAAC,SAAS,OAAO,GAAG;AAGzC,YAAI,KAAK,KAAK,kBAAkB;AAAA,MAClC;AAEA,gBAAU,IAAI,IAAI;AAKlB,UAAI,MAAM,IAAI,KAAK,IAAI,SAAU,KAAK;AACpC,eAAO,IAAI;AAAA,MACb,CAAC;AAED,WAAK,UAAU,KAAK,MAAM,SAAU,KAAK,KAAK;AAC5C,YAAI,KAAK;AACP,iBAAO,SAAS,GAAG;AAAA,QACrB;AACA,YAAI,CAAC,KAAK,WAAW;AAEnB,gBAAM,IAAI,OAAO,SAAU,GAAG;AAC5B,mBAAO,EAAE;AAAA,UACX,CAAC;AAAA,QACH;AAEA,YAAI,CAAC,SAAS,OAAO,GAAG;AACtB,mBAASJ,KAAI,GAAG,IAAI,IAAI,QAAQA,KAAI,GAAGA,MAAK;AAC1C,gBAAIA,EAAC,EAAE,KAAK,IAAIA,EAAC,EAAE,MAAM,IAAIA,EAAC;AAAA,UAChC;AAAA,QACF;AAEA,iBAAS,MAAM,GAAG;AAAA,MACpB,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,4BAA4B,WAAW,6BAA6B,SAAU,aAAa,UAAU;AACxG,UAAI,YAAY,MAAM,KAAK,MAAM;AACjC,UAAI,KAAK,OAAO,cAAc;AAC5B,kBAAU,UAAU,KAAK,OAAO;AAAA,MAClC;AAEA,UAAI,QAAQ,IAAI,KAAK,YAAY,aAAa,SAAS;AAEvD,eAAS,QAAQ,KAAK;AACpB,YAAI,eAAe,IAAI,gBAAgB,CAAC;AACxC,YAAI,IAAI,aAAa,WAAW,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,YAAI,aAAa,WAAW,IAAI;AAChC,eAAO;AAAA,MACT;AACA,aAAO,MAAM,8BAA8B,OAAO,EAAE,KAAK,WAAY;AACnE,iBAAS,MAAM,EAAC,IAAI,MAAK,CAAC;AAAA,MAC5B,CAAC,EAAE,MAAM,QAAQ;AAAA,IACnB,CAAC,EAAE,KAAK,IAAI;AAEZ,SAAK,UAAU,WAAW,WAAW,SAAU,MAAM,UAAU;AAE7D,UAAI,OAAO,SAAS,YAAY;AAC9B,mBAAW;AACX,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,YAAY,gBAAgB,OAAO,KAAK,aAAa;AAEzD,YAAM,YAAY,MAAM;AAEtB,aAAK,SAAS,MAAM,CAAC,KAAK,SAAS;AACjC,cAAI,KAAK;AACP,mBAAO,SAAS,GAAG;AAAA,UACrB;AACA,eAAK,aAAa;AAClB,eAAK,KAAK,WAAW;AACrB,mBAAS,MAAM,QAAQ,EAAE,MAAM,KAAK,CAAC;AAAA,QACvC,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,IAAI,GAAG;AAElB,eAAO,UAAU;AAAA,MACnB;AAEA,WAAK,IAAI,8BAA8B,CAAC,KAAK,aAAa;AACxD,YAAI,KAAK;AAEP,cAAI,IAAI,WAAW,KAAK;AACtB,mBAAO,SAAS,GAAG;AAAA,UACrB,OAAO;AACL,mBAAO,UAAU;AAAA,UACnB;AAAA,QACF;AACA,YAAI,eAAe,SAAS;AAC5B,YAAIK,WAAU,KAAK;AACnB,YAAI,aAAa,OAAO,KAAK,YAAY,EAAE,IAAI,CAAC,SAAS;AAGvD,cAAI,WAAW,YACb,KAAK,QAAQ,IAAI,OAAO,MAAMA,SAAQ,MAAM,GAAG,EAAE,IAAI;AACvD,iBAAO,IAAIA,SAAQ,UAAU,KAAK,MAAM,EAAE,QAAQ;AAAA,QACpD,CAAC;AACD,gBAAQ,IAAI,UAAU,EAAE,KAAK,WAAW,QAAQ;AAAA,MAClD,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,IAAI;AAAA,EACd;AAAA,EAEA,SAAS,MAAM,UAAU;AACvB,QAAI,cAAc;AAAA,MAChB,aAAa;AAAA,MACb,UAAU,KAAK,YAAY;AAAA,MAC3B,OAAO,KAAK,YAAY;AAAA,IAC1B;AACA,QAAI,WAAW,CAAC;AAEhB,QAAI;AACJ,QAAI,gBAAgB;AAEpB,UAAM,WAAW,CAAC,QAAQ;AACxB,WAAK,YAAY,OAAO,QAAQ;AAAA,QAC9B,iBAAiB,EAAE;AAAA,MACrB,CAAC;AACD,eAAS,KAAK,KAAK,gBAAgB,IAAI,IAAI,CAAC,CAAC;AAAA,IAC/C;AACA,UAAM,UAAU,CAAC,QAAQ;AACvB,WAAK,YAAY,OAAO,QAAQ,GAAG;AACnC,eAAS,GAAG;AAAA,IACd;AACA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,UAAU,KAAK;AACnB,cAAQ,IAAI,QAAQ,EAAE,KAAK,MAAM;AAC/B,eAAO,OAAO,MAAM,qBAAqB,CAAC,QAAQ;AAChD,cAAI,CAAC,IAAI,YAAY,IAAI,WAAW,SAAS;AAC3C,gBAAI,WAAW;AACf,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC,EAAE,KAAK,MAAM;AACZ,aAAK,YAAY,OAAO,MAAM;AAC9B,iBAAS,MAAM,EAAC,IAAI,KAAI,CAAC;AAAA,MAC3B,CAAC,EAAE,MAAM,OAAO;AAAA,IAClB;AAEA,SAAK,KAAK,EAAE,KAAK,CAAC,SAAS;AACzB,eAAS,KAAK,YAAY,IAAI;AAAA,QAC5B,MAAM;AAAA,QACN,aAAa,KAAK,aAAa,YAAY;AAAA,MAC7C,CAAC;AAED,WAAK,QAAQ,WAAW,EACrB,GAAG,UAAU,QAAQ,EACrB,GAAG,YAAY,UAAU,EACzB,GAAG,SAAS,OAAO;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EAEA,QAAQ,MAAM,UAAU;AACtB,QAAI,OAAO,SAAS,YAAY;AAC9B,iBAAW;AACX,aAAO,CAAC;AAAA,IACV;AAEA,WAAO,QAAQ,CAAC;AAKhB,SAAK,cAAe,iBAAiB,OAAQ,KAAK,cAAc,CAAC,KAAK;AAEtE,WAAO,IAAI,UAAU,MAAM,MAAM,QAAQ;AAAA,EAC3C;AAAA,EAEA,OAAO;AACL,WAAQ,OAAO,KAAK,UAAU,aAAc,KAAK,MAAM,IAAI,KAAK;AAAA,EAClE;AACF;AAIA,gBAAgB,UAAU,QAAQ,WAAW,UAAU,SAAU,OAAO,QAAQ,UAAU;AACxF,MAAI,OAAO,KAAK,WAAW,aAAa;AACtC,WAAO,SAAS,YAAY,eAAe,qCAAqC,KAAK,UAAU,WAAW,CAAC;AAAA,EAC7G;AACA,MAAIf,QAAO;AAEX,EAAAA,MAAK,iBAAiB,OAAO,CAAC,OAAO,SAAS;AAC5C,QAAI,OAAO;AACT,aAAO,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,CAAC,MAAM;AACT,aAAO,SAAS,YAAY,WAAW,CAAC;AAAA,IAC1C;AACA,QAAI;AACJ,QAAI;AACF,aAAO,eAAe,MAAM,MAAM;AAAA,IACpC,SAASgB,QAAO;AACd,aAAO,SAASA,OAAM,WAAWA,MAAK;AAAA,IACxC;AACA,IAAAhB,MAAK,OAAO,OAAO,MAAM,CAACgB,QAAO,WAAW;AAC1C,UAAIA,QAAO;AACT,eAAO,SAASA,MAAK;AAAA,MACvB,OAAO;AACL,uBAAehB,OAAM,OAAO,MAAM,EAAE,KAAK,WAAY;AACnD,iBAAO,SAAS,MAAM,MAAM;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AAED,IAAM,YAAN,MAAgB;AAAA,EACd,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EAEA,UAAU;AACR,QAAI;AACJ,QAAI,KAAK,QAAQ;AACf,aAAQ,MAAM,KAAK,MAAM,MAAM,GAAI;AACjC,YAAI,KAAK,MAAM;AAAA,MACjB;AAAA,IACF,OAAO;AACL,aAAQ,MAAM,KAAK,MAAM,MAAM,GAAI;AACjC,YAAI;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAK;AACR,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,MAAM,IAAI;AACR,SAAK,UAAU;AACf,SAAK,KAAK;AACV,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,QAAQ,KAAK;AACX,SAAK,MAAM,KAAK,GAAG;AACnB,QAAI,KAAK,QAAQ;AACf,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACF;AAEA,SAAS,aAAa,MAAM,MAAM;AAChC,MAAIiB,SAAQ,KAAK,MAAM,oBAAoB;AAC3C,MAAIA,QAAO;AAET,WAAO;AAAA,MACL,MAAM,SAAS,KAAKA,OAAM,CAAC,CAAC,IAAIA,OAAM,CAAC,IAAI,QAAQA,OAAM,CAAC,IAAIA,OAAM,CAAC;AAAA,MACrE,SAASA,OAAM,CAAC;AAAA,IAClB;AAAA,EACF;AAEA,MAAI,WAAW,QAAQ;AACvB,MAAI,oBAAoB,QAAQ;AAChC,MAAI,SAAS,QAAQ;AACrB,MAAI,cAAc,KAAK;AAEvB,MAAI,CAAC,aAAa;AAChB,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,EAAE,GAAG;AACjD,oBAAc,kBAAkB,CAAC;AAGjC,UAAI,gBAAgB,SAAS,YAAY,YACrC,gBAAgB,KAAK,aAAa,sBAAsB,SAAS,IAAI,GAAG;AAE1E,uBAAe,OAAO,6BAA6B,OAAO,4EACM;AAChE;AAAA,MACF;AACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,UAAU,SAAS,WAAW;AAGlC,MAAI,YAAa,WAAW,gBAAgB,UAC1C,QAAQ,aAAa;AAEvB,SAAO;AAAA,IACL,MAAM,YAAa,SAAS,OAAQ;AAAA,IACpC,SAAS;AAAA,EACX;AACF;AAEA,SAAS,SAAS,GAAG,GAAG;AACtB,IAAE,YAAY,OAAO,OAAO,EAAE,WAAW;AAAA,IACvC,aAAa,EAAE,OAAO,EAAE;AAAA,EAC1B,CAAC;AACH;AAEA,SAAS,YAAY,QAAQC,OAAM;AACjC,MAAI,QAAQ,YAAa,MAAM;AAC7B,QAAI,EAAE,gBAAgB,QAAQ;AAC5B,aAAO,IAAI,MAAM,GAAG,IAAI;AAAA,IAC1B;AACA,IAAAA,MAAK,MAAM,MAAM,IAAI;AAAA,EACvB;AACA,WAAS,OAAO,MAAM;AACtB,SAAO;AACT;AAYA,SAAS,sBAAsBlB,OAAM;AAEnC,WAAS,YAAY,kBAAkB;AACrC,IAAAA,MAAK,eAAe,UAAU,QAAQ;AACtC,QAAI,CAAC,kBAAkB;AACrB,MAAAA,MAAK,YAAY,KAAK,aAAaA,MAAK,IAAI;AAAA,IAC9C;AAAA,EACF;AAEA,WAAS,WAAW;AAClB,IAAAA,MAAK,eAAe,aAAa,WAAW;AAC5C,IAAAA,MAAK,YAAY,KAAK,SAASA,KAAI;AAAA,EACrC;AAEA,EAAAA,MAAK,KAAK,aAAa,WAAW;AAClC,EAAAA,MAAK,KAAK,UAAU,QAAQ;AAC5B,EAAAA,MAAK,YAAY,KAAK,OAAOA,KAAI;AACnC;AAEA,IAAM,gBAAN,cAA4B,gBAAgB;AAAA,EAC1C,YAAY,MAAM,MAAM;AACtB,UAAM;AACN,SAAK,OAAO,MAAM,IAAI;AAAA,EACxB;AAAA,EAEA,OAAO,MAAM,MAAM;AACjB,UAAM,OAAO;AACb,WAAO,QAAQ,CAAC;AAEhB,QAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,aAAO;AACP,aAAO,KAAK;AACZ,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,KAAK,uBAAuB,QAAW;AACzC,WAAK,qBAAqB;AAAA,IAC5B;AAEA,SAAK,SAAS,OAAO,MAAM,IAAI;AAE/B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,SAAS,QAAQ;AAEtB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AAEA,QAAI,gBAAgB,KAAK,UAAU,MAAM;AACzC,QAAI,UAAU,aAAa,cAAc,IAAI;AAE7C,SAAK,OAAO,QAAQ;AACpB,SAAK,UAAU,KAAK,WAAW,QAAQ;AAEvC,SAAK,OAAO;AACZ,SAAK,WAAW,KAAK;AACrB,YAAQ,KAAK,SAAS,CAAC,WAAW,oBAAoB,KAAK,OAAO,CAAC;AAEnE,QAAI,CAAC,QAAQ,SAAS,KAAK,OAAO,KAC9B,CAAC,QAAQ,SAAS,KAAK,OAAO,EAAE,MAAM,GAAG;AAC3C,YAAM,IAAI,MAAM,sBAAsB,KAAK,OAAO;AAAA,IACpD;AAEA,QAAI,KAAK,cAAc;AACrB,UAAI,CAAC,QAAQ,SAAS,KAAK,YAAY,KACnC,CAAC,QAAQ,SAAS,KAAK,YAAY,EAAE,MAAM,GAAG;AAChD,cAAM,IAAI,MAAM,2BAA2B,KAAK,YAAY;AAAA,MAC9D;AAAA,IACF;AAEA,SAAK,YAAY,IAAI,UAAU;AAE/B,SAAK,UAAU,KAAK;AAEpB,YAAQ,SAAS,KAAK,OAAO,EAAE,KAAK,MAAM,MAAM,CAAC,QAAQ;AACvD,UAAI,KAAK;AACP,eAAO,KAAK,UAAU,KAAK,GAAG;AAAA,MAChC;AACA,4BAAsB,IAAI;AAE1B,WAAK,KAAK,WAAW,IAAI;AACzB,cAAQ,KAAK,WAAW,KAAK,IAAI;AACjC,WAAK,UAAU,MAAM,IAAI;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAEA,IAAM,UAAU,YAAY,eAAe,SAAU,MAAM,MAAM;AAC/D,gBAAc,UAAU,OAAO,KAAK,MAAM,MAAM,IAAI;AACtD,CAAC;AAED,IAAI,MAAM;AACV,IAAI,IAAI;AAER,IAAM,cAAN,MAAkB;AAAA,EAChB,cAAc;AACZ,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EAEA,OAAO;AACL,WAAO,OAAO,OAAO,KAAK,KAAK;AAAA,EACjC;AAAA,EAEA,IAAI,MAAM;AACR,UAAM,KAAK,WAAG;AACd,SAAK,MAAM,EAAE,IAAI;AAAA,MACf;AAAA,MACA,MAAM,KAAK;AAAA,MACX,aAAa,KAAK;AAAA,MAClB,aAAY,oBAAI,KAAK,GAAE,OAAO;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,IAAI;AACN,WAAO,KAAK,MAAM,EAAE;AAAA,EACtB;AAAA;AAAA,EAGA,OAAO,IAAI,QAAQ;AACjB,WAAO,KAAK,MAAM,EAAE;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO,IAAI,aAAa;AACtB,UAAM,OAAO,KAAK,MAAM,EAAE;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC/B,YAAM,aAAa;AAAA,QACjB,IAAI,KAAK;AAAA,QACT,MAAM,KAAK;AAAA,QACX,YAAY,KAAK;AAAA,QACjB,aAAa,YAAY,eAAe,KAAK;AAAA,QAC7C,iBAAiB,YAAY,mBAAmB,KAAK;AAAA,QACrD,aAAY,oBAAI,KAAK,GAAE,OAAO;AAAA,MAChC;AACA,WAAK,MAAM,EAAE,IAAI;AAAA,IACnB;AACA,WAAO,KAAK;AAAA,EACd;AACF;AAEA,QAAQ,WAAW,CAAC;AACpB,QAAQ,oBAAoB,CAAC;AAE7B,QAAQ,SAAS;AAEjB,IAAI,eAAe,IAAI,cAAAE,QAAG;AAE1B,SAAS,kBAAkB,OAAO;AAChC,SAAO,KAAK,cAAAA,QAAG,SAAS,EAAE,QAAQ,SAAU,KAAK;AAC/C,QAAI,OAAO,cAAAA,QAAG,UAAU,GAAG,MAAM,YAAY;AAC3C,YAAM,GAAG,IAAI,aAAa,GAAG,EAAE,KAAK,YAAY;AAAA,IAClD;AAAA,EACF,CAAC;AAID,MAAI,oBAAoB,MAAM,wBAAwB,oBAAI,IAAI;AAE9D,QAAM,GAAG,OAAO,SAAS,iBAAiB,IAAI;AAC5C,QAAI,CAAC,kBAAkB,IAAI,GAAG,IAAI,GAAG;AACnC,wBAAkB,IAAI,GAAG,MAAM,CAAC,CAAC;AAAA,IACnC;AACA,sBAAkB,IAAI,GAAG,IAAI,EAAE,KAAK,EAAE;AAAA,EACxC,CAAC;AAED,QAAM,GAAG,SAAS,SAAS,mBAAmB,IAAI;AAChD,QAAI,CAAC,kBAAkB,IAAI,GAAG,IAAI,GAAG;AACnC;AAAA,IACF;AACA,QAAI,SAAS,kBAAkB,IAAI,GAAG,IAAI;AAC1C,QAAI,MAAM,OAAO,QAAQ,EAAE;AAC3B,QAAI,MAAM,GAAG;AAEX;AAAA,IACF;AACA,WAAO,OAAO,KAAK,CAAC;AACpB,QAAI,OAAO,SAAS,GAAG;AAErB,wBAAkB,IAAI,GAAG,MAAM,MAAM;AAAA,IACvC,OAAO;AACL,wBAAkB,OAAO,GAAG,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AAED,QAAM,GAAG,aAAa,SAAS,uBAAuB,MAAM;AAC1D,QAAI,CAAC,kBAAkB,IAAI,IAAI,GAAG;AAChC;AAAA,IACF;AACA,QAAI,SAAS,kBAAkB,IAAI,IAAI;AACvC,sBAAkB,OAAO,IAAI;AAC7B,WAAO,QAAQ,SAAU,IAAI;AAC3B,SAAG,KAAK,aAAY,IAAI;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC;AACH;AAEA,kBAAkB,OAAO;AAEzB,QAAQ,UAAU,SAAU,IAAI,KAAK,wBAAwB;AAE3D,MAAI,IAAI,MAAM,GAAG;AACf,YAAQ,SAAS,EAAE,IAAI;AACvB,QAAI,wBAAwB;AAC1B,cAAQ,kBAAkB,KAAK,EAAE;AAAA,IACnC;AAAA,EACF;AACF;AAEA,QAAQ,SAAS,SAAU,KAAK;AAC9B,MAAI,OAAO,QAAQ,YAAY;AAC7B,QAAI,OAAO;AAAA,EACb,WAAW,OAAO,QAAQ,YAAY,OAAO,KAAK,GAAG,EAAE,WAAW,GAAG;AACnE,UAAM,IAAI,MAAM,0BAA0B,MAAM,qCAAqC;AAAA,EACvF,OAAO;AACL,WAAO,KAAK,GAAG,EAAE,QAAQ,SAAU,IAAI;AACrC,cAAQ,UAAU,EAAE,IAAI,IAAI,EAAE;AAAA,IAChC,CAAC;AAAA,EACH;AACA,MAAI,KAAK,YAAY;AACnB,YAAQ,aAAa,OAAO,OAAO,CAAC,GAAG,KAAK,UAAU;AAAA,EACxD;AACA,SAAO;AACT;AAEA,QAAQ,WAAW,SAAU,aAAa;AACxC,MAAI,oBAAoB,YAAY,SAAS,SAAU,MAAM,MAAM;AACjE,WAAO,QAAQ,CAAC;AAEhB,QAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,aAAO;AACP,aAAO,KAAK;AACZ,aAAO,KAAK;AAAA,IACd;AAEA,WAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,YAAY,IAAI;AAC3D,YAAQ,KAAK,MAAM,MAAM,IAAI;AAAA,EAC/B,CAAC;AAED,oBAAkB,oBAAoB,QAAQ,kBAAkB,MAAM;AACtE,SAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,KAAK;AAC1C,QAAI,EAAE,OAAO,oBAAoB;AAC/B,wBAAkB,GAAG,IAAI,QAAQ,GAAG;AAAA,IACtC;AAAA,EACF,CAAC;AAID,oBAAkB,aAAa,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,WAAW;AAE7E,SAAO;AACT;AAEA,QAAQ,QAAQ,SAAU,KAAK,MAAM;AACnC,SAAO,IAAI,KAAK,IAAI;AACtB;AAEA,QAAQ,UAAU,cAAc,QAAQ,cAAc,IAAI,YAAY;AAGtE,IAAI,UAAU;AAId,SAAS,gBAAgB,KAAK,aAAa;AACzC,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,QAAI,MAAM,YAAY,CAAC;AACvB,YAAQ,MAAM,GAAG;AACjB,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,MAAM,OAAO;AAC5B,SAAO,OAAO,QAAQ,KAAK,OAAO,QAAQ,IAAI;AAChD;AAGA,SAAS,WAAW,WAAW;AAE7B,MAAI,SAAS,CAAC;AACd,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,KAAK;AACpD,QAAI,KAAK,UAAU,CAAC;AACpB,QAAI,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,SAAS,OAAO,OAAO,OAAO,MAAM;AAEpE,gBAAU,QAAQ,UAAU,GAAG,QAAQ,SAAS,CAAC,IAAI;AAAA,IACvD,WAAW,OAAO,KAAK;AAErB,aAAO,KAAK,OAAO;AACnB,gBAAU;AAAA,IACZ,OAAO;AACL,iBAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO,KAAK,OAAO;AACnB,SAAO;AACT;AAEA,IAAI,oBAAoB,CAAC,OAAO,QAAQ,MAAM;AAC9C,SAAS,qBAAqB,OAAO;AACnC,SAAO,kBAAkB,QAAQ,KAAK,IAAI;AAC5C;AAEA,SAAS,OAAO,KAAK;AACnB,SAAO,OAAO,KAAK,GAAG,EAAE,CAAC;AAC3B;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,IAAI,OAAO,GAAG,CAAC;AACxB;AAIA,SAAS,oBAAoB,WAAW;AAKtC,MAAI,MAAM,CAAC;AACX,MAAI,QAAQ,EAAC,KAAK,MAAM,MAAM,KAAI;AAElC,YAAU,QAAQ,SAAU,UAAU;AACpC,WAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,OAAO;AAC7C,UAAI,UAAU,SAAS,KAAK;AAC5B,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,EAAC,KAAK,QAAO;AAAA,MACzB;AAEA,UAAI,qBAAqB,KAAK,GAAG;AAE/B,YAAI,mBAAmB,OAAO;AAC5B,cAAI,MAAM,KAAK,GAAG;AAChB,kBAAM,KAAK,IAAI;AACf,gBAAI,KAAK,IAAI;AACb;AAAA,UACF;AAEA,cAAI,UAAU,CAAC;AACf,cAAI,KAAK,EAAE,QAAQ,SAAU,UAAU;AACrC,mBAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,KAAK;AAC1C,kBAAI,IAAI,QAAQ,GAAG;AACnB,kBAAI,UAAU,KAAK,IAAI,OAAO,KAAK,QAAQ,EAAE,QAAQ,OAAO,KAAK,CAAC,EAAE,MAAM;AAC1E,kBAAI,SAAS,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAC9C,kBAAI,OAAO,KAAK,MAAM,EAAE,UAAU,SAAS;AAIzC;AAAA,cACF;AACA,sBAAQ,KAAK,MAAM;AAAA,YACrB,CAAC;AAAA,UACH,CAAC;AACD,cAAI,KAAK,IAAI;AAAA,QACf,OAAO;AAEL,cAAI,KAAK,IAAI,oBAAoB,CAAC,OAAO,CAAC;AAAA,QAC5C;AAAA,MACF,OAAO;AACL,YAAI,gBAAgB,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AAChD,eAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,UAAU;AAC/C,cAAI,QAAQ,QAAQ,QAAQ;AAE5B,cAAI,aAAa,SAAS,aAAa,QAAQ;AAC7C,mBAAO,WAAW,UAAU,OAAO,aAAa;AAAA,UAClD,WAAW,aAAa,SAAS,aAAa,QAAQ;AACpD,mBAAO,WAAW,UAAU,OAAO,aAAa;AAAA,UAClD,WAAW,aAAa,OAAO;AAC7B,mBAAO,QAAQ,OAAO,aAAa;AAAA,UACrC,WAAW,aAAa,OAAO;AAC7B,mBAAO,QAAQ,OAAO,aAAa;AAAA,UACrC,WAAW,aAAa,UAAU;AAChC,mBAAO,WAAW,OAAO,aAAa;AAAA,UACxC;AACA,wBAAc,QAAQ,IAAI;AAAA,QAC5B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;AAKA,SAAS,WAAW,UAAU,OAAO,eAAe;AAClD,MAAI,OAAO,cAAc,QAAQ,aAAa;AAC5C;AAAA,EACF;AACA,MAAI,OAAO,cAAc,SAAS,aAAa;AAC7C,QAAI,aAAa,QAAQ;AACvB,UAAI,QAAQ,cAAc,MAAM;AAC9B,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,SAAS,cAAc,MAAM;AAC/B,eAAO,cAAc;AACrB,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF,WAAW,OAAO,cAAc,QAAQ,aAAa;AACnD,QAAI,aAAa,QAAQ;AACvB,UAAI,QAAQ,cAAc,KAAK;AAC7B,eAAO,cAAc;AACrB,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,QAAQ,cAAc,KAAK;AAC7B,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF,OAAO;AACL,kBAAc,QAAQ,IAAI;AAAA,EAC5B;AACF;AAGA,SAAS,WAAW,UAAU,OAAO,eAAe;AAClD,MAAI,OAAO,cAAc,QAAQ,aAAa;AAC5C;AAAA,EACF;AACA,MAAI,OAAO,cAAc,SAAS,aAAa;AAC7C,QAAI,aAAa,QAAQ;AACvB,UAAI,QAAQ,cAAc,MAAM;AAC9B,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,SAAS,cAAc,MAAM;AAC/B,eAAO,cAAc;AACrB,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF,WAAW,OAAO,cAAc,QAAQ,aAAa;AACnD,QAAI,aAAa,QAAQ;AACvB,UAAI,QAAQ,cAAc,KAAK;AAC7B,eAAO,cAAc;AACrB,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,QAAQ,cAAc,KAAK;AAC7B,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF,OAAO;AACL,kBAAc,QAAQ,IAAI;AAAA,EAC5B;AACF;AAGA,SAAS,QAAQ,OAAO,eAAe;AACrC,MAAI,SAAS,eAAe;AAE1B,kBAAc,IAAI,KAAK,KAAK;AAAA,EAC9B,OAAO;AACL,kBAAc,MAAM,CAAC,KAAK;AAAA,EAC5B;AACF;AAGA,SAAS,QAAQ,OAAO,eAAe;AAGrC,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,gBAAc,MAAM;AACtB;AAGA,SAAS,WAAW,OAAO,eAAe;AACxC,MAAI,YAAY,eAAe;AAE7B,kBAAc,OAAO,KAAK,KAAK;AAAA,EACjC,OAAO;AACL,kBAAc,SAAS,CAAC,KAAK;AAAA,EAC/B;AACF;AAGA,SAAS,0BAA0B,KAAK;AACpC,WAAS,QAAQ,KAAK;AAClB,QAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,eAAS,KAAK,KAAK;AACf,YAAI,IAAI,CAAC,EAAE,MAAM,GAAG;AAChB,cAAI,CAAC,IAAI,oBAAoB,IAAI,CAAC,EAAE,MAAM,CAAC;AAAA,QAC/C;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,QAAQ,IAAI,IAAI;AACpB,QAAI,OAAO,UAAU,UAAU;AAC3B,gCAA0B,KAAK;AAAA,IACnC;AAAA,EACJ;AACA,SAAO;AACX;AAGA,SAAS,gBAAgB,KAAK,OAAO;AACjC,WAAS,QAAQ,KAAK;AAClB,QAAI,SAAS,QAAQ;AACjB,cAAQ;AAAA,IACZ;AACA,QAAI,QAAQ,IAAI,IAAI;AACpB,QAAI,OAAO,UAAU,UAAU;AAC3B,cAAQ,gBAAgB,OAAO,KAAK;AAAA,IACxC;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,SAAS,MAAM,KAAK;AAGxB,MAAI,gBAAgB,QAAQ,KAAK,GAAG;AAClC,aAAS,0BAA0B,MAAM;AACzC,QAAI,UAAU,QAAQ;AACpB,eAAS,oBAAoB,OAAO,MAAM,CAAC;AAAA,IAC7C;AAAA,EACF;AAEA,GAAC,OAAO,MAAM,EAAE,QAAQ,SAAU,SAAS;AACzC,QAAI,WAAW,QAAQ;AAGrB,aAAO,OAAO,EAAE,QAAQ,SAAU,aAAa;AAC7C,YAAIiB,UAAS,OAAO,KAAK,WAAW;AACpC,iBAAST,KAAI,GAAGA,KAAIS,QAAO,QAAQT,MAAK;AACtC,cAAIU,SAAQD,QAAOT,EAAC;AACpB,cAAIW,WAAU,YAAYD,MAAK;AAC/B,cAAI,OAAOC,aAAY,YAAYA,aAAY,MAAM;AACnD,wBAAYD,MAAK,IAAI,EAAC,KAAKC,SAAO;AAAA,UACpC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,MAAI,UAAU,QAAQ;AAGpB,WAAO,MAAM,IAAI,oBAAoB,CAAC,OAAO,MAAM,CAAC,CAAC;AAAA,EACvD;AAEA,MAAI,SAAS,OAAO,KAAK,MAAM;AAE/B,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,QAAQ,OAAO,CAAC;AACpB,QAAI,UAAU,OAAO,KAAK;AAE1B,QAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACnD,gBAAU,EAAC,KAAK,QAAO;AAAA,IACzB;AACA,WAAO,KAAK,IAAI;AAAA,EAClB;AAEA,0BAAwB,MAAM;AAE9B,SAAO;AACT;AAMA,SAAS,wBAAwB,UAAU;AACzC,SAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,OAAO;AAC7C,QAAI,UAAU,SAAS,KAAK;AAE5B,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAQ,QAAQ,SAAU,aAAa;AACrC,YAAI,eAAe,OAAO,gBAAgB,UAAU;AAClD,kCAAwB,WAAW;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH,WAAW,UAAU,OAAO;AAC1B,eAAS,MAAM,CAAC,OAAO;AAAA,IACzB,WAAW,UAAU,UAAU;AAC7B,eAAS,SAAS,CAAC,OAAO;AAAA,IAC5B,WAAW,WAAW,OAAO,YAAY,UAAU;AACjD,8BAAwB,OAAO;AAAA,IACjC;AAAA,EACF,CAAC;AACH;AAEA,SAAS,IAAI,KAAK,SAAS,YAAY;AACrC,MAAI,UAAU;AACd,MAAI,eAAe,aAAa,IAAI;AAEpC,SAAO,QAAQ,SAAS,cAAc;AACpC,eAAW;AAAA,EACb;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,KAAK,SAAS,YAAY;AACzC,MAAI,UAAU,IAAI,KAAK,SAAS,UAAU;AAC1C,SAAO,UAAU;AACnB;AAEA,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,IAAI,MAAM;AAEV,SAAS,QAAQ,GAAG,GAAG;AAErB,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,CAAC;AAClB,MAAI,aAAa,CAAC;AAElB,MAAI,KAAK,eAAe,CAAC;AACzB,MAAI,KAAK,eAAe,CAAC;AACzB,MAAK,KAAK,OAAQ,GAAG;AACnB,WAAO,KAAK;AAAA,EACd;AACA,UAAQ,OAAO,GAAG;AAAA,IAChB,KAAK;AACH,aAAO,IAAI;AAAA,IACb,KAAK;AACH,aAAO,IAAI,IAAI,KAAK;AAAA,IACtB,KAAK;AACH,aAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,MAAM,QAAQ,CAAC,IAAI,aAAa,GAAG,CAAC,IAAI,cAAc,GAAG,CAAC;AACnE;AAIA,SAAS,aAAa,KAAK;AACzB,UAAQ,OAAO,KAAK;AAAA,IAClB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,UAAI,QAAQ,YAAY,QAAQ,aAAa,MAAM,GAAG,GAAG;AACvD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,KAAK;AACH,UAAI,UAAU;AACd,UAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,YAAI,MAAM,IAAI;AACd,cAAM,IAAI,MAAM,GAAG;AACnB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,CAAC,IAAI,aAAa,QAAQ,CAAC,CAAC;AAAA,QAClC;AAAA,MAEF,WAAW,eAAe,MAAM;AAC9B,eAAO,IAAI,OAAO;AAAA,MACpB,WAAW,QAAQ,MAAM;AACvB,cAAM,CAAC;AACP,iBAAS,KAAK,SAAS;AACrB,cAAI,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACpD,gBAAI,MAAM,QAAQ,CAAC;AACnB,gBAAI,OAAO,QAAQ,aAAa;AAC9B,kBAAI,CAAC,IAAI,aAAa,GAAG;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,EACJ;AACA,SAAO;AACT;AAEA,SAAS,SAAS,KAAK;AACrB,MAAI,QAAQ,MAAM;AAChB,YAAQ,OAAO,KAAK;AAAA,MAClB,KAAK;AACH,eAAO,MAAM,IAAI;AAAA,MACnB,KAAK;AACH,eAAO,qBAAqB,GAAG;AAAA,MACjC,KAAK;AAOH,eAAO,IACJ,QAAQ,WAAW,IAAc,EACjC,QAAQ,WAAW,IAAc,EACjC,QAAQ,WAAW,IAAc;AAAA;AAAA,MAEtC,KAAK;AACH,YAAIC,WAAU,MAAM,QAAQ,GAAG;AAC/B,YAAI,MAAMA,WAAU,MAAM,OAAO,KAAK,GAAG;AACzC,YAAI,IAAI;AACR,YAAI,MAAM,IAAI;AACd,YAAI,SAAS;AACb,YAAIA,UAAS;AACX,iBAAO,EAAE,IAAI,KAAK;AAChB,sBAAU,kBAAkB,IAAI,CAAC,CAAC;AAAA,UACpC;AAAA,QACF,OAAO;AACL,iBAAO,EAAE,IAAI,KAAK;AAChB,gBAAI,SAAS,IAAI,CAAC;AAClB,sBAAU,kBAAkB,MAAM,IAC9B,kBAAkB,IAAI,MAAM,CAAC;AAAA,UACnC;AAAA,QACF;AACA,eAAO;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,kBAAkB,KAAK;AAC9B,MAAI,OAAO;AACX,QAAM,aAAa,GAAG;AACtB,SAAO,eAAe,GAAG,IAAI,MAAM,SAAS,GAAG,IAAI;AACrD;AAEA,SAAS,YAAY,KAAK,GAAG;AAC3B,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI,OAAO,IAAI,CAAC,MAAM;AACtB,MAAI,MAAM;AACR,UAAM;AACN;AAAA,EACF,OAAO;AACL,QAAI,MAAM,IAAI,CAAC,MAAM;AACrB;AACA,QAAI,cAAc;AAClB,QAAI,cAAc,IAAI,UAAU,GAAG,IAAI,gBAAgB;AACvD,QAAI,YAAY,SAAS,aAAa,EAAE,IAAI;AAE5C,QAAI,KAAK;AACP,kBAAY,CAAC;AAAA,IACf;AACA,SAAK;AACL,WAAO,MAAM;AACX,UAAI,KAAK,IAAI,CAAC;AACd,UAAI,OAAO,MAAU;AACnB;AAAA,MACF,OAAO;AACL,uBAAe;AAAA,MACjB;AACA;AAAA,IACF;AACA,kBAAc,YAAY,MAAM,GAAG;AACnC,QAAI,YAAY,WAAW,GAAG;AAC5B,YAAM,SAAS,aAAa,EAAE;AAAA,IAChC,OAAO;AAEL,YAAM,WAAW,YAAY,CAAC,IAAI,MAAM,YAAY,CAAC,CAAC;AAAA,IACxD;AAEA,QAAI,KAAK;AACP,YAAM,MAAM;AAAA,IACd;AAEA,QAAI,cAAc,GAAG;AAInB,YAAM,WAAW,MAAM,MAAM,SAAS;AAAA,IACxC;AAAA,EACF;AACA,SAAO,EAAC,KAAK,QAAS,IAAI,YAAW;AACvC;AAIA,SAAS,IAAI,OAAO,WAAW;AAC7B,MAAI,MAAM,MAAM,IAAI;AAEpB,MAAI,UAAU,QAAQ;AACpB,QAAI,kBAAkB,UAAU,UAAU,SAAS,CAAC;AACpD,QAAI,QAAQ,gBAAgB,SAAS;AAEnC,gBAAU,IAAI;AACd,wBAAkB,UAAU,UAAU,SAAS,CAAC;AAAA,IAClD;AACA,QAAI,UAAU,gBAAgB;AAC9B,QAAI,mBAAmB,gBAAgB;AACvC,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAQ,KAAK,GAAG;AAAA,IAClB,WAAW,qBAAqB,MAAM,SAAS,GAAG;AAChD,UAAI,MAAM,MAAM,IAAI;AACpB,cAAQ,GAAG,IAAI;AAAA,IACjB,OAAO;AACL,YAAM,KAAK,GAAG;AAAA,IAChB;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,KAAK;AACjC,MAAI,QAAQ,CAAC;AACb,MAAI,YAAY,CAAC;AACjB,MAAI,IAAI;AAGR,SAAO,MAAM;AACX,QAAIC,kBAAiB,IAAI,GAAG;AAC5B,QAAIA,oBAAmB,MAAU;AAC/B,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO,MAAM,IAAI;AAAA,MACnB,OAAO;AACL,YAAI,OAAO,SAAS;AACpB;AAAA,MACF;AAAA,IACF;AACA,YAAQA,iBAAgB;AAAA,MACtB,KAAK;AACH,cAAM,KAAK,IAAI;AACf;AAAA,MACF,KAAK;AACH,cAAM,KAAK,IAAI,CAAC,MAAM,GAAG;AACzB;AACA;AAAA,MACF,KAAK;AACH,YAAI,YAAY,YAAY,KAAK,CAAC;AAClC,cAAM,KAAK,UAAU,GAAG;AACxB,aAAK,UAAU;AACf;AAAA,MACF,KAAK;AACH,YAAI,YAAY;AAEhB,eAAO,MAAM;AACX,cAAI,KAAK,IAAI,CAAC;AACd,cAAI,OAAO,MAAU;AACnB;AAAA,UACF;AACA,uBAAa;AACb;AAAA,QACF;AAIA,oBAAY,UAAU,QAAQ,iBAAiB,IAAQ,EACpD,QAAQ,iBAAiB,GAAQ,EACjC,QAAQ,iBAAiB,GAAQ;AAEpC,cAAM,KAAK,SAAS;AACpB;AAAA,MACF,KAAK;AACH,YAAI,eAAe,EAAE,SAAS,CAAC,GAAG,OAAO,MAAM,OAAO;AACtD,cAAM,KAAK,aAAa,OAAO;AAC/B,kBAAU,KAAK,YAAY;AAC3B;AAAA,MACF,KAAK;AACH,YAAI,aAAa,EAAE,SAAS,CAAC,GAAG,OAAO,MAAM,OAAO;AACpD,cAAM,KAAK,WAAW,OAAO;AAC7B,kBAAU,KAAK,UAAU;AACzB;AAAA;AAAA,MAEF;AACE,cAAM,IAAI;AAAA,UACR,8DACEA;AAAA,QAAc;AAAA,IACtB;AAAA,EACF;AACF;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,MAAM,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM;AACrC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,OAAO,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC7B,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAQ,EAAE,WAAW,EAAE,SAAU,IAC9B,EAAE,SAAS,EAAE,SAAU,IAAI;AAChC;AACA,SAAS,cAAc,GAAG,GAAG;AAI3B,SAAQ,MAAM,IAAK,IAAM,IAAI,IAAK,IAAI;AACxC;AACA,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,KAAK,OAAO,KAAK,CAAC,GAAG,KAAK,OAAO,KAAK,CAAC;AAC3C,MAAI,MAAM,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM;AACvC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAE5B,QAAI,OAAO,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC/B,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AACjC,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AAAA,EAEF;AACA,SAAQ,GAAG,WAAW,GAAG,SAAU,IAChC,GAAG,SAAS,GAAG,SAAU,IAAI;AAClC;AAKA,SAAS,eAAe,GAAG;AACzB,MAAI,KAAK,CAAC,WAAW,UAAU,UAAU,QAAQ;AACjD,MAAI,MAAM,GAAG,QAAQ,OAAO,CAAC;AAE7B,MAAI,CAAC,KAAK;AACR,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT;AACA,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO,MAAM,IAAK,MAAM,IAAM,MAAM;AAAA,EACtC;AAEA,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO;AAAA,EACT;AACF;AAOA,SAAS,qBAAqB,KAAK;AAEjC,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AAIA,MAAI,YAAY,IAAI,cAAc,EAAE,MAAM,MAAM;AAChD,MAAI,YAAY,SAAS,UAAU,CAAC,GAAG,EAAE;AAEzC,MAAI,MAAM,MAAM;AAEhB,MAAI,SAAS,MAAM,MAAM;AAIzB,MAAI,oBAAqB,MAAM,CAAC,YAAY,aAAa;AACzD,MAAI,YAAY,QAAS,iBAAkB,SAAS,GAAG,KAAK,gBAAgB;AAE5E,YAAU,MAAM;AAGhB,MAAI,SAAS,KAAK,IAAI,WAAW,UAAU,CAAC,CAAC,CAAC;AAE9C,MAAI,KAAK;AACP,aAAS,KAAK;AAAA,EAChB;AAEA,MAAI,YAAY,OAAO,QAAQ,EAAE;AAGjC,cAAY,UAAU,QAAQ,UAAU,EAAE;AAE1C,YAAU,MAAM;AAEhB,SAAO;AACT;AAGA,SAAS,kBAAkB,MAAM;AAE/B,WAAS,sBAAsB,KAAK;AAClC,WAAO,KAAK,IAAI,SAAU,SAAS;AACjC,UAAI,YAAY,OAAO,OAAO;AAC9B,UAAI,cAAc,WAAW,SAAS;AACtC,UAAI,gBAAgB,gBAAgB,KAAK,WAAW;AACpD,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,SAAO,SAAU,MAAM,MAAM;AAC3B,QAAI,eAAe,sBAAsB,KAAK,GAAG;AACjD,QAAI,eAAe,sBAAsB,KAAK,GAAG;AACjD,QAAI,YAAY,QAAQ,cAAc,YAAY;AAClD,QAAI,cAAc,GAAG;AACnB,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG;AAAA,EAC3C;AACF;AAEA,SAAS,qBAAqB,MAAM,YAAY,gBAAgB;AAC9D,SAAO,KAAK,OAAO,SAAU,KAAK;AAChC,WAAO,UAAU,IAAI,KAAK,WAAW,UAAU,cAAc;AAAA,EAC/D,CAAC;AAED,MAAI,WAAW,MAAM;AAEnB,QAAI,cAAc,kBAAkB,WAAW,IAAI;AACnD,WAAO,KAAK,KAAK,WAAW;AAC5B,QAAI,OAAO,WAAW,KAAK,CAAC,MAAM,YAC9B,SAAS,WAAW,KAAK,CAAC,CAAC,MAAM,QAAQ;AAC3C,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,EACF;AAEA,MAAI,WAAW,cAAc,UAAU,YAAY;AAEjD,QAAI,OAAO,WAAW,QAAQ;AAC9B,QAAI,SAAS,WAAW,aAAa,WAAW,QAAQ,KAAK,UAAU;AACvE,WAAO,KAAK,MAAM,MAAM,KAAK;AAAA,EAC/B;AACA,SAAO;AACT;AAEA,SAAS,UAAU,KAAK,UAAU,gBAAgB;AAChD,SAAO,eAAe,MAAM,SAAU,OAAO;AAC3C,QAAI,UAAU,SAAS,KAAK;AAC5B,QAAI,cAAc,WAAW,KAAK;AAClC,QAAI,gBAAgB,gBAAgB,KAAK,WAAW;AACpD,QAAI,qBAAqB,KAAK,GAAG;AAC/B,aAAO,0BAA0B,OAAO,SAAS,GAAG;AAAA,IACtD;AAEA,WAAO,cAAc,SAAS,KAAK,aAAa,aAAa;AAAA,EAC/D,CAAC;AACH;AAEA,SAAS,cAAc,SAAS,KAAK,aAAa,eAAe;AAC/D,MAAI,CAAC,SAAS;AAEZ,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO,OAAO,KAAK,OAAO,EAAE,MAAM,SAAU,mBAAmB;AAC7D,UAAI,YAAY,QAAS,iBAAkB;AAE3C,UAAI,kBAAkB,QAAQ,GAAG,MAAM,GAAG;AACxC,eAAO,MAAM,mBAAmB,KAAK,WAAW,aAAa,aAAa;AAAA,MAC5E,OAAO;AACL,YAAI,iBAAiB,WAAW,iBAAiB;AAEjD,YACE,kBAAkB,UAClB,OAAO,cAAc,YACrB,eAAe,SAAS,GACxB;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,gBAAgB,eAAe,cAAc;AAEpE,YAAI,OAAO,cAAc,UAAU;AAEjC,iBAAO,cAAc,WAAW,KAAK,aAAa,gBAAgB;AAAA,QACpE;AAGA,eAAO,MAAM,OAAO,KAAK,WAAW,gBAAgB,gBAAgB;AAAA,MACtE;AAAA,IACF,CAAC;AAAA,EACH;AAGA,SAAO,YAAY;AACrB;AAEA,SAAS,0BAA0B,OAAO,SAAS,KAAK;AAEtD,MAAI,UAAU,OAAO;AACnB,WAAO,QAAQ,KAAK,SAAU,YAAY;AACxC,aAAO,UAAU,KAAK,YAAY,OAAO,KAAK,UAAU,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH;AAEA,MAAI,UAAU,QAAQ;AACpB,WAAO,CAAC,UAAU,KAAK,SAAS,OAAO,KAAK,OAAO,CAAC;AAAA,EACtD;AAGA,SAAO,CAAC,QAAQ,KAAK,SAAU,YAAY;AACzC,WAAO,UAAU,KAAK,YAAY,OAAO,KAAK,UAAU,CAAC;AAAA,EAC3D,CAAC;AAEH;AAEA,SAAS,MAAM,cAAc,KAAK,WAAW,aAAa,eAAe;AACvE,MAAI,CAAC,SAAS,YAAY,GAAG;AAE3B,UAAM,IAAI,MAAM,uBAAuB,eACrC,oIACiE;AAAA,EACrE;AACA,SAAO,SAAS,YAAY,EAAE,KAAK,WAAW,aAAa,aAAa;AAC1E;AAEA,SAAS,YAAY,eAAe;AAClC,SAAO,OAAO,kBAAkB,eAAe,kBAAkB;AACnE;AAEA,SAAS,oBAAoB,eAAe;AAC1C,SAAO,OAAO,kBAAkB;AAClC;AAEA,SAAS,SAAS,eAAe,WAAW;AAC1C,MAAI,OAAO,kBAAkB,YAC3B,SAAS,eAAe,EAAE,MAAM,eAAe;AAC/C,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,UAAU,CAAC;AACzB,MAAI,MAAM,UAAU,CAAC;AAErB,SAAO,gBAAgB,YAAY;AACrC;AAEA,SAAS,mBAAmB,eAAe,WAAW;AACpD,SAAO,UAAU,KAAK,SAAU,KAAK;AACnC,QAAI,yBAAyB,OAAO;AAClC,aAAO,cAAc,KAAK,SAAU,mBAAmB;AACrD,eAAO,QAAQ,KAAK,iBAAiB,MAAM;AAAA,MAC7C,CAAC;AAAA,IACH;AAEA,WAAO,QAAQ,KAAK,aAAa,MAAM;AAAA,EACzC,CAAC;AACH;AAEA,SAAS,uBAAuB,eAAe,WAAW;AACxD,SAAO,UAAU,MAAM,SAAU,KAAK;AACpC,WAAO,cAAc,KAAK,SAAU,mBAAmB;AACrD,aAAO,QAAQ,KAAK,iBAAiB,MAAM;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,UAAU,eAAe,WAAW;AAC3C,SAAO,cAAc,WAAW;AAClC;AAEA,SAAS,WAAW,eAAe,WAAW;AAC5C,MAAI,KAAK,IAAI,OAAO,SAAS;AAE7B,SAAO,GAAG,KAAK,aAAa;AAC9B;AAEA,SAAS,UAAU,eAAe,WAAW;AAE3C,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,aAAO,kBAAkB;AAAA,IAC3B,KAAK;AACH,aAAO,OAAQ,kBAAmB;AAAA,IACpC,KAAK;AACH,aAAO,OAAQ,kBAAmB;AAAA,IACpC,KAAK;AACH,aAAO,OAAQ,kBAAmB;AAAA,IACpC,KAAK;AACH,aAAO,yBAAyB;AAAA,IAClC,KAAK;AACH,aAAQ,CAAC,EAAG,SAAS,KAAK,aAAa,MAAM;AAAA,EACjD;AACF;AAEA,IAAI,WAAW;AAAA,EAEb,cAAc,SAAU,KAAK,WAAW,aAAa,eAAe;AAClE,QAAI,CAAC,MAAM,QAAQ,aAAa,GAAG;AACjC,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,cAAc,CAAC,MAAM,YAAa,cAAc,CAAC,MAAM,MAAM;AACtE,aAAO,cAAc,KAAK,SAAU,KAAK;AACvC,eAAO,UAAU,KAAK,WAAW,OAAO,KAAK,SAAS,CAAC;AAAA,MACzD,CAAC;AAAA,IACH;AAEA,WAAO,cAAc,KAAK,SAAU,KAAK;AACvC,aAAO,cAAc,WAAW,KAAK,aAAa,GAAG;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,aAAa,SAAU,KAAK,WAAW,aAAa,eAAe;AACjE,QAAI,CAAC,MAAM,QAAQ,aAAa,GAAG;AACjC,aAAO;AAAA,IACT;AAGA,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,cAAc,CAAC,MAAM,YAAa,cAAc,CAAC,MAAM,MAAM;AACtE,aAAO,cAAc,MAAM,SAAU,KAAK;AACxC,eAAO,UAAU,KAAK,WAAW,OAAO,KAAK,SAAS,CAAC;AAAA,MACzD,CAAC;AAAA,IACH;AAEA,WAAO,cAAc,MAAM,SAAU,KAAK;AACxC,aAAO,cAAc,WAAW,KAAK,aAAa,GAAG;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,MAAM;AAAA,EACrF;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,KAAK;AAAA,EACpF;AAAA,EAEA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,IAAI;AAAA,EACnF;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,KAAK;AAAA,EACpF;AAAA,EAEA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,oBAAoB,aAAa,KAAK,QAAQ,eAAe,SAAS,IAAI;AAAA,EACnF;AAAA,EAEA,WAAW,SAAU,KAAK,WAAW,aAAa,eAAe;AAE/D,QAAI,WAAW;AACb,aAAO,oBAAoB,aAAa;AAAA,IAC1C;AAEA,WAAO,CAAC,oBAAoB,aAAa;AAAA,EAC3C;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,YAAY,aAAa,KAAK,SAAS,eAAe,SAAS;AAAA,EACxE;AAAA,EAEA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,UAAU,MAAM,SAAU,SAAS;AACxC,aAAO,QAAQ,eAAe,OAAO,MAAM;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAU,KAAK,WAAW,aAAa,eAAe;AAC3D,WAAO,YAAY,aAAa,KAAK,mBAAmB,eAAe,SAAS;AAAA,EAClF;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,YAAY,aAAa,KAAK,CAAC,mBAAmB,eAAe,SAAS;AAAA,EACnF;AAAA,EAEA,SAAS,SAAU,KAAK,WAAW,aAAa,eAAe;AAC7D,WAAO,YAAY,aAAa,KAC9B,MAAM,QAAQ,aAAa,KAC3B,UAAU,eAAe,SAAS;AAAA,EACtC;AAAA,EAEA,QAAQ,SAAU,KAAK,WAAW,aAAa,eAAe;AAC5D,WAAO,MAAM,QAAQ,aAAa,KAAK,uBAAuB,eAAe,SAAS;AAAA,EACxF;AAAA,EAEA,UAAU,SAAU,KAAK,WAAW,aAAa,eAAe;AAC9D,WAAO,YAAY,aAAa,KAC9B,OAAO,iBAAiB,YACxB,UAAU,MAAM,SAAU,YAAY;AACpC,aAAO,WAAW,eAAe,UAAU;AAAA,IAC7C,CAAC;AAAA,EACL;AAAA,EAEA,SAAS,SAAU,KAAK,WAAW,aAAa,eAAe;AAC7D,WAAO,UAAU,eAAe,SAAS;AAAA,EAC3C;AACF;AAGA,SAAS,gBAAgB,KAAK,UAAU;AAEtC,MAAI,OAAO,aAAa,UAAU;AAEhC,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC1D;AAEA,aAAW,gBAAgB,QAAQ;AACnC,MAAI,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,cAAc,qBAAqB,CAAC,GAAG,GAAG,EAAE,SAAS,GAAG,OAAO,KAAK,QAAQ,CAAC;AACjF,SAAO,eAAe,YAAY,WAAW;AAC/C;AAEA,SAAS,WAAW,OAAO;AACzB,SAAO,UAAU,2BAA2B,QAAQ,KAAK,CAAC,CAAC;AAC7D;AAEA,SAAS,SAAS,OAAO;AACvB,MAAI,OAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,kBAAkB,QAAQ;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,KAAK,IAAI;AAEX,SAAO,UAAU,MAAM,CAAC,CAAC;AAC3B;AAEA,SAAS,SAAS,MAAM,UAAU;AAChC,MAAI,KAAK,UAAU;AACjB,QAAI,KAAK,UAAU,KAAK,WAAW,aAAa;AAC9C,UAAI,aAAa,OAAO,KAAK,WAAW,WACtC,KAAK,SAAS;AAChB,aAAO,SAAS,IAAI,MAAM,kCAAkC,aAAa,GAAG,CAAC;AAAA,IAC/E;AAAA,EACF;AACA,WAAS;AACX;AAEA,SAAS,UAAU,MAAM;AACvB,MAAI,KAAK,QAAQ,CAAC,KAAK,QAAQ;AAC7B,SAAK,SAAS;AAAA,EAChB;AAEA,MAAI,KAAK,YAAY,CAAC,KAAK,QAAQ;AACjC,SAAK,SAAS;AAAA,EAChB;AAEA,MAAI,KAAK,UAAU,OAAO,KAAK,WAAW,UAAU;AAClD,QAAI,KAAK,WAAW,SAAS;AAC3B,WAAK,OAAO,+BAA+B,KAAK,IAAI;AAAA,IACtD,OAAO;AACL,WAAK,SAAS,+BAA+B,KAAK,MAAM;AAAA,IAC1D;AAAA,EACF;AACF;AAEA,SAAS,aAAaC,iBAAgB,MAAM;AAC1C,SAAO,KAAK,UAAU,OAAO,KAAK,WAAW,YAC3C,CAAC,KAAK,WAAW,CAAC,SAASA,gBAAe,EAAE;AAChD;AAEA,SAAS,OAAOA,iBAAgB,MAAM;AACpC,MAAI,WAAW,KAAK;AACpB,MAAI,KAAK,WAAW,SAAS;AAC3B,QAAI,CAAC,KAAK,QAAQ,OAAO,KAAK,SAAS,UAAU;AAC/C,UAAI,MAAM;AAAA,QAAY;AAAA,QACpB;AAAA,MAA+C;AACjD,aAAO,SAAS,GAAG;AAAA,IACrB;AAEA,QAAI,WAAW,2BAA2B,KAAK,IAAI;AACnD,IAAAA,gBAAe,GAAG,IAAI,aAAa,SAAS,CAAC,GAAG,SAAUX,MAAK,MAAM;AAEnE,UAAIW,gBAAe,aAAa;AAC9B,eAAO,SAAS,MAAM,EAAC,QAAQ,YAAW,CAAC;AAAA,MAC7C;AAEA,UAAIX,MAAK;AACP,eAAO,SAAS,0BAA0BA,IAAG,CAAC;AAAA,MAChD;AACA,UAAI,SAAS,QAAQ,KAAK,SAAS,KAAK,MAAM,SAAS,CAAC,CAAC,KACvD,KAAK,MAAM,SAAS,CAAC,CAAC,EAAE;AAC1B,UAAI,CAAC,QAAQ;AACX,eAAO,SAAS;AAAA,UAAY;AAAA,UACzB,KAAK,QAAQ,uBAAuB,SAAS,CAAC,IAC7C;AAAA,QAA0B,CAAC;AAAA,MACjC;AACA,WAAK,SAAS,SAAS,MAAM;AAC7B,MAAAW,gBAAe,UAAU,IAAI;AAAA,IAC/B,CAAC;AAAA,EACH,WAAW,KAAK,UAAU;AACxB,SAAK,SAAS,SAAU,KAAK;AAC3B,aAAO,gBAAgB,KAAK,KAAK,QAAQ;AAAA,IAC3C;AACA,IAAAA,gBAAe,UAAU,IAAI;AAAA,EAC/B,OAAO;AAEL,QAAI,aAAa,2BAA2B,KAAK,MAAM;AACvD,IAAAA,gBAAe,GAAG,IAAI,aAAa,WAAW,CAAC,GAAG,SAAUX,MAAK,MAAM;AAErE,UAAIW,gBAAe,aAAa;AAC9B,eAAO,SAAS,MAAM,EAAC,QAAQ,YAAW,CAAC;AAAA,MAC7C;AAEA,UAAIX,MAAK;AACP,eAAO,SAAS,0BAA0BA,IAAG,CAAC;AAAA,MAChD;AACA,UAAI,YAAY,QAAQ,KAAK,WAAW,KAAK,QAAQ,WAAW,CAAC,CAAC;AAClE,UAAI,CAAC,WAAW;AACd,eAAO,SAAS;AAAA,UAAY;AAAA,UACxB,QAAQ,KAAK,UAAW,uBAAuB,WAAW,CAAC,IACzD;AAAA,QAA4B,CAAC;AAAA,MACrC;AACA,WAAK,SAAS,WAAW,SAAS;AAClC,MAAAW,gBAAe,UAAU,IAAI;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAEA,SAAS,yBAAyBT,UAAS;AACzC,EAAAA,SAAQ,uBAAuB;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,QAAQ,OAAO,wBAAwB;AAEvC,QAAQ,UAAU;AAkBlB,SAAS,iBAAiB,KAAK,OAAO,kBAAkB;AACtD,SAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,QAAI,UAAU,WAAW,CAAC,EAAE,CAAC;AAE7B,QAAI;AACJ,QAAI,OAAO,qBAAqB,YAAY;AAG1C,YAAM,YAAY;AAClB,YAAM,MAAM,UAAU,OAAO;AAC7B,YAAM,IAAI,YAAY,KAAK,EAAE,IAAI,GAAG;AAAA,IACtC,OAAO;AACL,YAAM,QAAQ;AACd,YAAM,IAAI,YAAY,KAAK,EAAE,IAAI,SAAS,KAAK;AAAA,IACjD;AAEA,QAAI,YAAY,WAAY;AAC1B,UAAI,gBAAgB,UAAU,UAAU,MAAM,eAAe;AAC7D,UAAI,cAAc,UAAU,UAAU,MAAM,QAAQ;AAGpD,cAAQ,eAAe,CAAC,iBACtB,SAAS,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE;AAAA,IACxC;AAEA,QAAI,UAAU,IAAI,UAAU,SAAU,GAAG;AAGvC,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAClB,cAAQ,KAAK;AAAA,IACf;AAAA,EACF,CAAC,EAAE,MAAM,WAAY;AACnB,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,MAAM,OAAO,SAAU,KAAK,MAAM;AACvC,QAAI,IAAI,IAAI;AACZ,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,IAAI,gBAAgB,SAAS;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AACF,CAAC;AAGD,IAAI,YAAY,SAAS;AAAA,EACvB;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,SAAS,kBAAkB,QAAQ;AACjC,MAAI,CAAC,QAAQ,KAAK,MAAM,GAAG;AACzB,WAAO,YAAY,WAAW;AAAA,EAChC;AACA,MAAI,MAAM,OAAO,QAAQ,GAAG;AAC5B,MAAI,OAAO,OAAO,UAAU,GAAG,GAAG;AAClC,MAAI,QAAQ,OAAO,UAAU,MAAM,CAAC;AACpC,SAAO;AAAA,IACL,QAAQ,SAAS,MAAM,EAAE;AAAA,IACzB,IAAI;AAAA,EACN;AACF;AAEA,SAAS,yBAAyB,WAAW,MAAM;AACjD,MAAI,MAAM,UAAU,QAAQ,UAAU,IAAI,SAAS;AAEnD,MAAI,cAAc,UAAU;AAC5B,MAAI,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC,CAAC;AAEnC,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,UAAM,CAAC,YAAY,CAAC,GAAG,EAAC,QAAQ,UAAS,GAAG,CAAC,GAAG,CAAC;AAAA,EACnD;AAEA,SAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,SAAS,KAAK,UAAU,QAAQ;AACvC,MAAI,CAAC,QAAQ;AACX,aAAS;AAAA,MACP,oBAAoB;AAAA,IACtB;AAAA,EACF;AAEA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,EAAC,QAAQ,YAAW;AAC/B,MAAI,IAAI,UAAU;AAChB,SAAK,UAAU;AAAA,EACjB;AAEA,MAAI,UAAU;AACZ,QAAI,CAAC,IAAI,KAAK;AACZ,UAAI,MAAM,KAAK;AAAA,IACjB;AACA,eAAW,IAAI,KAAK,OAAO,kBAAkB;AAC7C,QAAI,IAAI,MAAM;AACZ,gBAAU,kBAAkB,IAAI,IAAI;AACpC,UAAI,QAAQ,OAAO;AACjB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,CAAC;AAAA,QACf,KAAK,QAAQ;AAAA,QACb,KAAK,CAAC,QAAQ,IAAI,EAAC,QAAQ,UAAS,GAAG,CAAC,CAAC,UAAU,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,MAC/D,CAAC;AACD,gBAAU,QAAQ,SAAS;AAAA,IAC7B,OAAO;AACL,UAAI,YAAY,CAAC;AAAA,QACf,KAAK;AAAA,QACL,KAAM,CAAC,UAAU,MAAM,CAAC,CAAC;AAAA,MAC3B,CAAC;AACD,gBAAU;AAAA,IACZ;AAAA,EACF,OAAO;AACL,QAAI,IAAI,YAAY;AAClB,UAAI,YAAY,yBAAyB,IAAI,YAAY,IAAI;AAC7D,gBAAU,IAAI,WAAW;AACzB,iBAAW,IAAI,WAAW,IAAI,CAAC;AAAA,IACjC;AACA,QAAI,CAAC,IAAI,WAAW;AAClB,gBAAU,kBAAkB,IAAI,IAAI;AACpC,UAAI,QAAQ,OAAO;AACjB,eAAO;AAAA,MACT;AACA,gBAAU,QAAQ;AAClB,iBAAW,QAAQ;AACnB,UAAI,YAAY,CAAC;AAAA,QACf,KAAK;AAAA,QACL,KAAK,CAAC,UAAU,MAAM,CAAC,CAAC;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAEA,iBAAe,IAAI,GAAG;AAEtB,MAAI,OAAO,UAAU,MAAM;AAE3B,MAAI,SAAS,EAAC,UAAW,CAAC,GAAG,MAAO,CAAC,EAAC;AACtC,WAAS,OAAO,KAAK;AAEnB,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAClD,UAAI,aAAa,IAAI,CAAC,MAAM;AAC5B,UAAI,cAAc,CAAC,cAAc,GAAG,GAAG;AACrC,YAAI,QAAQ,YAAY,gBAAgB,GAAG;AAC3C,cAAM,UAAU,eAAe,UAAU,OAAO;AAChD,cAAM;AAAA,MACR,WAAW,cAAc,CAAC,UAAU,GAAG,GAAG;AACxC,eAAO,SAAS,IAAI,MAAM,CAAC,CAAC,IAAI,IAAI,GAAG;AAAA,MACzC,OAAO;AACL,eAAO,KAAK,GAAG,IAAI,IAAI,GAAG;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,YAAY,MAAM;AACzB,MAAI;AACF,WAAO,SAAS,IAAI;AAAA,EACtB,SAAS,GAAG;AACV,QAAI,MAAM;AAAA,MAAY;AAAA,MACpB;AAAA,IAAyC;AAC3C,WAAO,EAAC,OAAO,IAAG;AAAA,EACpB;AACF;AAEA,SAAS,iBAAiB,KAAK,UAAU,UAAU;AACjD,MAAI,WAAW,YAAY,IAAI,IAAI;AACnC,MAAI,SAAS,OAAO;AAClB,WAAO,SAAS,SAAS,KAAK;AAAA,EAChC;AAEA,MAAI,SAAS,SAAS;AACtB,MAAI,aAAa,QAAQ;AACvB,QAAI,OAAO,mBAAmB,UAAU,IAAI,YAAY;AAAA,EAC1D,WAAW,aAAa,UAAU;AAChC,QAAI,OAAO,SAAS,QAAQ;AAAA,EAC9B,OAAO;AACL,QAAI,OAAO;AAAA,EACb;AACA,YAAU,UAAU,SAAU,QAAQ;AACpC,QAAI,SAAS,SAAS;AACtB,aAAS;AAAA,EACX,CAAC;AACH;AAEA,SAAS,eAAe,KAAK,UAAU,UAAU;AAC/C,YAAU,IAAI,MAAM,SAAU,KAAK;AACjC,QAAI,SAAS,SAAS;AAEtB,QAAI,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAK,UAAU;AACjD,QAAI,aAAa,UAAU;AACzB,yBAAmB,IAAI,MAAM,SAAU,WAAW;AAChD,YAAI,OAAO;AACX,iBAAS;AAAA,MACX,CAAC;AAAA,IACH,WAAW,aAAa,UAAU;AAChC,mBAAa,IAAI,MAAM,SAAU,KAAK;AACpC,YAAI,OAAO;AACX,iBAAS;AAAA,MACX,CAAC;AAAA,IACH,OAAO;AACL,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH;AAEA,SAAS,qBAAqB,KAAK,UAAU,UAAU;AACrD,MAAI,IAAI,MAAM;AACZ,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO,IAAI,SAAS,UAAU;AAChC,qBAAiB,KAAK,UAAU,QAAQ;AAAA,EAC1C,OAAO;AACL,mBAAe,KAAK,UAAU,QAAQ;AAAA,EACxC;AACF;AAEA,SAAS,sBAAsB,UAAU,UAAU,UAAU;AAE3D,MAAI,CAAC,SAAS,QAAQ;AACpB,WAAO,SAAS;AAAA,EAClB;AAEA,MAAI,OAAO;AACX,MAAI;AAEJ,WAAS,QAAQ,SAAU,SAAS;AAClC,QAAI,cAAc,QAAQ,QAAQ,QAAQ,KAAK,eAC7C,OAAO,KAAK,QAAQ,KAAK,YAAY,IAAI,CAAC;AAC5C,QAAI,OAAO;AAEX,QAAI,CAAC,YAAY,QAAQ;AACvB,aAAO,KAAK;AAAA,IACd;AAEA,aAAS,oBAAoB,KAAK;AAChC,mBAAa;AACb;AACA,UAAI,SAAS,YAAY,QAAQ;AAC/B,aAAK;AAAA,MACP;AAAA,IACF;AAEA,aAAS,OAAO,QAAQ,KAAK,cAAc;AACzC,UAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,KAAK,cAAc,GAAG,GAAG;AACxE;AAAA,UAAqB,QAAQ,KAAK,aAAa,GAAG;AAAA,UAChD;AAAA,UAAU;AAAA,QAAmB;AAAA,MACjC;AAAA,IACF;AAAA,EACF,CAAC;AAED,WAAS,OAAO;AACd;AACA,QAAI,SAAS,WAAW,MAAM;AAC5B,UAAI,YAAY;AACd,iBAAS,UAAU;AAAA,MACrB,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,UAAU,UAAU,MAAM,SAAS,SACzB,GAAG,IAAI,UAAU,UAAU;AAE5C,MAAI,UAAU,KAAK,UAAU,QAAQ,SAAS,GAAG,KAAK,CAAC,UAAU;AAC/D,YAAQ,CAAC,IAAI;AACb,WAAO,GAAG;AAAA,EACZ;AAGA,MAAI,qBAAqB,KAAK,cAAc,WAAW,IAAI;AAC3D,MAAI,oBAAoB,aAAa,OAAO,KAAK,UAC/C,UAAU,MAAM,kBAAkB;AACpC,MAAI,UAAU,aAAa,QAAQ,WAAW,QAAQ,SAAS,UAC7D,UAAU,QAAQ,QAAQ;AAC5B,MAAI,SAAS,MAAM,KAAK,QAAQ,SAAS,GAAG;AAE5C,MAAI,qBAAqB,CAAC,WAAW,YAAY,QAAQ;AACvD,QAAI,SAAS,QAAQ;AACrB,WAAO,OAAO;AACd,WAAO,MAAM,QAAQ,SAAS;AAC9B,cAAU,SAAS,QAAQ,QAAQ;AAAA,EACrC;AAEA,MAAI,SAAS,MAAM,KAAK,UAAU,QAAQ,SAAS,SAAS,CAAC,GAAG,QAAQ;AAExE,MAAI,aAAa,aACd,qBAAqB,WAAW,OAAO,cAAc,cACrD,CAAC,qBAAqB,OAAO,cAAc,cAC3C,qBAAqB,CAAC,WAAW,OAAO,cAAc;AAEzD,MAAI,YAAY;AACd,QAAI,MAAM,YAAY,YAAY;AAClC,YAAQ,CAAC,IAAI;AACb,WAAO,GAAG;AAAA,EACZ;AAEA,MAAI,SAAS,QAAQ,SAAS;AAC9B,UAAQ,SAAS,WAAW,OAAO;AACnC,UAAQ,cAAc,OAAO,eAAe,CAAC;AAE7C,MAAI,KAAK,SAAS;AAChB,YAAQ,SAAS,UAAU,KAAK;AAAA,EAClC;AAGA,MAAI,gBAAgB,WAAW,QAAQ,QAAQ;AAC/C,MAAI,sBAAsB,UAAU,QAAQ,UAAU,aAAa;AAInE,MAAI,QAAS,sBAAsB,sBAAuB,IACxD,oBAAoB,sBAAsB,KAAK;AAEjD,MAAI;AACJ,MAAI,WAAW,eAAe;AAE5B,sBAAkB;AAAA,EACpB,OAAO;AAEL,sBAAkB,UAAU,QAAQ,UAAU,MAAM;AAAA,EACtD;AAEA;AAAA,IAAS;AAAA,IAAS;AAAA,IAAe;AAAA,IAAqB;AAAA,IACpD;AAAA,IAAM;AAAA,IAAO;AAAA,IAAG;AAAA,EAAE;AACtB;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,QAAQ,SAAS,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE,WAAW;AACxD;AAEA,SAAS,YAAY,UAAU,UAAU,KAAK,aAAa,IAAI,SAC1C,UAAU,MAAM,iBAAiB;AAGpD,aAAW,YAAY;AAEvB,WAAS,UAAU,SAAS,YAAY,UAAU;AAEhD,QAAI,gBAAgB,WAAW,QAAQ,QAAQ;AAC/C,QAAI,UAAU,UAAU,QAAQ,UAAU,aAAa;AACvD,QAAI,gBAAgB,QAAQ,SAAS;AACnC,cAAQ,UAAU,IAAI,YAAY,aAAa,SAAS;AACxD,aAAO,SAAS;AAAA,IAClB;AAGA,QAAI,aAAa,YAAY,cAAc,OAAO;AAElD,QAAI,YAAY;AACd,UAAI,MAAM,YAAY,YAAY;AAClC,cAAQ,UAAU,IAAI;AACtB,aAAO,SAAS;AAAA,IAClB;AAEA,QAAI,QAAQ,UAAU,IAAI;AAE1B;AAAA,MAAS;AAAA,MAAS;AAAA,MAAe;AAAA,MAAS;AAAA,MAAS;AAAA,MACjD;AAAA,MAAO;AAAA,MAAY;AAAA,IAAQ;AAAA,EAC/B;AAEA,MAAI,WAAW,KAAK;AACpB,MAAI,YAAY,oBAAI,IAAI;AAExB,MAAI,WAAW;AACf,MAAI,WAAW,SAAS;AAExB,WAAS,mBAAmB;AAC1B,QAAI,EAAE,aAAa,YAAY,iBAAiB;AAC9C,sBAAgB;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,QAAQ,SAAU,YAAY,YAAY;AAEjD,QAAI,WAAW,OAAO,UAAU,WAAW,GAAG,GAAG;AAC/C,UAAI,MAAM,WAAW,WAAW,iBAAiB;AACjD,UAAI,GAAG,EAAE,YAAY,EAAC,KAAK,GAAE,GAAG,SAAU,KAAK,KAAK;AAClD,gBAAQ,UAAU,IAAI,OAAO;AAC7B,yBAAiB;AAAA,MACnB,CAAC;AACD;AAAA,IACF;AAEA,QAAI,KAAK,WAAW,SAAS;AAC7B,QAAI,UAAU,IAAI,EAAE,GAAG;AACrB;AACA,gBAAU,IAAI,EAAE,EAAE,KAAK,CAAC,YAAY,UAAU,CAAC;AAAA,IACjD,OAAO;AACL,gBAAU,IAAI,IAAI,CAAC,CAAC,YAAY,UAAU,CAAC,CAAC;AAAA,IAC9C;AAAA,EACF,CAAC;AAID,YAAU,QAAQ,SAAU,MAAM,IAAI;AACpC,QAAI,UAAU;AAEd,aAAS,aAAa;AACpB,UAAI,EAAE,UAAU,KAAK,QAAQ;AAC3B,gBAAQ;AAAA,MACV,OAAO;AACL,yBAAiB;AAAA,MACnB;AAAA,IACF;AACA,aAAS,UAAU;AACjB,UAAI,QAAQ,KAAK,OAAO;AACxB,UAAI,aAAa,MAAM,CAAC;AACxB,UAAI,aAAa,MAAM,CAAC;AAExB,UAAI,YAAY,IAAI,EAAE,GAAG;AACvB;AAAA,UAAU;AAAA,UAAU,YAAY,IAAI,EAAE;AAAA,UAAG;AAAA,UAAY;AAAA,UACnD;AAAA,UAAY;AAAA,UAAY;AAAA,UAAU;AAAA,QAAQ;AAAA,MAC9C,OAAO;AAEL,YAAI,SAAS,MAAM,CAAC,GAAG,WAAW,SAAS,SAAS,CAAC,GAAG,QAAQ;AAChE,mBAAW,SAAS,WAAW,OAAO;AACtC,mBAAW,cAAc,OAAO,eAAe,CAAC;AAChD,kBAAU,YAAY,YAAY,UAAU;AAAA,MAC9C;AAAA,IACF;AACA,YAAQ;AAAA,EACV,CAAC;AACH;AAIA,IAAI,kBAAkB;AAKtB,IAAI,YAAY;AAGhB,IAAI,eAAe;AAEnB,IAAI,eAAe;AAGnB,IAAI,uBAAuB;AAI3B,IAAI,aAAa;AAEjB,IAAI,cAAc;AAElB,IAAI,4BAA4B;AAEhC,SAAS,cAAc,KAAK;AAI1B,MAAI;AACF,WAAO,KAAK,MAAM,GAAG;AAAA,EACvB,SAAS,GAAG;AAEV,WAAO,gBAAAU,QAAS,MAAM,GAAG;AAAA,EAC3B;AACF;AAEA,SAAS,kBAAkB,MAAM;AAC/B,MAAI;AACF,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B,SAAS,GAAG;AAEV,WAAO,gBAAAA,QAAS,UAAU,IAAI;AAAA,EAChC;AACF;AAEA,SAAS,SAAS,UAAU;AAC1B,SAAO,SAAU,KAAK;AACpB,QAAI,UAAU;AACd,QAAI,IAAI,UAAU,IAAI,OAAO,OAAO;AAClC,gBAAU,IAAI,OAAO,MAAM,QAAQ,IAAI,OAAO,MAAM;AAAA,IACtD;AACA,aAAS,YAAY,WAAW,SAAS,IAAI,IAAI,CAAC;AAAA,EACpD;AACF;AASA,SAAS,eAAe,UAAUC,aAAY,SAAS;AACrD,SAAO;AAAA,IACL,MAAM,kBAAkB,QAAQ;AAAA,IAChC,YAAAA;AAAA,IACA,gBAAgB,UAAU,MAAM;AAAA,IAChC,KAAK,SAAS;AAAA;AAAA,IACd,IAAI,SAAS;AAAA,EACf;AACF;AAEA,SAAS,eAAe,cAAc;AACpC,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,MAAI,WAAW,cAAc,aAAa,IAAI;AAC9C,WAAS,aAAa,aAAa;AACnC,WAAS,UAAU,aAAa,mBAAmB;AACnD,WAAS,MAAM,aAAa;AAC5B,SAAO;AACT;AAIA,SAAS,UAAU,KAAK;AACtB,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,MAAI,MAAM,IAAI,YAAY,YAAY,GAAG;AACzC,MAAI,MAAM,IAAI,YAAY,UAAU,GAAG,MAAM,CAAC;AAC9C,MAAI,OAAO,IAAI,YAAY,UAAU,MAAM,CAAC;AAC5C,SAAO,IAAI;AACX,SAAO;AACT;AAKA,SAAS,aAAa,MAAM,MAAM,QAAQ,UAAU;AAClD,MAAI,QAAQ;AACV,QAAI,CAAC,MAAM;AACT,eAAS,WAAW,CAAC,EAAE,GAAG,EAAC,KAAI,CAAC,CAAC;AAAA,IACnC,WAAW,OAAO,SAAS,UAAU;AACnC,eAAS,IAAI;AAAA,IACf,OAAO;AACL,eAAS,aAAa,MAAM,IAAI,CAAC;AAAA,IACnC;AAAA,EACF,OAAO;AACL,QAAI,CAAC,MAAM;AACT,eAAS,EAAE;AAAA,IACb,WAAW,OAAO,SAAS,UAAU;AACnC,yBAAmB,MAAM,SAAU,QAAQ;AACzC,iBAAS,SAAS,MAAM,CAAC;AAAA,MAC3B,CAAC;AAAA,IACH,OAAO;AACL,eAAS,IAAI;AAAA,IACf;AAAA,EACF;AACF;AAEA,SAAS,4BAA4B,KAAK,MAAM,KAAK,IAAI;AACvD,MAAI,cAAc,OAAO,KAAK,IAAI,gBAAgB,CAAC,CAAC;AACpD,MAAI,CAAC,YAAY,QAAQ;AACvB,WAAO,MAAM,GAAG;AAAA,EAClB;AACA,MAAI,UAAU;AAEd,WAAS,YAAY;AACnB,QAAI,EAAE,YAAY,YAAY,UAAU,IAAI;AAC1C,SAAG;AAAA,IACL;AAAA,EACF;AAEA,WAAS,gBAAgBC,MAAK,KAAK;AACjC,QAAI,SAASA,KAAI,aAAa,GAAG;AACjC,QAAI,SAAS,OAAO;AACpB,QAAI,MAAM,IAAI,YAAY,YAAY,EAAE,IAAI,MAAM;AAClD,QAAI,YAAY,SAAU,GAAG;AAC3B,aAAO,OAAO,EAAE,OAAO,OAAO;AAC9B,gBAAU;AAAA,IACZ;AAAA,EACF;AAEA,cAAY,QAAQ,SAAU,KAAK;AACjC,QAAI,KAAK,eAAe,KAAK,cAAc;AACzC,sBAAgB,KAAK,GAAG;AAAA,IAC1B,OAAO;AACL,UAAI,aAAa,GAAG,EAAE,OAAO;AAC7B,gBAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACH;AAMA,SAAS,uBAAuB,SAAS,QAAQ;AAC/C,SAAO,QAAQ,IAAI,QAAQ,IAAI,SAAU,KAAK;AAC5C,QAAI,IAAI,OAAO,IAAI,IAAI,cAAc;AACnC,UAAI,WAAW,OAAO,KAAK,IAAI,IAAI,YAAY;AAC/C,aAAO,QAAQ,IAAI,SAAS,IAAI,SAAU,KAAK;AAC7C,YAAI,SAAS,IAAI,IAAI,aAAa,GAAG;AACrC,YAAI,EAAE,UAAU,SAAS;AACvB;AAAA,QACF;AACA,YAAI,OAAO,OAAO;AAClB,YAAI,OAAO,OAAO;AAClB,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,uBAAa,MAAM,MAAM,QAAQ,SAAU,MAAM;AAC/C,gBAAI,IAAI,aAAa,GAAG,IAAI,OAAO;AAAA,cACjC,KAAK,QAAQ,CAAC,UAAU,cAAc,CAAC;AAAA,cACvC,EAAC,KAAI;AAAA,YACP;AACA,oBAAQ;AAAA,UACV,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,SAAS,YAAY,MAAM,OAAO,KAAK;AAErC,MAAI,0BAA0B,CAAC;AAC/B,MAAI,WAAW,IAAI,YAAY,YAAY;AAC3C,MAAI,WAAW,IAAI,YAAY,YAAY;AAC3C,MAAI,iBAAiB,IAAI,YAAY,oBAAoB;AACzD,MAAI,QAAQ,KAAK;AAEjB,WAAS,YAAY;AACnB;AACA,QAAI,CAAC,OAAO;AACV,gCAA0B;AAAA,IAC5B;AAAA,EACF;AAEA,WAAS,4BAA4B;AACnC,QAAI,CAAC,wBAAwB,QAAQ;AACnC;AAAA,IACF;AACA,4BAAwB,QAAQ,SAAU,QAAQ;AAChD,UAAI,WAAW,eAAe,MAAM,WAAW,EAAE;AAAA,QAC/C,YAAY;AAAA,UACV,SAAS;AAAA,UAAM,SAAS;AAAA,UAAY;AAAA,UAAO;AAAA,QAAK;AAAA,MAAC;AACrD,eAAS,YAAY,SAAU,GAAG;AAChC,YAAIC,SAAQ,EAAE,OAAO;AACrB,YAAI,CAACA,QAAO;AAEV,mBAAS,OAAO,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,OAAK,QAAQ,SAAU,QAAQ;AAC7B,QAAI,QAAQ,SAAS,MAAM,aAAa;AACxC,QAAI,MAAM,QAAQ,OAAO;AACzB,UAAM,OAAO,GAAG,EAAE,YAAY,SAAU,GAAG;AACzC,UAAI,MAAM,EAAE,OAAO;AACnB,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,UAAU;AAAA,MACnB;AACA,eAAS,OAAO,GAAG;AAEnB,UAAI,SAAS,eAAe,MAAM,KAAK,EACpC,WAAW,YAAY,KAAK,GAAG,CAAC;AAEnC,aAAO,YAAY,SAAU,OAAO;AAClC,YAAIC,UAAS,MAAM,OAAO;AAC1B,YAAIA,SAAQ;AACV,cAAI,SAASA,QAAO,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC;AACjD,kCAAwB,KAAK,MAAM;AACnC,yBAAe,OAAOA,QAAO,UAAU;AACvC,UAAAA,QAAO,SAAS;AAAA,QAClB,OAAO;AACL,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,sBAAsB,KAAK,QAAQ,MAAM;AAChD,MAAI;AACF,WAAO;AAAA,MACL,KAAK,IAAI,YAAY,QAAQ,IAAI;AAAA,IACnC;AAAA,EACF,SAAS,KAAK;AACZ,WAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAI,iBAAiB,IAAI,QAAQ;AAEjC,SAAS,YAAY,QAAQ,KAAK,MAAM,KAAK,KAAK,UAAU;AAC1D,MAAI,WAAW,IAAI;AACnB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACnD,QAAI,MAAM,SAAS,CAAC;AACpB,QAAI,IAAI,OAAO,UAAU,IAAI,GAAG,GAAG;AACjC;AAAA,IACF;AACA,UAAM,SAAS,CAAC,IAAI,SAAS,KAAK,KAAK,WAAW,MAAM;AACxD,QAAI,IAAI,SAAS,CAAC,cAAc;AAC9B,qBAAe;AAAA,IACjB;AAAA,EACF;AAEA,MAAI,cAAc;AAChB,WAAO,SAAS,YAAY;AAAA,EAC9B;AAEA,MAAI,mBAAmB;AACvB,MAAI,gBAAgB;AACpB,MAAI,UAAU,IAAI,MAAM,SAAS,MAAM;AACvC,MAAI,cAAc,oBAAI,IAAI;AAC1B,MAAI,sBAAsB;AAC1B,MAAI,WAAW,IAAI,MAAM,cAAc,SAAS;AAEhD,wBAAsB,UAAU,UAAU,SAAU,KAAK;AACvD,QAAI,KAAK;AACP,aAAO,SAAS,GAAG;AAAA,IACrB;AACA,qBAAiB;AAAA,EACnB,CAAC;AAED,WAAS,mBAAmB;AAE1B,QAAI,SAAS;AAAA,MACX;AAAA,MAAW;AAAA,MACX;AAAA,MACA;AAAA,MAAa;AAAA,MACb;AAAA,IACF;AACA,QAAI,YAAY,sBAAsB,KAAK,QAAQ,WAAW;AAC9D,QAAI,UAAU,OAAO;AACnB,aAAO,SAAS,UAAU,KAAK;AAAA,IACjC;AACA,UAAM,UAAU;AAChB,QAAI,UAAU,SAAS,QAAQ;AAC/B,QAAI,YAAY,SAAS,QAAQ;AACjC,QAAI,aAAa;AACjB,eAAW,IAAI,YAAY,SAAS;AACpC,iBAAa,IAAI,YAAY,YAAY;AACzC,kBAAc,IAAI,YAAY,YAAY;AAC1C,wBAAoB,IAAI,YAAY,oBAAoB;AACxD,gBAAY,IAAI,YAAY,UAAU;AAEtC,cAAU,IAAI,UAAU,EAAE,YAAY,SAAU,GAAG;AACjD,gBAAU,EAAE,OAAO;AACnB,4BAAsB;AAAA,IACxB;AAEA,sBAAkB,SAAU,KAAK;AAC/B,UAAI,KAAK;AACP,8BAAsB;AACtB,eAAO,SAAS,GAAG;AAAA,MACrB;AACA,wBAAkB;AAAA,IACpB,CAAC;AAAA,EACH;AAEA,WAAS,qBAAqB;AAC5B,uBAAmB;AACnB,0BAAsB;AAAA,EACxB;AAEA,WAAS,iBAAiB;AACxB;AAAA,MAAY,OAAO;AAAA,MAAY;AAAA,MAAU;AAAA,MAAK;AAAA,MAClC;AAAA,MAAK;AAAA,MAAS;AAAA,MAAU;AAAA,MAAM;AAAA,IAAkB;AAAA,EAC9D;AAEA,WAAS,wBAAwB;AAC/B,QAAI,CAAC,WAAW,CAAC,kBAAkB;AACjC;AAAA,IACF;AAGA,YAAQ,YAAY;AACpB,cAAU,IAAI,OAAO;AAAA,EACvB;AAEA,WAAS,oBAAoB;AAE3B,QAAI,CAAC,SAAS,QAAQ;AACpB;AAAA,IACF;AAEA,QAAI,aAAa;AAEjB,aAAS,YAAY;AACnB,UAAI,EAAE,eAAe,SAAS,QAAQ;AACpC,uBAAe;AAAA,MACjB;AAAA,IACF;AAEA,aAAS,aAAa,OAAO;AAC3B,UAAI,WAAW,eAAe,MAAM,OAAO,MAAM;AAEjD,UAAI,UAAU;AACZ,oBAAY,IAAI,SAAS,IAAI,QAAQ;AAAA,MACvC;AACA,gBAAU;AAAA,IACZ;AAEA,aAASnB,KAAI,GAAGoB,OAAM,SAAS,QAAQpB,KAAIoB,MAAKpB,MAAK;AACnD,UAAI,UAAU,SAASA,EAAC;AACxB,UAAI,QAAQ,OAAO,UAAU,QAAQ,GAAG,GAAG;AACzC,kBAAU;AACV;AAAA,MACF;AACA,UAAIqB,OAAM,SAAS,IAAI,QAAQ,SAAS,EAAE;AAC1C,MAAAA,KAAI,YAAY;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,WAAW;AAClB,QAAI,qBAAqB;AACvB;AAAA,IACF;AAEA,mBAAe,OAAO,IAAI,MAAM,IAAI;AACpC,aAAS,MAAM,OAAO;AAAA,EACxB;AAEA,WAAS,iBAAiB,QAAQC,WAAU;AAE1C,QAAID,OAAM,YAAY,IAAI,MAAM;AAChC,IAAAA,KAAI,YAAY,SAAU,GAAG;AAC3B,UAAI,CAAC,EAAE,OAAO,QAAQ;AACpB,YAAI,MAAM;AAAA,UAAY;AAAA,UACpB,yCACA;AAAA,QAAM;AACR,YAAI,SAAS;AACb,QAAAC,UAAS,GAAG;AAAA,MACd,OAAO;AACL,QAAAA,UAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAEA,WAAS,kBAAkB,QAAQ;AAGjC,QAAI,UAAU,CAAC;AACf,aAAS,QAAQ,SAAU,SAAS;AAClC,UAAI,QAAQ,QAAQ,QAAQ,KAAK,cAAc;AAC7C,eAAO,KAAK,QAAQ,KAAK,YAAY,EAAE,QAAQ,SAAU,UAAU;AACjE,cAAI,MAAM,QAAQ,KAAK,aAAa,QAAQ;AAC5C,cAAI,IAAI,MAAM;AACZ,oBAAQ,KAAK,IAAI,MAAM;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,UAAU;AACd,QAAI;AAEJ,aAAS,YAAY;AACnB,UAAI,EAAE,YAAY,QAAQ,QAAQ;AAChC,eAAO,GAAG;AAAA,MACZ;AAAA,IACF;AACA,YAAQ,QAAQ,SAAU,QAAQ;AAChC,uBAAiB,QAAQ,SAAU,QAAQ;AACzC,YAAI,UAAU,CAAC,KAAK;AAClB,gBAAM;AAAA,QACR;AACA,kBAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,WAAS,SAAS,SAAS,eAAe,qBAAqB,iBAC7C,UAAU,OAAO,YAAYA,WAAU;AAEvD,YAAQ,SAAS,aAAa;AAC9B,YAAQ,SAAS,UAAU;AAE3B,QAAIL,OAAM,QAAQ;AAClB,IAAAA,KAAI,MAAM,QAAQ,SAAS;AAC3B,IAAAA,KAAI,OAAO,QAAQ,SAAS;AAE5B,QAAI,iBAAiB;AACnB,MAAAA,KAAI,WAAW;AAAA,IACjB;AAEA,QAAI,iBAAiBA,KAAI,gBACvB,OAAO,KAAKA,KAAI,YAAY,EAAE;AAChC,QAAI,gBAAgB;AAClB,aAAO;AAAA,QAAiB;AAAA,QAAS;AAAA,QAAe;AAAA,QAC9C;AAAA,QAAU;AAAA,QAAYK;AAAA,MAAQ;AAAA,IAClC;AAEA,qBAAiB;AACjB,0BAAsB;AAEtB;AAAA,MAAU;AAAA,MAAS;AAAA,MAAe;AAAA,MAChC;AAAA,MAAU;AAAA,MAAYA;AAAA,IAAQ;AAAA,EAClC;AAEA,WAAS,UAAU,SAAS,eAAe,qBACxB,UAAU,YAAYA,WAAU;AAEjD,QAAIL,OAAM,QAAQ;AAClB,QAAI,WAAW,QAAQ;AAEvB,IAAAA,KAAI,cAAc,SAAS,KAAK,OAAO,SAAS;AAChD,WAAOA,KAAI;AACX,WAAOA,KAAI;AAEX,aAAS,YAAY,GAAG;AACtB,UAAI,eAAe,QAAQ,eAAe,CAAC;AAE3C,UAAI,YAAY,IAAI,iBAAiB;AACnC,uBAAe,aAAa,OAAO,YAAY,QAAQ,QAAQ,CAAC;AAAA,MAClE;AAEA,UAAI,gBAAgB,aAAa,QAAQ;AACvC,oBAAY,cAAc,QAAQ,SAAS,IAAI,GAAG;AAAA,MACpD;AAEA,eAAS,MAAM,EAAE,OAAO;AAGxB,UAAI,kBAAkB;AAAA,QAAe;AAAA,QAAU;AAAA,QAC7C;AAAA,MAAmB;AACrB,UAAI,cAAc,SAAS,IAAI,eAAe;AAC9C,kBAAY,YAAY;AAAA,IAC1B;AAEA,aAAS,iBAAiB,GAAG;AAE3B,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAClB,UAAI,QAAQ,WAAW,MAAM,aAAa;AAC1C,UAAI,YAAY,MAAM,OAAOA,KAAI,WAAW;AAC5C,gBAAU,YAAY,SAAUM,IAAG;AACjC,YAAIC,UAAS,WAAW,IAAIP,MAAKM,GAAE,OAAO,MAAM;AAChD,QAAAC,QAAO,YAAY;AAAA,MACrB;AAAA,IACF;AAEA,aAAS,mBAAmB;AAC1B,cAAQ,UAAU,IAAI;AAAA,QACpB,IAAI;AAAA,QACJ,IAAI,SAAS;AAAA,QACb,KAAK,SAAS;AAAA,MAChB;AACA,kBAAY,IAAI,QAAQ,SAAS,IAAI,QAAQ,QAAQ;AACrD,+BAAyB,SAAS,SAAS,KAAKF,SAAQ;AAAA,IAC1D;AAEA,QAAI,SAAS,WAAW,IAAIL,IAAG;AAE/B,WAAO,YAAY;AACnB,WAAO,UAAU;AAAA,EACnB;AAEA,WAAS,iBAAiB,SAAS,eAAe,qBACxB,UAAU,YAAYK,WAAU;AAGxD,QAAIL,OAAM,QAAQ;AAElB,QAAI,UAAU;AACd,QAAI,cAAc,OAAO,KAAKA,KAAI,YAAY;AAE9C,aAAS,iBAAiB;AACxB,UAAI,YAAY,YAAY,QAAQ;AAClC;AAAA,UAAU;AAAA,UAAS;AAAA,UAAe;AAAA,UAChC;AAAA,UAAU;AAAA,UAAYK;AAAA,QAAQ;AAAA,MAClC;AAAA,IACF;AAEA,aAAS,kBAAkB;AACzB;AACA,qBAAe;AAAA,IACjB;AAEA,gBAAY,QAAQ,SAAU,KAAK;AACjC,UAAI,MAAM,QAAQ,KAAK,aAAa,GAAG;AACvC,UAAI,CAAC,IAAI,MAAM;AACb,YAAI,OAAO,IAAI;AACf,eAAO,IAAI;AACX,YAAI,SAAS,SAAS,eAAe,EAAE;AACvC,YAAI,SAAS,IAAI;AACjB,uBAAe,QAAQ,MAAM,eAAe;AAAA,MAC9C,OAAO;AACL;AACA,uBAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAIA,WAAS,yBAAyB,SAAS,KAAKA,WAAU;AAExD,QAAI,YAAY;AAChB,QAAI,YAAY,OAAO,KAAK,QAAQ,KAAK,gBAAgB,CAAC,CAAC;AAE3D,QAAI,CAAC,UAAU,QAAQ;AACrB,aAAOA,UAAS;AAAA,IAClB;AAEA,aAAS,YAAY;AACnB,UAAI,EAAE,cAAc,UAAU,QAAQ;AACpC,QAAAA,UAAS;AAAA,MACX;AAAA,IACF;AAEA,aAAS,IAAI,KAAK;AAChB,UAAI,SAAS,QAAQ,KAAK,aAAa,GAAG,EAAE;AAC5C,UAAID,OAAM,kBAAkB,IAAI;AAAA,QAC9B;AAAA,QACA,WAAW,SAAS,OAAO;AAAA,MAC7B,CAAC;AAED,MAAAA,KAAI,YAAY;AAChB,MAAAA,KAAI,UAAU,SAAU,GAAG;AAIzB,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAClB,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,aAASrB,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,UAAI,UAAUA,EAAC,CAAC;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,eAAe,QAAQ,MAAMsB,WAAU;AAG9C,QAAI,YAAY,YAAY,MAAM,MAAM;AACxC,cAAU,YAAY,SAAU,GAAG;AACjC,UAAI,QAAQ,EAAE,OAAO;AACrB,UAAI,OAAO;AACT,eAAOA,UAAS;AAAA,MAClB;AACA,UAAI,SAAS;AAAA,QACX;AAAA,QACA,MAAM;AAAA,MACR;AACA,UAAI,SAAS,YAAY,IAAI,MAAM;AACnC,aAAO,YAAYA;AAAA,IACrB;AAAA,EACF;AACF;AAMA,SAAS,iBAAiB,aAAa,UAAU,YAAY,WAAW,SAAS;AAE/E,MAAI,cAAc,IAAI;AACpB,gBAAY;AAAA,EACd;AAOA,MAAI,YAAY,OAAO,YAAY,WAAW,cAC5C,OAAO,YAAY,eAAe,cAClC,YAAY,KAAK,CAAC;AAEpB,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,SAAS,GAAG;AACnB,kBAAc,EAAE,OAAO;AACvB,QAAI,WAAW;AACb,cAAQ,WAAW,aAAa,YAAY;AAAA,IAC9C;AAAA,EACF;AAEA,WAAS,aAAa,GAAG;AACvB,gBAAY,EAAE,OAAO;AACrB,QAAI,aAAa;AACf,cAAQ,WAAW,aAAa,YAAY;AAAA,IAC9C;AAAA,EACF;AAEA,WAAS,uBAAuB;AAC9B,QAAI,CAAC,UAAU,QAAQ;AACrB,aAAO,QAAQ;AAAA,IACjB;AAEA,QAAI,UAAU,UAAU,UAAU,SAAS,CAAC;AAC5C,QAAI;AACJ,QAAI,YAAY,SAAS,OAAO;AAC9B,UAAI;AACF,sBAAc,YAAY;AAAA,UAAM;AAAA,UAAS,SAAS;AAAA,UAChD;AAAA,UAAM,SAAS;AAAA,QAAS;AAAA,MAC5B,SAAS,GAAG;AACV,YAAI,EAAE,SAAS,eAAe,EAAE,SAAS,GAAG;AAC1C,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF;AAAA,IACF,OAAO;AACL,oBAAc,YAAY,WAAW,SAAS,IAAI;AAAA,IACpD;AACA,eAAW;AACX,gBAAY;AACZ,kBAAc;AACd,gBAAY,OAAO,UAAU,SAAS,EAAE,YAAY;AACpD,gBAAY,WAAW,UAAU,SAAS,EAAE,YAAY;AAAA,EAC1D;AAEA,WAAS,SAAS,GAAG;AACnB,QAAI,SAAS,EAAE,OAAO;AACtB,QAAI,CAAC,QAAQ;AACX,aAAO,QAAQ;AAAA,IACjB;AAEA,YAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,KAAK,GAAG,MAAM;AAAA,EAC9C;AAEA,MAAI,WAAW;AACb,mBAAe,EAAC,YAAY,qBAAoB;AAChD,gBAAY,OAAO,UAAU,SAAS,EAAE,YAAY;AACpD,gBAAY,WAAW,UAAU,SAAS,EAAE,YAAY;AAAA,EAC1D,WAAW,YAAY;AACrB,gBAAY,WAAW,UAAU,MAAM,EAAE,YAAY;AAAA,EACvD,OAAO;AACL,gBAAY,WAAW,QAAQ,EAAE,YAAY;AAAA,EAC/C;AACF;AAGA,SAAS,OAAO,aAAa,UAAU,WAAW;AAChD,MAAI,OAAO,YAAY,WAAW,YAAY;AAE5C,gBAAY,OAAO,QAAQ,EAAE,YAAY;AACzC;AAAA,EACF;AAEA,MAAI,SAAS,CAAC;AAEd,WAAS,SAAS,GAAG;AACnB,QAAI,SAAS,EAAE,OAAO;AACtB,QAAI,QAAQ;AACV,aAAO,KAAK,OAAO,KAAK;AACxB,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,gBAAU;AAAA,QACR,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,cAAY,WAAW,QAAQ,EAAE,YAAY;AAC/C;AAEA,SAAS,YAAY3B,OAAM,UAAU,SAAS;AAE5C,MAAI,cAAc,IAAI,MAAMA,MAAK,MAAM;AACvC,MAAI,QAAQ;AACZ,EAAAA,MAAK,QAAQ,SAAU,KAAK,OAAO;AACjC,aAAS,IAAI,GAAG,EAAE,YAAY,SAAU,OAAO;AAC7C,UAAI,MAAM,OAAO,QAAQ;AACvB,oBAAY,KAAK,IAAI,MAAM,OAAO;AAAA,MACpC,OAAO;AACL,oBAAY,KAAK,IAAI,EAAC,KAAK,OAAO,YAAW;AAAA,MAC/C;AACA;AACA,UAAI,UAAUA,MAAK,QAAQ;AACzB,gBAAQA,OAAM,aAAa,CAAC,CAAC;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eAAe,OAAO,KAAK,cAAc,KAAK,YAAY;AACjE,MAAI;AACF,QAAI,SAAS,KAAK;AAChB,UAAI,YAAY;AACd,eAAO,YAAY,MAAM,KAAK,OAAO,CAAC,cAAc,KAAK;AAAA,MAC3D,OAAO;AACL,eAAO,YAAY,MAAM,OAAO,KAAK,OAAO,CAAC,YAAY;AAAA,MAC3D;AAAA,IACF,WAAW,OAAO;AAChB,UAAI,YAAY;AACd,eAAO,YAAY,WAAW,KAAK;AAAA,MACrC,OAAO;AACL,eAAO,YAAY,WAAW,KAAK;AAAA,MACrC;AAAA,IACF,WAAW,KAAK;AACd,UAAI,YAAY;AACd,eAAO,YAAY,WAAW,KAAK,CAAC,YAAY;AAAA,MAClD,OAAO;AACL,eAAO,YAAY,WAAW,KAAK,CAAC,YAAY;AAAA,MAClD;AAAA,IACF,WAAW,KAAK;AACd,aAAO,YAAY,KAAK,GAAG;AAAA,IAC7B;AAAA,EACF,SAAS,GAAG;AACV,WAAO,EAAC,OAAO,EAAC;AAAA,EAClB;AACA,SAAO;AACT;AAEA,SAAS,WAAW,MAAM,KAAK,UAAU;AACvC,MAAI,QAAQ,cAAc,OAAO,KAAK,WAAW;AACjD,MAAI,MAAM,YAAY,OAAO,KAAK,SAAS;AAC3C,MAAI,MAAM,SAAS,OAAO,KAAK,MAAM;AACrC,MAAIA,QAAO,UAAU,OAAO,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK,QAAQ;AACxB,MAAI,QAAQ,OAAO,KAAK,UAAU,WAAW,KAAK,QAAQ;AAC1D,MAAI,eAAe,KAAK,kBAAkB;AAE1C,MAAI;AACJ,MAAI;AACJ,MAAI,CAACA,OAAM;AACT,eAAW,eAAe,OAAO,KAAK,cAAc,KAAK,KAAK,UAAU;AACxE,oBAAgB,YAAY,SAAS;AACrC,QAAI,iBACF,EAAE,cAAc,SAAS,eAAe,cAAc,SAAS,IAAI;AAGnE,aAAO,SAAS;AAAA,QAAY;AAAA,QAC1B,cAAc;AAAA,QAAM,cAAc;AAAA,MAAO,CAAC;AAAA,IAC9C;AAAA,EACF;AAEA,MAAI,SAAS,CAAC,WAAW,cAAc,UAAU;AAEjD,MAAI,KAAK,aAAa;AACpB,WAAO,KAAK,YAAY;AAAA,EAC1B;AACA,MAAI,YAAY,sBAAsB,KAAK,QAAQ,UAAU;AAC7D,MAAI,UAAU,OAAO;AACnB,WAAO,SAAS,UAAU,KAAK;AAAA,EACjC;AACA,MAAI,MAAM,UAAU;AACpB,MAAI,aAAa;AACjB,MAAI,UAAU,SAAS,QAAQ;AAC/B,MAAI,WAAW,IAAI,YAAY,SAAS;AACxC,MAAI,WAAW,IAAI,YAAY,YAAY;AAC3C,MAAI,YAAY,IAAI,YAAY,UAAU;AAC1C,MAAI,gBAAgB,SAAS,MAAM,aAAa;AAChD,MAAI,UAAU,CAAC;AACf,MAAI;AACJ,MAAI;AAEJ,YAAU,IAAI,UAAU,EAAE,YAAY,SAAU,GAAG;AACjD,eAAW,EAAE,OAAO,OAAO;AAAA,EAC7B;AAGA,MAAI,KAAK,YAAY;AAEnB,aAAS,cAAc,MAAM,MAAM,EAAE,YAAY,OAAK;AACpD,UAAI,SAAS,EAAE,OAAO;AACtB,UAAI,UAAU,OAAO,KAAK;AACxB,oBAAY,OAAO;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAIA,WAAS,uBAAuB,UAAU,KAAK,eAAe;AAC5D,QAAIO,OAAM,SAAS,KAAK,OAAO;AAC/B,kBAAc,IAAIA,IAAG,EAAE,YAAa,SAAS,SAAS,GAAG;AACvD,UAAI,MAAM,UAAU,EAAE,OAAO,MAAM,KAAK,CAAC;AACzC,UAAI,KAAK,WAAW;AAClB,YAAI,YAAY,iBAAiB,QAAQ;AACzC,YAAI,UAAU,QAAQ;AACpB,cAAI,IAAI,aAAa;AAAA,QACvB;AAAA,MACF;AACA,kCAA4B,IAAI,KAAK,MAAM,GAAG;AAAA,IAChD;AAAA,EACF;AAEA,WAAS,aAAa,eAAe,UAAU;AAC7C,QAAI,MAAM;AAAA,MACR,IAAI,SAAS;AAAA,MACb,KAAK,SAAS;AAAA,MACd,OAAO;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AACA,QAAI,UAAU,SAAS;AACvB,QAAI,SAAS;AACX,UAAIP,OAAM;AACR,gBAAQ,KAAK,GAAG;AAEhB,YAAI,MAAM,UAAU;AACpB,YAAI,MAAM;AAAA,MACZ;AAAA,IACF,WAAW,UAAU,GAAG;AACtB,cAAQ,KAAK,GAAG;AAChB,UAAI,KAAK,cAAc;AACrB,+BAAuB,UAAU,KAAK,aAAa;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAEA,WAAS,aAAa,aAAa;AACjC,aAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,UAAI,QAAQ,WAAW,OAAO;AAC5B;AAAA,MACF;AACA,UAAI,aAAa,YAAY,CAAC;AAC9B,UAAI,WAAW,SAASA,OAAM;AAE5B,gBAAQ,KAAK,UAAU;AACvB;AAAA,MACF;AACA,UAAI,WAAW,eAAe,UAAU;AACxC,UAAI,gBAAgB,SAAS;AAC7B,mBAAa,eAAe,QAAQ;AAAA,IACtC;AAAA,EACF;AAEA,WAAS,QAAQ,WAAW,aAAa,QAAQ;AAC/C,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,iBAAa,WAAW;AACxB,QAAI,QAAQ,SAAS,OAAO;AAC1B,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,SAAS,GAAG;AACnB,QAAI,SAAS,EAAE,OAAO;AACtB,QAAI,KAAK,YAAY;AACnB,eAAS,OAAO,QAAQ;AAAA,IAC1B;AACA,iBAAa,MAAM;AAAA,EACrB;AAEA,WAAS,iBAAiB;AACxB,QAAI,YAAY;AAAA,MACd,YAAY;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,MAAM;AAAA,IACR;AAGA,QAAI,KAAK,cAAc,cAAc,QAAW;AAC9C,gBAAU,aAAa;AAAA,IACzB;AACA,aAAS,MAAM,SAAS;AAAA,EAC1B;AAEA,WAAS,gBAAgB;AACvB,QAAI,KAAK,aAAa;AACpB,6BAAuB,SAAS,KAAK,MAAM,EAAE,KAAK,cAAc;AAAA,IAClE,OAAO;AACL,qBAAe;AAAA,IACjB;AAAA,EACF;AAGA,MAAI,iBAAiB,UAAU,GAAG;AAChC;AAAA,EACF;AACA,MAAIA,OAAM;AACR,WAAO,YAAYA,OAAM,UAAU,OAAO;AAAA,EAC5C;AACA,MAAI,UAAU,IAAI;AAChB,WAAO,OAAO,UAAU,UAAU,QAAQ;AAAA,EAC5C;AAGA,mBAAiB,UAAU,UAAU,KAAK,YAAY,QAAQ,MAAM,OAAO;AAC7E;AAEA,SAAS,UAAU,KAAK,IAAI;AAC1B,MAAI,QAAQ,IAAI,YAAY,SAAS,EAAE,MAAM,gBAAgB;AAC7D,QAAM,MAAM,YAAY,KAAK,GAAG,CAAC,EAAE,YAAY,SAAU,GAAG;AAC1D,OAAG,EAAE,OAAO,MAAM;AAAA,EACpB;AACF;AAIA,IAAI,UAAU;AACd,IAAI,QAAQ,CAAC;AAEb,SAAS,QAAQ,KAAK,KAAK,KAAKU,UAAS;AACvC,MAAI;AACF,QAAI,KAAK,GAAG;AAAA,EACd,SAASF,MAAK;AAIZ,IAAAE,SAAQ,KAAK,SAASF,IAAG;AAAA,EAC3B;AACF;AAEA,SAAS,YAAY;AACnB,MAAI,WAAW,CAAC,MAAM,QAAQ;AAC5B;AAAA,EACF;AACA,YAAU;AACV,QAAM,MAAM,EAAE;AAChB;AAEA,SAAS,YAAY,QAAQ,UAAUE,UAAS;AAC9C,QAAM,KAAK,SAAS,YAAY;AAC9B,WAAO,SAAS,YAAY,KAAK,KAAK;AACpC,cAAQ,UAAU,KAAK,KAAKA,QAAO;AACnC,gBAAU;AACV,eAAS,SAAS,UAAU;AAC1B,kBAAUA,QAAO;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACD,YAAU;AACZ;AAEA,SAAS,QAAQ,MAAM,KAAK,QAAQ,KAAK;AACvC,SAAO,MAAM,IAAI;AAEjB,MAAI,KAAK,YAAY;AACnB,QAAI,KAAK,SAAS,MAAM,KAAK;AAC7B,mBAAe,YAAY,QAAQ,IAAI,KAAK,IAAI;AAChD,mBAAe,OAAO,MAAM;AAC5B,WAAO;AAAA,MACL,QAAQ,WAAY;AAClB,uBAAe,eAAe,QAAQ,EAAE;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,KAAK,WAAW,IAAI,IAAI,KAAK,OAAO;AAEjD,OAAK,QAAQ,KAAK,SAAS;AAC3B,MAAI,UAAU,KAAK;AAEnB,MAAI,QAAQ,WAAW,OAAO,KAAK,QAAQ;AAC3C,MAAI,UAAU,GAAG;AACf,YAAQ;AAAA,EACV;AAEA,MAAI,UAAU,CAAC;AACf,MAAI,aAAa;AACjB,MAAIX,UAAS,aAAa,IAAI;AAC9B,MAAI,mBAAmB,oBAAI,IAAI;AAE/B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,QAAQ,WAAW,aAAa,QAAQ;AAC/C,QAAI,CAAC,UAAU,CAAC,UAAU,QAAQ;AAChC;AAAA,IACF;AAEA,QAAI,cAAc,IAAI,MAAM,UAAU,MAAM;AAC5C,QAAI,YAAY,IAAI,MAAM,UAAU,MAAM;AAE1C,aAAS,6BAA6B,UAAU,YAAY;AAC1D,UAAI,SAAS,KAAK,cAAc,YAAY,UAAU,IAAI;AAC1D,gBAAU,OAAO,MAAM,SAAS;AAEhC,UAAI,WAAWA,QAAO,MAAM;AAC5B,UAAI,OAAO,aAAa,UAAU;AAChC,eAAO,QAAQ,OAAO,QAAQ;AAAA,MAChC;AAEA,UAAI,CAAC,UAAU;AACb,eAAO,QAAQ,QAAQ;AAAA,MACzB;AACA;AACA,UAAI,KAAK,aAAa;AACpB,gBAAQ,KAAK,MAAM;AAAA,MACrB;AAGA,UAAI,KAAK,eAAe,KAAK,cAAc;AACzC,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,sCAA4B,YAAY,MAAM,KAAK,WAAY;AAC7D,mCAAuB,CAAC,MAAM,GAAG,KAAK,MAAM,EAAE,KAAK,WAAY;AAC7D,sBAAQ,MAAM;AAAA,YAChB,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AACL,eAAO,QAAQ,QAAQ,MAAM;AAAA,MAC/B;AAAA,IACF;AAEA,aAAS,cAAc;AACrB,UAAI,WAAW,CAAC;AAChB,eAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,YAAI,eAAe,OAAO;AACxB;AAAA,QACF;AACA,YAAI,aAAa,YAAY,CAAC;AAC9B,YAAI,CAAC,YAAY;AACf;AAAA,QACF;AACA,YAAI,WAAW,UAAU,CAAC;AAC1B,iBAAS,KAAK,6BAA6B,UAAU,UAAU,CAAC;AAAA,MAClE;AAEA,cAAQ,IAAI,QAAQ,EAAE,KAAK,SAAU+B,UAAS;AAC5C,iBAASzB,KAAI,GAAGoB,OAAMK,SAAQ,QAAQzB,KAAIoB,MAAKpB,MAAK;AAClD,cAAIyB,SAAQzB,EAAC,GAAG;AACd,iBAAK,SAASyB,SAAQzB,EAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAAA,MACF,CAAC,EAAE,MAAM,KAAK,QAAQ;AAEtB,UAAI,eAAe,OAAO;AACxB,eAAO,SAAS;AAAA,MAClB;AAAA,IACF;AAKA,QAAI,UAAU;AACd,gBAAY,QAAQ,SAAU,OAAO,GAAG;AACtC,UAAI,MAAM,UAAU,KAAK;AACzB,UAAI,MAAM,UAAU,CAAC;AACrB,iCAA2B,KAAK,KAAK,SAAU,UAAU,YAAY;AACnE,kBAAU,CAAC,IAAI;AACf,oBAAY,CAAC,IAAI;AACjB,YAAI,EAAE,YAAY,UAAU,QAAQ;AAClC,sBAAY;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,WAAS,cAAc,KAAK,KAAK,UAAU,IAAI;AAC7C,QAAI,SAAS,QAAQ,KAAK;AAExB,aAAO,GAAG;AAAA,IACZ;AAEA,QAAI,SAAS,eAAe,IAAI,MAAM;AAEpC,aAAO,GAAG,UAAU,GAAG;AAAA,IACzB;AAGA,QAAI,WAAW,IAAI,MAAM,OAAO,SAAS;AACzC,QAAI,MAAM,cAAc,IAAI,QAAQ;AACpC,QAAI,YAAY,SAAU,GAAG;AAC3B,SAAG,UAAU,UAAU,EAAE,OAAO,MAAM,CAAC;AAAA,IACzC;AAAA,EACF;AAEA,WAAS,2BAA2B,KAAK,KAAK,IAAI;AAChD,QAAI,UAAU,CAAC,OAAO,IAAI,IAAI,GAAG,GAAG;AAClC,aAAO,GAAG;AAAA,IACZ;AAEA,QAAI,WAAW,iBAAiB,IAAI,IAAI,GAAG;AAC3C,QAAI,UAAU;AACZ,aAAO,cAAc,KAAK,KAAK,UAAU,EAAE;AAAA,IAC7C;AAEA,aAAS,IAAI,IAAI,GAAG,EAAE,YAAY,SAAU,GAAG;AAC7C,iBAAW,eAAe,EAAE,OAAO,MAAM;AACzC,uBAAiB,IAAI,IAAI,KAAK,QAAQ;AACtC,oBAAc,KAAK,KAAK,UAAU,EAAE;AAAA,IACtC;AAAA,EACF;AAEA,WAAS,SAAS;AAChB,SAAK,SAAS,MAAM;AAAA,MAClB;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,WAAS,gBAAgB;AACvB,QAAI,CAAC,KAAK,cAAc,KAAK,aAAa;AAGxC,6BAAuB,OAAO,EAAE,KAAK,MAAM;AAAA,IAC7C,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,eAAe,CAAC,WAAW,YAAY;AAC3C,MAAI,KAAK,aAAa;AACpB,iBAAa,KAAK,YAAY;AAAA,EAChC;AACA,MAAI,YAAY,sBAAsB,KAAK,cAAc,UAAU;AACnE,MAAI,UAAU,OAAO;AACnB,WAAO,KAAK,SAAS,UAAU,KAAK;AAAA,EACtC;AACA,QAAM,UAAU;AAChB,MAAI,UAAU,SAAS,KAAK,QAAQ;AACpC,MAAI,aAAa;AAEjB,eAAa,IAAI,YAAY,YAAY;AACzC,aAAW,IAAI,YAAY,SAAS;AACpC,kBAAgB,WAAW,MAAM,aAAa;AAE9C,MAAI,WAAY,KAAK,SAAS,CAAC,KAAK,aAClC,YAAY,WAAW,KAAK,OAAO,IAAI,IAAI;AAE7C,mBAAiB,YAAY,UAAU,KAAK,YAAY,OAAO,OAAO;AACxE;AAEA,IAAI,YAAY,oBAAI,IAAI;AACxB,IAAI;AACJ,IAAI,cAAc,oBAAI,IAAI;AAE1B,SAAS,SAAS,MAAM,UAAU;AAChC,MAAI,MAAM;AAEV,cAAY,SAAU,cAAc;AAClC,SAAK,KAAK,MAAM,YAAY;AAAA,EAC9B,GAAG,UAAU,IAAI,WAAW;AAC9B;AAEA,SAAS,KAAK,KAAK,MAAM,UAAU;AAEjC,MAAI,SAAS,KAAK;AAElB,MAAI,MAAM;AACV,MAAI,wBAAwB;AAC5B,MAAI,QAAQ;AAEZ,WAAS,oBAAoBsB,WAAU;AACrC,WAAO,SAAU,OAAO,QAAQ;AAC9B,UAAI,SAAS,iBAAiB,SAAS,CAAC,MAAM,QAAQ;AACpD,YAAI,uBAAuB;AACzB,gBAAM,SAAS;AAAA,QACjB;AAAA,MACF;AAEA,MAAAA,UAAS,OAAO,MAAM;AAAA,IACxB;AAAA,EACF;AAGA,WAAS,aAAa,IAAI;AACxB,QAAI,WAAW,GAAG,kBAAkB,WAAW,EAAC,SAAU,KAAI,CAAC;AAC/D,OAAG,kBAAkB,cAAc,EAAC,eAAe,KAAI,CAAC,EACrD,YAAY,eAAe,eAAe,EAAC,QAAQ,KAAI,CAAC;AAC3D,OAAG,kBAAkB,cAAc,EAAC,SAAS,SAAQ,CAAC;AACtD,OAAG,kBAAkB,YAAY,EAAC,SAAS,MAAM,eAAe,MAAK,CAAC;AACtE,OAAG,kBAAkB,yBAAyB;AAG9C,aAAS,YAAY,kBAAkB,kBAAkB,EAAC,QAAS,MAAK,CAAC;AAGzE,OAAG,kBAAkB,aAAa,EAAC,SAAS,MAAK,CAAC;AAGlD,QAAI,iBAAiB,GAAG;AAAA,MAAkB;AAAA,MACxC,EAAC,eAAe,KAAI;AAAA,IAAC;AACvB,mBAAe,YAAY,OAAO,KAAK;AACvC,mBAAe,YAAY,aAAa,aAAa,EAAC,QAAQ,KAAI,CAAC;AAAA,EACrE;AAKA,WAAS,uBAAuB,KAAKA,WAAU;AAC7C,QAAI,WAAW,IAAI,YAAY,SAAS;AACxC,aAAS,YAAY,kBAAkB,kBAAkB,EAAC,QAAS,MAAK,CAAC;AAEzE,aAAS,WAAW,EAAE,YAAY,SAAU,OAAO;AACjD,UAAI,SAAS,MAAM,OAAO;AAC1B,UAAI,QAAQ;AACV,YAAI,WAAW,OAAO;AACtB,YAAI,UAAU,UAAU,QAAQ;AAChC,iBAAS,iBAAiB,UAAU,MAAM;AAC1C,iBAAS,IAAI,QAAQ;AACrB,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,QAAAA,UAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAGA,WAAS,uBAAuB,IAAI;AAClC,OAAG,kBAAkB,aAAa,EAAC,SAAS,MAAK,CAAC,EAC/C,YAAY,eAAe,eAAe,EAAC,QAAQ,KAAI,CAAC;AAAA,EAC7D;AAGA,WAAS,kBAAkB,KAAK,IAAI;AAClC,QAAI,aAAa,IAAI,YAAY,WAAW;AAC5C,QAAI,WAAW,IAAI,YAAY,SAAS;AACxC,QAAI,WAAW,IAAI,YAAY,YAAY;AAE3C,QAAI,SAAS,SAAS,WAAW;AACjC,WAAO,YAAY,SAAU,OAAO;AAClC,UAAIH,UAAS,MAAM,OAAO;AAC1B,UAAIA,SAAQ;AACV,YAAI,WAAWA,QAAO;AACtB,YAAI,QAAQ,SAAS;AACrB,YAAI,QAAQ,UAAU,KAAK;AAC3B,YAAI,SAAS,WAAW,QAAQ;AAChC,YAAI,OAAO;AACT,cAAI,WAAW,QAAQ,OAAO;AAG9B,cAAI,QAAQ,QAAQ;AACpB,cAAI,MAAM,QAAQ;AAClB,cAAI,QAAQ,SAAS,MAAM,aAAa;AACxC,cAAI,QAAQ,YAAY,MAAM,OAAO,KAAK,OAAO,KAAK;AACtD,cAAI,YAAY,MAAM,WAAW,KAAK;AACtC,oBAAU,YAAY,SAAU,GAAG;AACjC,wBAAY,EAAE,OAAO;AACrB,gBAAI,CAAC,WAAW;AAEd,uBAAS,OAAOA,QAAO,UAAU;AACjC,cAAAA,QAAO,SAAS;AAAA,YAClB,OAAO;AACL,kBAAI,OAAO,UAAU;AACrB,kBAAI,KAAK,gBAAgB,UAAU;AACjC,2BAAW,IAAI,IAAI;AAAA,cACrB;AACA,uBAAS,OAAO,UAAU,UAAU;AACpC,wBAAU,SAAS;AAAA,YACrB;AAAA,UACF;AAAA,QACF,OAAO;AACL,UAAAA,QAAO,SAAS;AAAA,QAClB;AAAA,MACF,WAAW,IAAI;AACb,WAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAGA,WAAS,qBAAqB,IAAI;AAChC,QAAI,iBAAiB,GAAG;AAAA,MAAkB;AAAA,MACxC,EAAC,eAAe,KAAI;AAAA,IAAC;AACvB,mBAAe,YAAY,OAAO,KAAK;AACvC,mBAAe,YAAY,aAAa,aAAa,EAAC,QAAQ,KAAI,CAAC;AAAA,EACrE;AAGA,WAAS,mBAAmB,KAAKG,WAAU;AACzC,QAAI,WAAW,IAAI,YAAY,YAAY;AAC3C,QAAI,WAAW,IAAI,YAAY,YAAY;AAC3C,QAAI,iBAAiB,IAAI,YAAY,oBAAoB;AAKzD,QAAID,OAAM,SAAS,MAAM;AACzB,IAAAA,KAAI,YAAY,SAAU,GAAG;AAC3B,UAAI,QAAQ,EAAE,OAAO;AACrB,UAAI,CAAC,OAAO;AACV,eAAOC,UAAS;AAAA,MAClB;AAEA,eAAS,WAAW,EAAE,YAAY,SAAUC,IAAG;AAC7C,YAAI,SAASA,GAAE,OAAO;AACtB,YAAI,CAAC,QAAQ;AACX,iBAAOD,UAAS;AAAA,QAClB;AACA,YAAI,MAAM,OAAO;AACjB,YAAI,MAAM,OAAO;AACjB,YAAI,OAAO,OAAO,KAAK,IAAI,gBAAgB,CAAC,CAAC;AAC7C,YAAI,YAAY,CAAC;AACjB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,MAAM,IAAI,aAAa,KAAK,CAAC,CAAC;AAClC,oBAAU,IAAI,MAAM,IAAI;AAAA,QAC1B;AACA,YAAI,UAAU,OAAO,KAAK,SAAS;AACnC,aAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,cAAI,SAAS,QAAQ,CAAC;AACtB,yBAAe,IAAI;AAAA,YACjB;AAAA,YACA,WAAW,SAAS,OAAO;AAAA,UAC7B,CAAC;AAAA,QACH;AACA,eAAO,SAAS;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAQA,WAAS,gBAAgB,KAAK;AAE5B,aAAS,qBAAqB,cAAc;AAC1C,UAAI,CAAC,aAAa,MAAM;AAEtB,qBAAa,UAAU,aAAa,mBAAmB;AACvD,eAAO;AAAA,MACT;AACA,aAAO,eAAe,YAAY;AAAA,IACpC;AAIA,QAAI,aAAa,IAAI,YAAY,YAAY;AAC7C,QAAI,WAAW,IAAI,YAAY,SAAS;AACxC,QAAI,SAAS,SAAS,WAAW;AACjC,WAAO,YAAY,SAAU,GAAG;AAC9B,UAAIH,UAAS,EAAE,OAAO;AACtB,UAAI,CAACA,SAAQ;AACX;AAAA,MACF;AACA,UAAI,WAAW,qBAAqBA,QAAO,KAAK;AAEhD,eAAS,aAAa,SAAS,cAC7B,WAAW,QAAQ;AAErB,eAAS,mBAAmB;AAG1B,YAAI,QAAQ,SAAS,KAAK;AAC1B,YAAI,MAAM,SAAS,KAAK;AACxB,YAAIE,OAAM,WAAW,MAAM,aAAa,EAAE;AAAA,UACxC,YAAY,MAAM,OAAO,GAAG;AAAA,QAAC;AAE/B,YAAI,cAAc;AAClB,QAAAA,KAAI,YAAY,SAAUE,IAAG;AAC3B,cAAIJ,UAASI,GAAE,OAAO;AACtB,cAAI,CAACJ,SAAQ;AACX,qBAAS,MAAM;AACf,mBAAO,iBAAiB;AAAA,UAC1B;AACA,cAAI,MAAMA,QAAO;AACjB,cAAI,MAAM,aAAa;AACrB,0BAAc;AAAA,UAChB;AACA,UAAAA,QAAO,SAAS;AAAA,QAClB;AAAA,MACF;AAEA,eAAS,mBAAmB;AAC1B,YAAI,kBAAkB;AAAA,UAAe;AAAA,UACnC,SAAS;AAAA,UAAY,SAAS;AAAA,QAAO;AAEvC,YAAIE,OAAM,SAAS,IAAI,eAAe;AACtC,QAAAA,KAAI,YAAY,WAAY;AAC1B,UAAAF,QAAO,SAAS;AAAA,QAClB;AAAA,MACF;AAEA,UAAI,SAAS,KAAK;AAChB,eAAO,iBAAiB;AAAA,MAC1B;AAEA,uBAAiB;AAAA,IACnB;AAAA,EAEF;AAEA,MAAI,UAAU;AACd,MAAI,OAAO,WAAY;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,UAAU,SAAUG,WAAU;AACtC,IAAAA,UAAS,MAAM,IAAI,MAAM,UAAU;AAAA,EACrC,CAAC;AAED,MAAI,YAAY,SAAS,aAAaD,MAAK,SAASC,WAAU;AAC5D,gBAAY,MAAMD,MAAK,SAAS,KAAK,KAAK,oBAAoBC,SAAQ,CAAC;AAAA,EACzE;AAIA,MAAI,OAAO,SAAS,QAAQ,IAAIvB,OAAMuB,WAAU;AAC9C,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,MAAMvB,MAAK;AACf,QAAI,CAAC,KAAK;AACR,UAAI,YAAY;AAAA,QAAsB;AAAA,QACpC,CAAC,WAAW,cAAc,YAAY;AAAA,QAAG;AAAA,MAAU;AACrD,UAAI,UAAU,OAAO;AACnB,eAAOuB,UAAS,UAAU,KAAK;AAAA,MACjC;AACA,YAAM,UAAU;AAAA,IAClB;AAEA,aAAS,SAAS;AAChB,MAAAA,UAAS,KAAK,EAAC,KAAK,UAAU,KAAK,IAAG,CAAC;AAAA,IACzC;AAEA,QAAI,YAAY,SAAS,EAAE,IAAI,EAAE,EAAE,YAAY,SAAU,GAAG;AAC1D,iBAAW,eAAe,EAAE,OAAO,MAAM;AAMzC,UAAI,CAAC,UAAU;AACb,cAAM,YAAY,aAAa,SAAS;AACxC,eAAO,OAAO;AAAA,MAChB;AAEA,UAAI;AACJ,UAAI,CAACvB,MAAK,KAAK;AACb,iBAAS,SAAS;AAClB,YAAI,UAAU,UAAU,QAAQ;AAChC,YAAI,SAAS;AACX,gBAAM,YAAY,aAAa,SAAS;AACxC,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF,OAAO;AACL,iBAASA,MAAK,SAAS,OAAOA,MAAK,KAAK,QAAQ,IAAIA,MAAK;AAAA,MAC3D;AAEA,UAAI,cAAc,IAAI,YAAY,YAAY;AAC9C,UAAI,MAAM,SAAS,KAAK,OAAO;AAE/B,kBAAY,MAAM,aAAa,EAAE,IAAI,GAAG,EAAE,YAAY,SAAUwB,IAAG;AACjE,cAAMA,GAAE,OAAO;AACf,YAAI,KAAK;AACP,gBAAM,UAAU,GAAG;AAAA,QACrB;AACA,YAAI,CAAC,KAAK;AACR,gBAAM,YAAY,aAAa,SAAS;AACxC,iBAAO,OAAO;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,MAAI,iBAAiB,SAAU,OAAO,UAAU,YAAYxB,OAAMuB,WAAU;AAC1E,QAAI;AACJ,QAAIvB,MAAK,KAAK;AACZ,YAAMA,MAAK;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,QAAsB;AAAA,QACpC,CAAC,WAAW,cAAc,YAAY;AAAA,QAAG;AAAA,MAAU;AACrD,UAAI,UAAU,OAAO;AACnB,eAAOuB,UAAS,UAAU,KAAK;AAAA,MACjC;AACA,YAAM,UAAU;AAAA,IAClB;AACA,QAAI,SAAS,WAAW;AACxB,QAAI,OAAO,WAAW;AAEtB,QAAI,YAAY,YAAY,EAAE,IAAI,MAAM,EAAE,YAAY,SAAU,GAAG;AACjE,UAAI,OAAO,EAAE,OAAO,OAAO;AAC3B,mBAAa,MAAM,MAAMvB,MAAK,QAAQ,SAAU,UAAU;AACxD,QAAAuB,UAAS,MAAM,QAAQ;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,SAAS,SAASA,WAAU;AACtC,QAAI;AACJ,QAAI;AAEJ,QAAI,YAAY,sBAAsB,KAAK,CAAC,YAAY,YAAY,GAAG,UAAU;AACjF,QAAI,UAAU,OAAO;AACnB,aAAOA,UAAS,UAAU,KAAK;AAAA,IACjC;AACA,QAAI,MAAM,UAAU;AACpB,QAAI,YAAY,UAAU,EAAE,IAAI,UAAU,EAAE,YAAY,SAAU,GAAG;AACnE,iBAAW,EAAE,OAAO,OAAO;AAAA,IAC7B;AACA,QAAI,YAAY,YAAY,EAAE,cAAc,MAAM,MAAM,EAAE,YAAY,SAAU,GAAG;AACjF,UAAI,SAAS,EAAE,OAAO;AACtB,kBAAY,SAAS,OAAO,MAAM;AAAA,IACpC;AAEA,QAAI,aAAa,WAAY;AAC3B,MAAAA,UAAS,MAAM;AAAA,QACb,WAAW;AAAA,QACX,YAAY;AAAA;AAAA,QAEZ,uBAAwB,IAAI,MAAM,cAAc,WAAW;AAAA,MAC7D,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,WAAW,SAAS,YAAYvB,OAAMuB,WAAU;AAClD,eAAWvB,OAAM,KAAK,oBAAoBuB,SAAQ,CAAC;AAAA,EACrD;AAEA,MAAI,WAAW,SAAS,WAAWvB,OAAM;AACvC,WAAO,QAAQA,OAAM,KAAK,QAAQ,GAAG;AAAA,EACvC;AAEA,MAAI,SAAS,SAAUuB,WAAU;AAG/B,QAAI,MAAM;AACV,cAAU,OAAO,MAAM;AACvB,IAAAA,UAAS;AAAA,EACX;AAEA,MAAI,mBAAmB,SAAU,OAAOA,WAAU;AAChD,QAAI,YAAY,sBAAsB,KAAK,CAAC,SAAS,GAAG,UAAU;AAClE,QAAI,UAAU,OAAO;AACnB,aAAOA,UAAS,UAAU,KAAK;AAAA,IACjC;AACA,QAAI,MAAM,UAAU;AACpB,QAAID,OAAM,IAAI,YAAY,SAAS,EAAE,IAAI,KAAK;AAC9C,IAAAA,KAAI,YAAY,SAAU,OAAO;AAC/B,UAAI,MAAM,eAAe,MAAM,OAAO,MAAM;AAC5C,UAAI,CAAC,KAAK;AACR,QAAAC,UAAS,YAAY,WAAW,CAAC;AAAA,MACnC,OAAO;AACL,QAAAA,UAAS,MAAM,IAAI,QAAQ;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAKA,MAAI,gBAAgB,SAAU,OAAO,MAAMA,WAAU;AACnD,QAAI,SAAS;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,YAAY,sBAAsB,KAAK,QAAQ,WAAW;AAC9D,QAAI,UAAU,OAAO;AACnB,aAAOA,UAAS,UAAU,KAAK;AAAA,IACjC;AACA,QAAI,MAAM,UAAU;AAEpB,QAAI,WAAW,IAAI,YAAY,SAAS;AAExC,aAAS,IAAI,KAAK,EAAE,YAAY,SAAU,OAAO;AAC/C,UAAI,WAAW,eAAe,MAAM,OAAO,MAAM;AACjD,sBAAgB,SAAS,UAAU,SAAU,QAAQ,KACF,SAAS,KAAKvB,OAAM;AACrE,YAAI,SAAS,MAAM,MAAM;AACzB,YAAI,KAAK,QAAQ,MAAM,MAAM,IAAI;AAC/B,UAAAA,MAAK,SAAS;AAAA,QAChB;AAAA,MACF,CAAC;AACD,kBAAY,MAAM,OAAO,GAAG;AAC5B,UAAI,gBAAgB,SAAS;AAC7B,UAAI,UAAU,SAAS;AACvB,UAAI,YAAY,SAAS,EAAE;AAAA,QACzB,eAAe,UAAU,eAAe,OAAO;AAAA,MAAC;AAAA,IACpD;AACA,QAAI,UAAU,SAASuB,SAAQ;AAC/B,QAAI,aAAa,WAAY;AAC3B,MAAAA,UAAS;AAAA,IACX;AAAA,EACF;AAGA,MAAI,YAAY,SAAU,IAAIA,WAAU;AACtC,QAAI,YAAY,sBAAsB,KAAK,CAAC,WAAW,GAAG,UAAU;AACpE,QAAI,UAAU,OAAO;AACnB,aAAOA,UAAS,UAAU,KAAK;AAAA,IACjC;AACA,QAAI,KAAK,UAAU;AACnB,QAAID,OAAM,GAAG,YAAY,WAAW,EAAE,IAAI,EAAE;AAE5C,IAAAA,KAAI,UAAU,SAASC,SAAQ;AAC/B,IAAAD,KAAI,YAAY,SAAU,GAAG;AAC3B,UAAI,MAAM,EAAE,OAAO;AACnB,UAAI,CAAC,KAAK;AACR,QAAAC,UAAS,YAAY,WAAW,CAAC;AAAA,MACnC,OAAO;AACL,eAAO,IAAI,aAAa;AACxB,QAAAA,UAAS,MAAM,GAAG;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,YAAY,SAAU,KAAKvB,OAAMuB,WAAU;AAC7C,QAAI,OAAOvB,UAAS,YAAY;AAC9B,MAAAuB,YAAWvB;AACX,MAAAA,QAAO,CAAC;AAAA,IACV;AACA,WAAO,IAAI;AACX,QAAI,SAAS,IAAI;AACjB,QAAI,KAAK,IAAI;AACb,QAAI,CAAC,QAAQ;AACX,UAAI,OAAO;AAAA,IACb,OAAO;AACL,UAAI,OAAO,QAAQ,SAAS,OAAO,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI;AAAA,IAC1D;AAEA,QAAI,KAAKA,MAAK;AACd,QAAI;AACJ,QAAI,CAAC,IAAI;AACP,UAAI,YAAY,sBAAsB,KAAK,CAAC,WAAW,GAAG,WAAW;AACrE,UAAI,UAAU,OAAO;AACnB,eAAOuB,UAAS,UAAU,KAAK;AAAA,MACjC;AACA,WAAK,UAAU;AACf,SAAG,UAAU,SAASA,SAAQ;AAC9B,SAAG,aAAa,WAAY;AAC1B,YAAI,KAAK;AACP,UAAAA,UAAS,MAAM,GAAG;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS,GAAG,YAAY,WAAW;AACvC,QAAID;AACJ,QAAI,QAAQ;AACV,MAAAA,OAAM,OAAO,IAAI,EAAE;AACnB,MAAAA,KAAI,YAAY,SAAU,GAAG;AAC3B,YAAI,SAAS,EAAE,OAAO;AACtB,YAAI,CAAC,UAAU,OAAO,SAAS,QAAQ;AACrC,UAAAC,UAAS,YAAY,YAAY,CAAC;AAAA,QACpC,OAAO;AACL,cAAID,OAAM,OAAO,IAAI,GAAG;AACxB,UAAAA,KAAI,YAAY,WAAY;AAC1B,kBAAM,EAAC,IAAI,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,KAAI;AAC3C,gBAAItB,MAAK,KAAK;AACZ,cAAAuB,UAAS,MAAM,GAAG;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,MAAAD,OAAM,OAAO,IAAI,GAAG;AACpB,MAAAA,KAAI,UAAU,SAAU,GAAG;AAEzB,QAAAC,UAAS,YAAY,YAAY,CAAC;AAClC,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAAA,MACpB;AACA,MAAAD,KAAI,YAAY,WAAY;AAC1B,cAAM,EAAC,IAAI,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,KAAI;AAC3C,YAAItB,MAAK,KAAK;AACZ,UAAAuB,UAAS,MAAM,GAAG;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,eAAe,SAAU,KAAKvB,OAAMuB,WAAU;AAChD,QAAI,OAAOvB,UAAS,YAAY;AAC9B,MAAAuB,YAAWvB;AACX,MAAAA,QAAO,CAAC;AAAA,IACV;AACA,QAAI,KAAKA,MAAK;AACd,QAAI,CAAC,IAAI;AACP,UAAI,YAAY,sBAAsB,KAAK,CAAC,WAAW,GAAG,WAAW;AACrE,UAAI,UAAU,OAAO;AACnB,eAAOuB,UAAS,UAAU,KAAK;AAAA,MACjC;AACA,WAAK,UAAU;AACf,SAAG,aAAa,WAAY;AAC1B,YAAI,KAAK;AACP,UAAAA,UAAS,MAAM,GAAG;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI,KAAK,IAAI;AACb,QAAI,SAAS,GAAG,YAAY,WAAW;AACvC,QAAID,OAAM,OAAO,IAAI,EAAE;AAEvB,IAAAA,KAAI,UAAU,SAASC,SAAQ;AAC/B,IAAAD,KAAI,YAAY,SAAU,GAAG;AAC3B,UAAI,SAAS,EAAE,OAAO;AACtB,UAAI,CAAC,UAAU,OAAO,SAAS,IAAI,MAAM;AACvC,QAAAC,UAAS,YAAY,WAAW,CAAC;AAAA,MACnC,OAAO;AACL,eAAO,OAAO,EAAE;AAChB,cAAM,EAAC,IAAI,MAAM,IAAI,KAAK,MAAK;AAC/B,YAAIvB,MAAK,KAAK;AACZ,UAAAuB,UAAS,MAAM,GAAG;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,WAAW,SAAUvB,OAAMuB,WAAU;AACvC,mBAAe,mBAAmB,MAAM;AAGxC,QAAI,UAAU,YAAY,IAAI,MAAM;AACpC,QAAI,WAAW,QAAQ,QAAQ;AAC7B,cAAQ,OAAO,MAAM;AACrB,gBAAU,OAAO,MAAM;AAAA,IACzB;AACA,QAAID,OAAM,UAAU,eAAe,MAAM;AAEzC,IAAAA,KAAI,YAAY,WAAY;AAE1B,kBAAY,OAAO,MAAM;AACzB,UAAI,gBAAgB,KAAM,UAAU,cAAe;AACjD,eAAO,aAAa,MAAM;AAAA,MAC5B;AACA,MAAAC,UAAS,MAAM,EAAE,MAAM,KAAK,CAAC;AAAA,IAC/B;AAEA,IAAAD,KAAI,UAAU,SAASC,SAAQ;AAAA,EACjC;AAEA,MAAI,SAAS,UAAU,IAAI,MAAM;AAEjC,MAAI,QAAQ;AACV,UAAM,OAAO;AACb,QAAI,QAAQ,OAAO;AACnB,WAAO,SAAS,WAAY;AAC1B,eAAS,MAAM,GAAG;AAAA,IACpB,CAAC;AAAA,EACH;AAEA,MAAI,MAAM,UAAU,KAAK,QAAQ,eAAe;AAChD,cAAY,IAAI,QAAQ,GAAG;AAE3B,MAAI,kBAAkB,SAAU,GAAG;AACjC,QAAI,KAAK,EAAE,OAAO;AAClB,QAAI,EAAE,aAAa,GAAG;AACpB,aAAO,aAAa,EAAE;AAAA,IACxB;AAGA,QAAI,MAAM,EAAE,cAAc;AAI1B,QAAI,EAAE,aAAa,GAAG;AACpB,6BAAuB,EAAE;AAAA,IAC3B;AACA,QAAI,EAAE,aAAa,GAAG;AACpB,2BAAqB,EAAE;AAAA,IACzB;AAEA,QAAI,aAAa;AAAA,MACf;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,QAAI,IAAI,EAAE;AAEV,aAAS,OAAO;AACd,UAAI,YAAY,WAAW,IAAI,CAAC;AAChC;AACA,UAAI,WAAW;AACb,kBAAU,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AAEA,SAAK;AAAA,EACP;AAEA,MAAI,YAAY,SAAU,GAAG;AAE3B,UAAM,EAAE,OAAO;AAEf,QAAI,kBAAkB,WAAY;AAChC,UAAI,MAAM;AACV,gBAAU,OAAO,MAAM;AAAA,IACzB;AAEA,QAAI,UAAU,SAAUC,IAAG;AACzB,qBAAe,SAAS,iCAAiCA,GAAE,OAAO,KAAK;AACvE,8BAAwBA,GAAE,OAAO;AACjC,UAAI,MAAM;AACV,gBAAU,OAAO,MAAM;AAAA,IACzB;AASA,QAAI,MAAM,IAAI,YAAY;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,WAAW;AAEd,QAAI,gBAAgB;AACpB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,aAAS,gBAAgB;AACvB,UAAI,OAAO,gBAAgB,eAAe,CAAC,eAAe;AACxD;AAAA,MACF;AACA,UAAI,QAAQ;AAAA,QACV,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,gBAAU,IAAI,QAAQ;AAAA,QACpB;AAAA,QACA,QAAQ,IAAI;AAAA,MACd,CAAC;AACD,eAAS,MAAM,GAAG;AAAA,IACpB;AAEA,aAAS,sBAAsB;AAC7B,UAAI,OAAO,aAAa,eAAe,OAAO,YAAY,aAAa;AACrE;AAAA,MACF;AACA,UAAI,cAAc,SAAS;AAC3B,UAAI,eAAe,SAAS;AAC1B,qBAAa,QAAQ,WAAW;AAAA,MAClC,OAAO;AACL,gBAAQ,WAAW,IAAI,aAAa,KAAK;AAAA,MAC3C;AACA,cAAQ,WAAW;AACnB,UAAI,YAAY,UAAU,EAAE,IAAI,OAAO;AAAA,IACzC;AAKA,QAAI,YAAY,UAAU,EAAE,IAAI,UAAU,EAAE,YAAY,SAAUA,IAAG;AACnE,gBAAUA,GAAE,OAAO,UAAU,EAAE,IAAI,WAAW;AAC9C,0BAAoB;AAAA,IACtB;AAKA,cAAU,KAAK,SAAU,OAAO;AAC9B,iBAAW;AACX,0BAAoB;AAAA,IACtB,CAAC;AAKD,QAAI,CAAC,oBAAoB;AAEvB,2BAAqB,iBAAiB,KAAK,2BAA2B,KAAK;AAAA,IAC7E;AAEA,uBAAmB,KAAK,SAAU,KAAK;AACrC,oBAAc;AACd,oBAAc;AAAA,IAChB,CAAC;AAID,QAAI,aAAa,WAAY;AAC3B,sBAAgB;AAChB,oBAAc;AAAA,IAChB;AACA,QAAI,UAAU,SAAS,QAAQ;AAAA,EACjC;AAEA,MAAI,UAAU,SAAU,GAAG;AACzB,QAAI,MAAM,EAAE,OAAO,SAAS,EAAE,OAAO,MAAM;AAE3C,QAAI,CAAC,KAAK;AACR,YAAM;AAAA,IACR,WAAW,IAAI,QAAQ,qCAAqC,MAAM,IAAI;AACpE,YAAM,IAAI,MAAM,oHAAoH;AAAA,IACtI;AAEA,mBAAe,SAAS,GAAG;AAC3B,aAAS,YAAY,WAAW,GAAG,CAAC;AAAA,EACtC;AACF;AAEA,SAAS,QAAQ,WAAY;AAM3B,MAAI;AAGF,WAAO,OAAO,cAAc,eAAe,OAAO,gBAAgB;AAAA,EACpE,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAEA,SAAS,SAAUlB,UAAS;AAC1B,EAAAA,SAAQ,QAAQ,OAAO,UAAU,IAAI;AACvC;AAMA,SAAS,KAAK,kBAAkB,OAAO;AACrC,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAIqB,WAAU;AACd,QAAI,UAAU;AACd,QAAI,OAAO;AACX,QAAI,MAAM,iBAAiB;AAC3B,QAAI;AAEJ,aAAS,UAAU;AACjB,MAAAA;AACA,uBAAiB,SAAS,EAAE,EAAE,KAAK,WAAW,OAAO;AAAA,IACvD;AAEA,aAAS,SAAS;AAChB,UAAI,EAAE,SAAS,KAAK;AAElB,YAAI,KAAK;AACP,iBAAO,GAAG;AAAA,QACZ,OAAO;AACL,kBAAQ;AAAA,QACV;AAAA,MACF,OAAO;AACL,qBAAa;AAAA,MACf;AAAA,IACF;AAEA,aAAS,YAAY;AACnB,MAAAA;AACA,aAAO;AAAA,IACT;AAGA,aAAS,QAAQ,SAAS;AACxB,MAAAA;AACA,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AAEA,aAAS,eAAe;AACtB,aAAOA,WAAU,SAAS,UAAU,KAAK;AACvC,gBAAQ;AAAA,MACV;AAAA,IACF;AAEA,iBAAa;AAAA,EACf,CAAC;AACH;AAEA,IAAM,qBAAqB;AAC3B,IAAM,wBAAwB;AAC9B,IAAM,yBAAyB;AAC/B,IAAM,oBAAoB;AAE1B,IAAM,qBAAqB,CAAC;AAE5B,SAAS,8BAA8B,KAAK;AAC1C,QAAM,MAAM,IAAI,OAAO,IAAI;AAC3B,QAAM,OAAO,OAAO,IAAI;AACxB,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AACA,SAAO,KAAK,IAAI,EAAE,QAAQ,SAAU,UAAU;AAC5C,UAAM,MAAM,KAAK,QAAQ;AACzB,QAAI,OAAO,aAAa,IAAI,MAAM,IAAI,YAAY;AAAA,EACpD,CAAC;AACH;AAEA,SAAS,YAAY,IAAI;AACvB,MAAI,WAAW,KAAK,EAAE,GAAG;AACvB,WAAO,aAAa,mBAAmB,GAAG,MAAM,CAAC,CAAC;AAAA,EACpD;AACA,MAAI,GAAG,WAAW,SAAS,GAAG;AAC5B,WAAO,YAAY,mBAAmB,GAAG,MAAM,CAAC,CAAC;AAAA,EACnD;AACA,SAAO,mBAAmB,EAAE;AAC9B;AAEA,SAAS,wBAAwB,KAAK;AACpC,MAAI,CAAC,IAAI,gBAAgB,CAAC,OAAO,KAAK,IAAI,YAAY,GAAG;AACvD,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEA,SAAO,QAAQ,IAAI,OAAO,KAAK,IAAI,YAAY,EAAE,IAAI,SAAU,KAAK;AAClE,UAAM,aAAa,IAAI,aAAa,GAAG;AACvC,QAAI,WAAW,QAAQ,OAAO,WAAW,SAAS,UAAU;AAC1D,aAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,qBAAa,WAAW,MAAM,OAAO;AAAA,MACvC,CAAC,EAAE,KAAK,SAAU,KAAK;AACrB,mBAAW,OAAO;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,SAAS,aAAa,MAAM;AAC1B,MAAI,CAAC,KAAK,QAAQ;AAChB,WAAO;AAAA,EACT;AACA,QAAM,WAAW,SAAS,KAAK,MAAM,EAAE;AACvC,SAAO,aAAa,UAAU,aAAa;AAC7C;AAIA,SAAS,QAAQ,MAAM,MAAM;AAE3B,MAAI,aAAa,IAAI,GAAG;AACtB,UAAM,SAAS,KAAK,KAAK,OAAO,KAAK,OAAO,MAAM;AAElD,UAAM,SAAS,KAAK,OAAO,QAAQ,QAAQ,GAAG;AAC9C,WAAO,SAAS,mBAAmB,MAAM;AAAA,EAC3C;AAEA,QAAM,MAAM,SAAS,IAAI;AACzB,MAAI,IAAI,QAAQ,IAAI,UAAU;AAC5B,QAAI,OAAO,EAAC,UAAU,IAAI,MAAM,UAAU,IAAI,SAAQ;AAAA,EACxD;AAIA,QAAM,QAAQ,IAAI,KAAK,QAAQ,cAAc,EAAE,EAAE,MAAM,GAAG;AAE1D,MAAI,KAAK,MAAM,IAAI;AAEnB,MAAI,IAAI,GAAG,QAAQ,GAAG,MAAM,IAAI;AAC9B,QAAI,KAAK,mBAAmB,IAAI,EAAE;AAAA,EACpC;AAEA,MAAI,OAAO,MAAM,KAAK,GAAG;AAEzB,SAAO;AACT;AAGA,SAAS,SAAS,MAAM,MAAM;AAC5B,SAAO,OAAO,MAAM,KAAK,KAAK,MAAM,IAAI;AAC1C;AAGA,SAAS,OAAO,MAAM,MAAM;AAG1B,QAAM,UAAU,CAAC,KAAK,OAAO,KAAK;AAIlC,SAAO,KAAK,WAAW,QAAQ,KAAK,QAC5B,KAAK,OAAQ,MAAM,KAAK,OAAQ,MACjC,MAAM,KAAK,OAAO,UAAU;AACrC;AAEA,SAAS,YAAY,QAAQ;AAC3B,QAAM,YAAY,OAAO,KAAK,MAAM;AACpC,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,UAAU,IAAI,SAAO,MAAM,MAAM,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG;AACzF;AAEA,SAAS,gBAAgB,MAAM;AAC7B,QAAM,KAAM,OAAO,cAAc,eAAe,UAAU,YACtD,UAAU,UAAU,YAAY,IAAI;AACxC,QAAM,OAAO,GAAG,QAAQ,MAAM,MAAM;AACpC,QAAM,YAAY,GAAG,QAAQ,SAAS,MAAM;AAC5C,QAAM,SAAS,GAAG,QAAQ,MAAM,MAAM;AACtC,QAAM,QAAQ,EAAE,YAAY,SAAS,KAAK,WAAW;AACrD,UAAQ,QAAQ,aAAa,WAAW;AAC1C;AAGA,SAAS,UAAU,MAAM,UAAU;AAGjC,QAAM,MAAM;AAEZ,QAAM,OAAO,QAAQ,KAAK,MAAM,IAAI;AACpC,QAAM,QAAQ,SAAS,MAAM,EAAE;AAE/B,SAAO,MAAM,IAAI;AAEjB,QAAM,WAAW,SAAgB,KAAK,SAAS;AAAA;AAE7C,gBAAU,WAAW,CAAC;AACtB,cAAQ,UAAU,QAAQ,WAAW,IAAI,EAAE;AAE3C,cAAQ,cAAc;AAEtB,UAAI,KAAK,QAAQ,KAAK,MAAM;AAC1B,cAAM,QAAQ,KAAK,QAAQ,KAAK;AAChC,cAAM,MAAM,MAAM,WAAW,MAAM,MAAM;AACzC,cAAM,QAAQ,SAAS,SAAS,mBAAmB,GAAG,CAAC,CAAC;AACxD,gBAAQ,QAAQ,IAAI,iBAAiB,WAAW,KAAK;AAAA,MACvD;AAEA,YAAM,UAAU,KAAK,WAAW,CAAC;AACjC,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,KAAK;AAC1C,gBAAQ,QAAQ,OAAO,KAAK,QAAQ,GAAG,CAAC;AAAA,MAC1C,CAAC;AAGD,UAAI,gBAAgB,OAAO,GAAG;AAC5B,gBAAQ,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO,YAAY,KAAK,IAAI;AAAA,MACtE;AAEA,YAAM,WAAW,KAAK,SAAS;AAC/B,aAAO,MAAM,SAAS,KAAK,OAAO;AAAA,IACpC;AAAA;AAEA,WAAS,cAAc,MAAM,KAAK;AAChC,WAAO,WAAW,MAAM,YAAa,MAAM;AACzC,YAAM,EAAE,KAAK,WAAY;AACvB,eAAO,IAAI,MAAM,MAAM,IAAI;AAAA,MAC7B,CAAC,EAAE,MAAM,SAAU,GAAG;AACpB,cAAMJ,YAAW,KAAK,IAAI;AAC1B,QAAAA,UAAS,CAAC;AAAA,MACZ,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AAEA,WAAe,UAAU,KAAK,SAAS;AAAA;AAErC,YAAM,SAAS,CAAC;AAEhB,gBAAU,WAAW,CAAC;AACtB,cAAQ,UAAU,QAAQ,WAAW,IAAI,EAAE;AAE3C,UAAI,CAAC,QAAQ,QAAQ,IAAI,cAAc,GAAG;AACxC,gBAAQ,QAAQ,IAAI,gBAAgB,kBAAkB;AAAA,MACxD;AACA,UAAI,CAAC,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AAClC,gBAAQ,QAAQ,IAAI,UAAU,kBAAkB;AAAA,MAClD;AAEA,YAAM,WAAW,MAAM,SAAS,KAAK,OAAO;AAC5C,aAAO,KAAK,SAAS;AACrB,aAAO,SAAS,SAAS;AACzB,YAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,aAAO,OAAO;AACd,UAAI,CAAC,OAAO,IAAI;AACd,eAAO,KAAK,SAAS,OAAO;AAC5B,cAAM,MAAM,0BAA0B,OAAO,IAAI;AACjD,cAAM;AAAA,MACR;AAEA,UAAI,MAAM,QAAQ,OAAO,IAAI,GAAG;AAC9B,eAAO,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACzC,cAAI,EAAE,SAAS,EAAE,SAAS;AACxB,mBAAO,0BAA0B,CAAC;AAAA,UACpC,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AAAA;AAEA,MAAI;AAEJ,WAAe,QAAQ;AAAA;AACrB,UAAI,KAAK,YAAY;AACnB,eAAO,QAAQ,QAAQ;AAAA,MACzB;AAKA,UAAI,cAAc;AAChB,eAAO;AAAA,MACT;AAEA,qBAAe,UAAU,KAAK,EAAE,MAAM,SAAU,KAAK;AACnD,YAAI,OAAO,IAAI,UAAU,IAAI,WAAW,KAAK;AAE3C,uBAAa,KAAK,iDAAiD;AACnE,iBAAO,UAAU,OAAO,EAAC,QAAQ,MAAK,CAAC;AAAA,QACzC,OAAO;AACL,iBAAO,QAAQ,OAAO,GAAG;AAAA,QAC3B;AAAA,MACF,CAAC,EAAE,MAAM,SAAU,KAAK;AAItB,YAAI,OAAO,IAAI,UAAU,IAAI,WAAW,KAAK;AAC3C,iBAAO;AAAA,QACT;AACA,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC3B,CAAC;AAED,mBAAa,MAAM,WAAY;AAC7B,uBAAe;AAAA,MACjB,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAEA,WAAS,WAAY;AACnB,aAAS,MAAM,GAAG;AAAA,EACpB,CAAC;AAED,MAAI,UAAU;AAGd,MAAI,OAAO,WAAY;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,cAAc,MAAM,SAAgBA,WAAU;AAAA;AACrD,UAAI;AACJ,UAAI;AACF,cAAM,WAAW,MAAM,SAAS,OAAO,MAAM,EAAE,CAAC;AAChD,iBAAS,MAAM,SAAS,KAAK;AAAA,MAC/B,SAAS,KAAK;AACZ,iBAAS,CAAC;AAAA,MACZ;AAGA,YAAM,UAAW,UAAU,OAAO,OAAS,OAAO,OAAO,KAAK,KAAM,SAAS,MAAM,EAAE;AACrF,MAAAA,UAAS,MAAM,OAAO;AAAA,IACxB;AAAA,GAAC;AAID,MAAI,UAAU,cAAc,WAAW,SAAgBvB,OAAMuB,WAAU;AAAA;AACrE,UAAI,OAAOvB,UAAS,YAAY;AAC9B,QAAAuB,YAAWvB;AACX,QAAAA,QAAO,CAAC;AAAA,MACV;AACA,MAAAA,QAAO,MAAMA,KAAI;AAEjB,YAAM,UAAU,SAAS,MAAM,UAAU,GAAG,EAAC,QAAQ,OAAM,CAAC;AAE5D,eAAS,OAAO;AACd,YAAI,KAAK,SAAU,KAAK,KAAK;AAI3B,cAAI,OAAO,CAAC,IAAI,iBAAiB;AAC/B,YAAAuB,UAAS,MAAM,EAAC,IAAI,KAAI,CAAC;AAAA,UAC3B,OAAO;AACL,uBAAW,MAAMvB,MAAK,YAAY,GAAG;AAAA,UACvC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,WAAK;AAAA,IACP;AAAA,GAAC;AAED,MAAI,UAAU,WAAW,WAAW,SAAUA,OAAMuB,WAAU;AAC5D,UAAMhC,QAAO;AAEb,aAAe,UAAU,IAAI;AAAA;AAC3B,cAAM,SAAS,CAAC;AAChB,YAAIS,MAAK,MAAM;AACb,iBAAO,OAAO;AAAA,QAChB;AACA,YAAIA,MAAK,aAAa;AAEpB,iBAAO,cAAc;AAAA,QACvB;AACA,YAAIA,MAAK,QAAQ;AACf,iBAAO,SAAS;AAAA,QAClB;AACA,YAAI;AACF,gBAAM,SAAS,MAAM,UAAU,SAAS,MAAM,cAAc,YAAY,MAAM,CAAC,GAAG;AAAA,YAChF,QAAQ;AAAA,YACR,MAAM,KAAK,UAAU,EAAE,MAAMA,MAAK,KAAI,CAAC;AAAA,UACzC,CAAC;AAED,cAAIA,MAAK,eAAeA,MAAK,QAAQ;AACnC,mBAAO,KAAK,QAAQ,QAAQ,SAAU,KAAK;AACzC,kBAAI,KAAK,QAAQ,6BAA6B;AAAA,YAChD,CAAC;AAAA,UACH;AACA,aAAG,MAAM,OAAO,IAAI;AAAA,QACtB,SAAS,OAAO;AACd,aAAG,KAAK;AAAA,QACV;AAAA,MACF;AAAA;AAGA,aAAS,gBAAgB;AAEvB,YAAM,YAAY;AAClB,YAAM,aAAa,KAAK,KAAKA,MAAK,KAAK,SAAS,SAAS;AACzD,UAAI,UAAU;AACd,YAAM,UAAU,IAAI,MAAM,UAAU;AAEpC,eAAS,SAAS,UAAU;AAC1B,eAAO,SAAU,KAAK,KAAK;AAEzB,kBAAQ,QAAQ,IAAI,IAAI;AACxB,cAAI,EAAE,YAAY,YAAY;AAC5B,YAAAuB,UAAS,MAAM,EAAC,SAAS,QAAQ,KAAK,EAAC,CAAC;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAM,UAAU,KAAKvB,OAAM,CAAC,QAAQ,eAAe,UAAU,QAAQ,CAAC;AACtE,gBAAQ,OAAOA,MAAK,KAAK;AAAA,UAAM,IAAI;AAAA,UACjC,KAAK,IAAIA,MAAK,KAAK,SAAS,IAAI,KAAK,SAAS;AAAA,QAAC;AACjD,gBAAQT,OAAM,SAAS,SAAS,CAAC,CAAC;AAAA,MACpC;AAAA,IACF;AAGA,UAAMqC,SAAQ,OAAO,MAAM,EAAE;AAC7B,UAAM,kBAAkB,mBAAmBA,MAAK;AAGhD,QAAI,OAAO,oBAAoB,WAAW;AAExC,gBAAU,SAAU,KAAK,KAAK;AAC5B,YAAI,KAAK;AACP,6BAAmBA,MAAK,IAAI;AAC5B;AAAA,YACE,IAAI;AAAA,YACJ;AAAA,UAEF;AACA,wBAAc;AAAA,QAChB,OAAO;AACL,6BAAmBA,MAAK,IAAI;AAC5B,UAAAL,UAAS,MAAM,GAAG;AAAA,QACpB;AAAA,MACF,CAAC;AAAA,IACH,WAAW,iBAAiB;AAC1B,gBAAUA,SAAQ;AAAA,IACpB,OAAO;AACL,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AAKD,MAAI,QAAQ,SAAgBA,WAAU;AAAA;AACpC,UAAI;AACF,cAAM,MAAM;AACZ,cAAM,WAAW,MAAM,SAAS,SAAS,MAAM,EAAE,CAAC;AAClD,cAAM,OAAO,MAAM,SAAS,KAAK;AACjC,aAAK,OAAO,SAAS,MAAM,EAAE;AAC7B,QAAAA,UAAS,MAAM,IAAI;AAAA,MACrB,SAAS,KAAK;AACZ,QAAAA,UAAS,GAAG;AAAA,MACd;AAAA,IACF;AAAA;AAEA,MAAI,QAAQ,SAAgB,MAAM,SAAS;AAAA;AACzC,YAAM,MAAM;AACZ,YAAM,MAAM,KAAK,UAAU,GAAG,CAAC,MAAM,MACrC,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,IAC9B,SAAS,MAAM,IAAI;AACnB,aAAO,SAAS,KAAK,OAAO;AAAA,IAC9B;AAAA;AAKA,MAAI,MAAM,cAAc,OAAO,SAAgB,IAAIvB,OAAMuB,WAAU;AAAA;AAEjE,UAAI,OAAOvB,UAAS,YAAY;AAC9B,QAAAuB,YAAWvB;AACX,QAAAA,QAAO,CAAC;AAAA,MACV;AACA,MAAAA,QAAO,MAAMA,KAAI;AAGjB,YAAM,SAAS,CAAC;AAEhB,UAAIA,MAAK,MAAM;AACb,eAAO,OAAO;AAAA,MAChB;AAEA,UAAIA,MAAK,WAAW;AAClB,eAAO,YAAY;AAAA,MACrB;AAEA,UAAIA,MAAK,QAAQ;AACf,eAAO,SAAS;AAAA,MAClB;AAEA,UAAIA,MAAK,WAAW;AAClB,YAAIA,MAAK,cAAc,OAAO;AAC5B,UAAAA,MAAK,YAAY,KAAK,UAAUA,MAAK,SAAS;AAAA,QAChD;AACA,eAAO,YAAYA,MAAK;AAAA,MAC1B;AAEA,UAAIA,MAAK,KAAK;AACZ,eAAO,MAAMA,MAAK;AAAA,MACpB;AAEA,UAAIA,MAAK,WAAW;AAClB,eAAO,YAAYA,MAAK;AAAA,MAC1B;AAGA,UAAIA,MAAK,YAAY;AACnB,eAAO,aAAaA,MAAK;AAAA,MAC3B;AAEA,WAAK,YAAY,EAAE;AAEnB,eAAS,iBAAiB,KAAK;AAC7B,cAAM,OAAO,IAAI;AACjB,cAAM,YAAY,QAAQ,OAAO,KAAK,IAAI;AAC1C,YAAI,CAAC,QAAQ,CAAC,UAAU,QAAQ;AAC9B;AAAA,QACF;AAKA,iBAAe,UAAU,UAAU;AAAA;AACjC,kBAAM,MAAM,KAAK,QAAQ;AACzB,kBAAM,OAAO,YAAY,IAAI,GAAG,IAAI,MAAM,mBAAmB,QAAQ,IACjE,UAAU,IAAI;AAElB,kBAAM,WAAW,MAAM,SAAS,SAAS,MAAM,IAAI,CAAC;AAEpD,gBAAI;AACJ,gBAAI,YAAY,UAAU;AACxB,qBAAO,MAAM,SAAS,OAAO;AAAA,YAC/B,OAAO;AAEL,qBAAO,MAAM,SAAS,KAAK;AAAA,YAC7B;AAEA,gBAAI;AACJ,gBAAIA,MAAK,QAAQ;AACf,oBAAM,sBAAsB,OAAO,yBAAyB,KAAK,WAAW,MAAM;AAClF,kBAAI,CAAC,uBAAuB,oBAAoB,KAAK;AACnD,qBAAK,OAAO,IAAI;AAAA,cAClB;AACA,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO,MAAM,IAAI,QAAQ,SAAU,SAAS;AAC1C,6BAAa,MAAM,OAAO;AAAA,cAC5B,CAAC;AAAA,YACH;AAEA,mBAAO,IAAI;AACX,mBAAO,IAAI;AACX,gBAAI,OAAO;AAAA,UACb;AAAA;AAEA,cAAM,mBAAmB,UAAU,IAAI,SAAU,UAAU;AACzD,iBAAO,WAAY;AACjB,mBAAO,UAAU,QAAQ;AAAA,UAC3B;AAAA,QACF,CAAC;AAID,eAAO,KAAK,kBAAkB,CAAC;AAAA,MACjC;AAEA,eAAS,oBAAoB,WAAW;AACtC,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO,QAAQ,IAAI,UAAU,IAAI,SAAU,KAAK;AAC9C,gBAAI,IAAI,IAAI;AACV,qBAAO,iBAAiB,IAAI,EAAE;AAAA,YAChC;AAAA,UACF,CAAC,CAAC;AAAA,QACJ;AACA,eAAO,iBAAiB,SAAS;AAAA,MACnC;AAEA,YAAM,MAAM,SAAS,MAAM,KAAK,YAAY,MAAM,CAAC;AACnD,UAAI;AACF,cAAM,MAAM,MAAM,UAAU,GAAG;AAC/B,YAAIA,MAAK,aAAa;AACpB,gBAAM,oBAAoB,IAAI,IAAI;AAAA,QACpC;AACA,QAAAuB,UAAS,MAAM,IAAI,IAAI;AAAA,MACzB,SAAS,OAAO;AACd,cAAM,QAAQ;AACd,QAAAA,UAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,GAAC;AAID,MAAI,SAAS,cAAc,UAAU,SAAgB,SAAS,WAAWvB,OAAM,IAAI;AAAA;AACjF,UAAI;AACJ,UAAI,OAAO,cAAc,UAAU;AAEjC,cAAM;AAAA,UACJ,KAAK;AAAA,UACL,MAAM;AAAA,QACR;AACA,YAAI,OAAOA,UAAS,YAAY;AAC9B,eAAKA;AACL,UAAAA,QAAO,CAAC;AAAA,QACV;AAAA,MACF,OAAO;AAEL,cAAM;AACN,YAAI,OAAO,cAAc,YAAY;AACnC,eAAK;AACL,UAAAA,QAAO,CAAC;AAAA,QACV,OAAO;AACL,eAAKA;AACL,UAAAA,QAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,SAAU,IAAI,QAAQA,MAAK;AACjC,YAAM,MAAM,SAAS,MAAM,YAAY,IAAI,GAAG,CAAC,IAAI,UAAU;AAE7D,UAAI;AACF,cAAM,SAAS,MAAM,UAAU,KAAK,EAAC,QAAQ,SAAQ,CAAC;AACtD,WAAG,MAAM,OAAO,IAAI;AAAA,MACtB,SAAS,OAAO;AACd,WAAG,KAAK;AAAA,MACV;AAAA,IACF;AAAA,GAAC;AAED,WAAS,mBAAmB,cAAc;AACxC,WAAO,aAAa,MAAM,GAAG,EAAE,IAAI,kBAAkB,EAAE,KAAK,GAAG;AAAA,EACjE;AAGA,MAAI,gBAAgB,cAAc,iBAAiB,SAAgB,OAAO,cAChBA,OAAMuB,WAAU;AAAA;AACxE,UAAI,OAAOvB,UAAS,YAAY;AAC9B,QAAAuB,YAAWvB;AACX,QAAAA,QAAO,CAAC;AAAA,MACV;AACA,YAAM,SAASA,MAAK,MAAO,UAAUA,MAAK,MAAO;AACjD,YAAM,MAAM,SAAS,MAAM,YAAY,KAAK,CAAC,IAAI,MAC7C,mBAAmB,YAAY,IAAI;AACvC,UAAI;AACJ,UAAI;AACF,cAAM,WAAW,MAAM,SAAS,KAAK,EAAC,QAAQ,MAAK,CAAC;AAEpD,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM;AAAA,QACR;AAEA,sBAAc,SAAS,QAAQ,IAAI,cAAc;AACjD,YAAI;AACJ,YAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,WAAW,OAAO,SAAS,WAAW,YAAY;AAC/F,iBAAO,MAAM,SAAS,OAAO;AAAA,QAC/B,OAAO;AAEL,iBAAO,MAAM,SAAS,KAAK;AAAA,QAC7B;AAGA,YAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,SAAS;AACtD,gBAAM,sBAAsB,OAAO,yBAAyB,KAAK,WAAW,MAAM;AAClF,cAAI,CAAC,uBAAuB,oBAAoB,KAAK;AACnD,iBAAK,OAAO;AAAA,UACd;AAAA,QACF;AACA,QAAAuB,UAAS,MAAM,IAAI;AAAA,MACrB,SAAS,KAAK;AACZ,QAAAA,UAAS,GAAG;AAAA,MACd;AAAA,IACF;AAAA,GAAC;AAGD,MAAI,mBAAoB,cAAc,oBAAoB,SACxD,OACA,cACA,QACAA,WACA;AAAA;AACA,YAAM,MAAM,SAAS,MAAM,YAAY,KAAK,IAAI,MAAM,mBAAmB,YAAY,CAAC,IAAI,UAAU;AAEpG,UAAI;AACF,cAAM,SAAS,MAAM,UAAU,KAAK,EAAC,QAAQ,SAAQ,CAAC;AACtD,QAAAA,UAAS,MAAM,OAAO,IAAI;AAAA,MAC5B,SAAS,OAAO;AACd,QAAAA,UAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,GAAC;AAKD,MAAI,gBAAgB,cAAc,iBAAiB,SACjD,OACA,cACA,QACA,MACA,MACAA,WACA;AAAA;AACA,UAAI,OAAO,SAAS,YAAY;AAC9B,QAAAA,YAAW;AACX,eAAO;AACP,eAAO;AACP,iBAAS;AAAA,MACX;AACA,YAAM,KAAK,YAAY,KAAK,IAAI,MAAM,mBAAmB,YAAY;AACrE,UAAI,MAAM,SAAS,MAAM,EAAE;AAC3B,UAAI,QAAQ;AACV,eAAO,UAAU;AAAA,MACnB;AAEA,UAAI,OAAO,SAAS,UAAU;AAE5B,YAAI;AACJ,YAAI;AACF,mBAAS,SAAS,IAAI;AAAA,QACxB,SAAS,KAAK;AACZ,iBAAOA,UAAS;AAAA,YAAY;AAAA,YACZ;AAAA,UAAyC,CAAC;AAAA,QAC5D;AACA,eAAO,SAAS,mBAAmB,QAAQ,IAAI,IAAI;AAAA,MACrD;AAEA,UAAI;AAEF,cAAM,SAAS,MAAM,UAAU,KAAK;AAAA,UAClC,SAAS,IAAI,EAAE,EAAC,gBAAgB,KAAI,CAAC;AAAA,UACrC,QAAQ;AAAA,UACR,MAAM;AAAA,QACR,CAAC;AACD,QAAAA,UAAS,MAAM,OAAO,IAAI;AAAA,MAC5B,SAAS,OAAO;AACd,QAAAA,UAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,GAAC;AAID,MAAI,YAAY,SAAgB,KAAKvB,OAAMuB,WAAU;AAAA;AAInD,UAAI,YAAYvB,MAAK;AAErB,UAAI;AACF,cAAM,MAAM;AACZ,cAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,uBAAuB,CAAC;AAGvD,cAAM,SAAS,MAAM,UAAU,SAAS,MAAM,YAAY,GAAG;AAAA,UAC3D,QAAQ;AAAA,UACR,MAAM,KAAK,UAAU,GAAG;AAAA,QAC1B,CAAC;AACD,QAAAuB,UAAS,MAAM,OAAO,IAAI;AAAA,MAC5B,SAAS,OAAO;AACd,QAAAA,UAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA;AAGA,MAAI,OAAO,SAAgB,KAAKvB,OAAMuB,WAAU;AAAA;AAC9C,UAAI;AACF,cAAM,MAAM;AACZ,cAAM,wBAAwB,GAAG;AAEjC,cAAM,SAAS,MAAM,UAAU,SAAS,MAAM,YAAY,IAAI,GAAG,CAAC,GAAG;AAAA,UACnE,QAAQ;AAAA,UACR,MAAM,KAAK,UAAU,GAAG;AAAA,QAC1B,CAAC;AACD,QAAAA,UAAS,MAAM,OAAO,IAAI;AAAA,MAC5B,SAAS,OAAO;AACd,cAAM,QAAQ,OAAO,IAAI;AACzB,QAAAA,UAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA;AAKA,MAAI,UAAU,cAAc,WAAW,SAAgBvB,OAAMuB,WAAU;AAAA;AACrE,UAAI,OAAOvB,UAAS,YAAY;AAC9B,QAAAuB,YAAWvB;AACX,QAAAA,QAAO,CAAC;AAAA,MACV;AACA,MAAAA,QAAO,MAAMA,KAAI;AAGjB,YAAM,SAAS,CAAC;AAChB,UAAI;AACJ,UAAI,SAAS;AAEb,UAAIA,MAAK,WAAW;AAClB,eAAO,YAAY;AAAA,MACrB;AAGA,UAAIA,MAAK,YAAY;AACnB,eAAO,aAAa;AAAA,MACtB;AAEA,UAAIA,MAAK,YAAY;AACnB,eAAO,aAAa;AAAA,MACtB;AAEA,UAAIA,MAAK,cAAc;AACrB,eAAO,eAAe;AAAA,MACxB;AAGA,UAAIA,MAAK,aAAa;AACpB,eAAO,cAAc;AAAA,MACvB;AAEA,UAAIA,MAAK,KAAK;AACZ,eAAO,MAAM,KAAK,UAAUA,MAAK,GAAG;AAAA,MACtC;AAEA,UAAIA,MAAK,WAAW;AAClB,QAAAA,MAAK,WAAWA,MAAK;AAAA,MACvB;AAEA,UAAIA,MAAK,UAAU;AACjB,eAAO,WAAW,KAAK,UAAUA,MAAK,QAAQ;AAAA,MAChD;AAEA,UAAIA,MAAK,SAAS;AAChB,QAAAA,MAAK,SAASA,MAAK;AAAA,MACrB;AAEA,UAAIA,MAAK,QAAQ;AACf,eAAO,SAAS,KAAK,UAAUA,MAAK,MAAM;AAAA,MAC5C;AAEA,UAAI,OAAOA,MAAK,kBAAkB,aAAa;AAC7C,eAAO,gBAAgB,CAAC,CAACA,MAAK;AAAA,MAChC;AAEA,UAAI,OAAOA,MAAK,UAAU,aAAa;AACrC,eAAO,QAAQA,MAAK;AAAA,MACtB;AAEA,UAAI,OAAOA,MAAK,SAAS,aAAa;AACpC,eAAO,OAAOA,MAAK;AAAA,MACrB;AAEA,YAAM,WAAW,YAAY,MAAM;AAEnC,UAAI,OAAOA,MAAK,SAAS,aAAa;AACpC,iBAAS;AACT,eAAO,EAAC,MAAMA,MAAK,KAAI;AAAA,MACzB;AAEA,UAAI;AACF,cAAM,SAAS,MAAM,UAAU,SAAS,MAAM,cAAc,QAAQ,GAAG;AAAA,UACrE;AAAA,UACA,MAAM,KAAK,UAAU,IAAI;AAAA,QAC3B,CAAC;AACD,YAAIA,MAAK,gBAAgBA,MAAK,eAAeA,MAAK,QAAQ;AACxD,iBAAO,KAAK,KAAK,QAAQ,6BAA6B;AAAA,QACxD;AACA,QAAAuB,UAAS,MAAM,OAAO,IAAI;AAAA,MAC5B,SAAS,OAAO;AACd,QAAAA,UAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,GAAC;AAKD,MAAI,WAAW,SAAUvB,OAAM;AAM7B,UAAM,YAAY,gBAAgBA,QAAOA,MAAK,aAAa;AAE3D,IAAAA,QAAO,MAAMA,KAAI;AAEjB,QAAIA,MAAK,cAAc,EAAE,eAAeA,QAAO;AAC7C,MAAAA,MAAK,YAAY;AAAA,IACnB;AAEA,QAAI,iBAAkB,aAAaA,QAAQA,MAAK,UAAU,KAAK;AAG/D,QAAI,aAAaA,SAAQA,MAAK,WAC3B,iBAAiBA,MAAK,UAAW,wBAAwB;AACxD,uBAAiBA,MAAK,UAAU;AAAA,IACpC;AAGA,QAAI,eAAeA,SAAQA,MAAK,aAC5B,iBAAiBA,MAAK,YAAa,wBAAwB;AAC3D,uBAAiBA,MAAK,YAAY;AAAA,IACtC;AAEA,UAAM,SAAS,CAAC;AAChB,QAAI,aAAaA,SAAQA,MAAK,SAAS;AACrC,aAAO,UAAUA,MAAK;AAAA,IACxB;AAEA,UAAM,QAAS,OAAOA,MAAK,UAAU,cAAeA,MAAK,QAAQ;AACjE,QAAI,cAAc;AAElB,QAAIA,MAAK,OAAO;AACd,aAAO,QAAQA,MAAK;AAAA,IACtB;AAEA,QAAIA,MAAK,gBAAgBA,MAAK,UAAU,OAAOA,MAAK,WAAW,YAAY;AACzE,aAAO,eAAe;AAAA,IACxB;AAEA,QAAIA,MAAK,aAAa;AACpB,aAAO,cAAc;AAAA,IACvB;AAEA,QAAIA,MAAK,YAAY;AACnB,aAAO,OAAO;AAAA,IAChB;AAEA,QAAIA,MAAK,cAAc;AACrB,aAAO,eAAeA,MAAK;AAAA,IAC7B;AAEA,QAAIA,MAAK,WAAW;AAClB,aAAO,YAAY;AAAA,IACrB;AAEA,QAAIA,MAAK,YAAY;AACnB,aAAO,aAAa;AAAA,IACtB;AAGA,QAAIA,MAAK,YAAY;AACnB,aAAO,aAAa;AAAA,IACtB;AAEA,QAAI,eAAeA,OAAM;AAEvB,UAAIA,MAAK,WAAW;AAClB,eAAO,YAAYA,MAAK;AAAA,MAC1B;AAAA,IACF;AAEA,QAAIA,MAAK,UAAU,OAAOA,MAAK,WAAW,UAAU;AAClD,aAAO,SAASA,MAAK;AAAA,IACvB;AAEA,QAAIA,MAAK,QAAQ,OAAOA,MAAK,SAAS,UAAU;AAC9C,aAAO,SAAS;AAChB,aAAO,OAAOA,MAAK;AAAA,IACrB;AAIA,QAAIA,MAAK,gBAAgB,OAAOA,MAAK,iBAAiB,UAAU;AAC9D,iBAAW,cAAcA,MAAK,cAAc;AAE1C,YAAI,OAAO,UAAU,eAAe,KAAKA,MAAK,cAAc,UAAU,GAAG;AACvE,iBAAO,UAAU,IAAIA,MAAK,aAAa,UAAU;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS;AACb,QAAI;AAEJ,QAAIA,MAAK,SAAS;AAGhB,aAAO,SAAS;AAChB,eAAS;AACT,aAAO,EAAC,SAASA,MAAK,QAAQ;AAAA,IAChC,WAESA,MAAK,UAAU;AAEtB,aAAO,SAAS;AAChB,eAAS;AACT,aAAO,EAAC,UAAUA,MAAK,SAAS;AAAA,IAClC;AAEA,UAAM,aAAa,IAAI,gBAAgB;AACvC,QAAI;AAIJ,UAAM,YAAY,SAAgB,OAAOuB,WAAU;AAAA;AACjD,YAAIvB,MAAK,SAAS;AAChB;AAAA,QACF;AACA,eAAO,QAAQ;AAGf,YAAI,OAAO,OAAO,UAAU,UAAU;AACpC,iBAAO,QAAQ,KAAK,UAAU,OAAO,KAAK;AAAA,QAC5C;AAEA,YAAIA,MAAK,YAAY;AACnB,cAAI,OAAO;AACT,mBAAO,QAAQ;AAAA,UACjB;AAAA,QACF,OAAO;AACL,iBAAO,QAAS,CAAC,SAAS,cAAc,YACtC,YAAY;AAAA,QAChB;AAGA,cAAM,MAAM,SAAS,MAAM,aAAa,YAAY,MAAM,CAAC;AAC3D,cAAM,YAAY;AAAA,UAChB,QAAQ,WAAW;AAAA,UACnB;AAAA,UACA,MAAM,KAAK,UAAU,IAAI;AAAA,QAC3B;AACA,yBAAiB;AAGjB,YAAIA,MAAK,SAAS;AAChB;AAAA,QACF;AAGA,YAAI;AACF,gBAAM,MAAM;AACZ,gBAAM,SAAS,MAAM,UAAU,KAAK,SAAS;AAC7C,UAAAuB,UAAS,MAAM,OAAO,IAAI;AAAA,QAC5B,SAAS,OAAO;AACd,UAAAA,UAAS,KAAK;AAAA,QAChB;AAAA,MACF;AAAA;AAKA,UAAM,UAAU,EAAC,SAAS,CAAC,EAAC;AAE5B,UAAM,UAAU,SAAU,KAAK,KAAK;AAClC,UAAIvB,MAAK,SAAS;AAChB;AAAA,MACF;AACA,UAAI,qBAAqB;AAEzB,UAAI,OAAO,IAAI,SAAS;AACtB,6BAAqB,IAAI,QAAQ;AACjC,gBAAQ,WAAW,IAAI;AACvB,YAAI,UAAU;AACd,YAAI,UAAU;AAGd,YAAI,OAAO,IAAI,YAAY,UAAU;AACnC,oBAAU,IAAI;AAAA,QAChB;AACA,YAAI,OAAO,QAAQ,aAAa,YAAY,OAAO,QAAQ,aAAa,UAAU;AAChF,oBAAU,QAAQ;AAAA,QACpB;AAEA,cAAM,MAAM,CAAC;AACb,YAAI,QAAQA,MAAK;AACjB,YAAI,UAAU,IAAI,QAAQ,OAAO,SAAU,GAAG;AAC5C;AACA,gBAAM,MAAM,aAAaA,KAAI,EAAE,CAAC;AAChC,cAAI,KAAK;AACP,gBAAIA,MAAK,gBAAgBA,MAAK,eAAeA,MAAK,QAAQ;AACxD,4CAA8B,CAAC;AAAA,YACjC;AACA,gBAAIA,MAAK,aAAa;AACpB,sBAAQ,QAAQ,KAAK,CAAC;AAAA,YACxB;AACA,YAAAA,MAAK,SAAS,GAAG,SAAS,OAAO;AAAA,UACnC;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,WAAW,KAAK;AAGd,QAAAA,MAAK,UAAU;AACf,QAAAA,MAAK,SAAS,GAAG;AACjB;AAAA,MACF;AAIA,UAAI,OAAO,IAAI,UAAU;AACvB,yBAAiB,IAAI;AAAA,MACvB;AAEA,YAAM,WAAY,SAAS,eAAe,KACvC,OAAO,qBAAqB,aAC5BA,MAAK;AAER,UAAKA,MAAK,cAAc,EAAE,SAAS,eAAe,MAAO,CAAC,UAAU;AAElE,iBAAS,WAAY;AAAE,oBAAU,gBAAgB,OAAO;AAAA,QAAG,CAAC;AAAA,MAC9D,OAAO;AAEL,QAAAA,MAAK,SAAS,MAAM,OAAO;AAAA,MAC7B;AAAA,IACF;AAEA,cAAUA,MAAK,SAAS,GAAG,OAAO;AAGlC,WAAO;AAAA,MACL,QAAQ,WAAY;AAClB,QAAAA,MAAK,UAAU;AACf,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAKA,MAAI,WAAW,cAAc,YAAY,SAAgB,KAAKA,OAAMuB,WAAU;AAAA;AAE5E,UAAI,OAAOvB,UAAS,YAAY;AAC9B,QAAAuB,YAAWvB;AACX,QAAAA,QAAO,CAAC;AAAA,MACV;AAEA,UAAI;AAEF,cAAM,SAAS,MAAM,UAAU,SAAS,MAAM,YAAY,GAAG;AAAA,UAC3D,QAAQ;AAAA,UACR,MAAM,KAAK,UAAU,GAAG;AAAA,QAC1B,CAAC;AACD,QAAAuB,UAAS,MAAM,OAAO,IAAI;AAAA,MAC5B,SAAS,OAAO;AACd,QAAAA,UAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,GAAC;AAED,MAAI,SAAS,SAAUA,WAAU;AAC/B,IAAAA,UAAS;AAAA,EACX;AAEA,MAAI,WAAW,SAAgB,SAASA,WAAU;AAAA;AAChD,UAAI;AACF,cAAM,OAAO,MAAM,UAAU,SAAS,MAAM,EAAE,GAAG,EAAC,QAAQ,SAAQ,CAAC;AACnE,QAAAA,UAAS,MAAM,IAAI;AAAA,MACrB,SAAS,OAAO;AACd,YAAI,MAAM,WAAW,KAAK;AACxB,UAAAA,UAAS,MAAM,EAAC,IAAI,KAAI,CAAC;AAAA,QAC3B,OAAO;AACL,UAAAA,UAAS,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA;AACF;AAGA,UAAU,QAAQ,WAAY;AAC5B,SAAO;AACT;AAEA,SAAS,YAAajB,UAAS;AAC7B,EAAAA,SAAQ,QAAQ,QAAQ,WAAW,KAAK;AACxC,EAAAA,SAAQ,QAAQ,SAAS,WAAW,KAAK;AAC3C;AAEA,IAAM,kBAAN,MAAM,yBAAwB,MAAM;AAAA,EAClC,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI;AACF,YAAM,kBAAkB,MAAM,gBAAe;AAAA,IAC/C,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,IAAM,gBAAN,MAAM,uBAAsB,MAAM;AAAA,EAChC,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI;AACF,YAAM,kBAAkB,MAAM,cAAa;AAAA,IAC7C,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,IAAM,eAAN,MAAM,sBAAqB,MAAM;AAAA,EAC/B,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI;AACF,YAAM,kBAAkB,MAAM,aAAY;AAAA,IAC5C,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,SAAS,iBAAiB,SAAS,UAAU;AAC3C,MAAI,UAAU;AACZ,YAAQ,KAAK,SAAU,KAAK;AAC1B,eAAS,WAAY;AACnB,iBAAS,MAAM,GAAG;AAAA,MACpB,CAAC;AAAA,IACH,GAAG,SAAU,QAAQ;AACnB,eAAS,WAAY;AACnB,iBAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,SAAS,YAAY,KAAK;AACxB,SAAO,YAAa,MAAM;AACxB,QAAI,KAAK,KAAK,IAAI;AAClB,QAAI,UAAU,IAAI,MAAM,MAAM,IAAI;AAClC,QAAI,OAAO,OAAO,YAAY;AAC5B,uBAAiB,SAAS,EAAE;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,IAAI,SAAS,qBAAqB;AACzC,SAAO,QAAQ,KAAK,SAAU,KAAK;AACjC,WAAO,oBAAoB,EAAE,KAAK,WAAY;AAC5C,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,SAAU,QAAQ;AACnB,WAAO,oBAAoB,EAAE,KAAK,WAAY;AAC5C,YAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,cAAcP,QAAO,gBAAgB;AAC5C,SAAO,WAAY;AACjB,QAAI,OAAO;AACX,QAAI,OAAO;AACX,WAAOA,OAAM,IAAI,WAAY;AAC3B,aAAO,eAAe,MAAM,MAAM,IAAI;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAIA,SAAS,KAAK,KAAK;AACjB,MAAI,SAAS,IAAI,IAAI,GAAG;AACxB,MAAI,SAAS,IAAI,MAAM,OAAO,IAAI;AAClC,MAAI,QAAQ;AACZ,SAAO,QAAQ,SAAU,OAAO;AAC9B,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,KAAK;AAC3B,MAAI,SAAS,IAAI,MAAM,IAAI,IAAI;AAC/B,MAAI,QAAQ;AACZ,MAAI,QAAQ,SAAU,OAAO,KAAK;AAChC,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,mBAAmB,MAAM;AAChC,MAAI,UAAU,aAAa,OACzB;AAEF,SAAO,IAAI,aAAa,OAAO;AACjC;AAEA,SAAS,IAAI,QAAQ;AACnB,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,QAAI,MAAM,OAAO,CAAC;AAClB,QAAI,OAAO,QAAQ,UAAU;AAC3B,UAAI,MAAM,QAAQ,GAAG,GAAG;AAEtB,iBAAS,OAAO,WAAW,WAAW,CAAC,MAAM,IAAI;AACjD,iBAAS,IAAI,GAAG,OAAO,IAAI,QAAQ,IAAI,MAAM,KAAK;AAChD,cAAI,OAAO,IAAI,CAAC;AAChB,cAAI,OAAO,SAAS,UAAU;AAC5B,kBAAM,mBAAmB,MAAM;AAAA,UACjC,WAAW,OAAO,OAAO,CAAC,MAAM,aAAa;AAC3C,mBAAO,KAAK,IAAI;AAAA,UAClB,OAAO;AACL,mBAAO,CAAC,KAAK;AAAA,UACf;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,mBAAmB,MAAM;AAAA,MACjC;AAAA,IACF,WAAW,OAAO,WAAW,UAAU;AACrC,gBAAU;AAAA,IACZ,OAAO;AACL,aAAO,CAAC,KAAK;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,MAAM,eAAe,KAAK,MAAM,KAAK;AACzC,IAAI,UAAU,MAAM;AACpB,IAAI,SAAS,KAAK;AAElB,SAAS,qBAAqB,MAAM,MAAM;AACxC,SAAO;AAAA,IACL,aAAa,KAAK,QAAQ,SAAS,EAAE,IAAI;AAAA,IACzC;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAM,cAAN,MAAkB;AAAA,EAChB,cAAc;AACZ,SAAK,UAAU,QAAQ,QAAQ;AAAA,EACjC;AAAA,EAEA,IAAI,gBAAgB;AAClB,SAAK,UAAU,KAAK,QAEjB,MAAM,MAAM;AAAA,IAAE,CAAC,EACf,KAAK,MAAM,eAAe,CAAC;AAC9B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AACF;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAGA,UAAQ,OAAO,OAAO;AAAA,IACpB,KAAK;AAEH,aAAO,MAAM,SAAS;AAAA,IACxB,KAAK;AAEH,aAAO,MAAM,SAAS;AAAA,IACxB;AAEE,aAAO,KAAK,UAAU,KAAK;AAAA,EAC/B;AACF;AAGA,SAAS,oBAAoB,QAAQ,WAAW;AAE9C,SAAO,UAAU,MAAM,IAAI,UAAU,SAAS,IAAI;AACpD;AAEA,SAAe,WAAW,UAAU,UAAU,QAAQ,WAAW,WAAW8B,eAAc;AAAA;AACxF,UAAM,gBAAgB,oBAAoB,QAAQ,SAAS;AAE3D,QAAI;AACJ,QAAI,CAAC,WAAW;AAEd,oBAAc,SAAS,eAAe,SAAS,gBAAgB,CAAC;AAChE,UAAI,YAAY,aAAa,GAAG;AAC9B,eAAO,YAAY,aAAa;AAAA,MAClC;AAAA,IACF;AAEA,UAAM,iBAAiB,SAAS,KAAK,EAAE,KAAK,SAAgB,MAAM;AAAA;AAChE,cAAM,YAAY,KAAK,UAAU,cAChC,YAAY,SAAS,UAAU,aAAa;AAI7C,iBAAS,aAAa,KAAK;AACzB,cAAI,QAAQ,IAAI,SAAS,CAAC;AAC1B,cAAI,eAAe;AACnB,cAAI,aAAa,QAAQ,GAAG,MAAM,IAAI;AACpC,2BAAe,WAAW,MAAM;AAAA,UAClC;AACA,gBAAM,SAAS,IAAI,MAAM,YAAY,IAAI,IAAI,MAAM,YAAY,KAAK,CAAC;AAErE,cAAI,OAAO,SAAS,GAAG;AACrB;AAAA,UACF;AACA,iBAAO,SAAS,IAAI;AACpB,iBAAO;AAAA,QACT;AACA,cAAM,OAAO,UAAU,YAAYA,eAAc,YAAY;AAC7D,cAAM,MAAM,MAAM,SAAS,0BAA0B,SAAS;AAC9D,cAAM,KAAK,IAAI;AACf,WAAG,kBAAkB;AACrB,cAAM,OAAO;AAAA,UACX,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,SAAS,SAAS;AAAA,UAClB;AAAA,UACA;AAAA,QACF;AAEA,YAAI;AACJ,YAAI;AACF,uBAAa,MAAM,KAAK,GAAG,IAAI,gBAAgB;AAAA,QACjD,SAAS,KAAK;AAEZ,cAAI,IAAI,WAAW,KAAK;AACtB,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,aAAK,MAAM,aAAa,WAAW,MAAM;AACzC,YAAI,aAAa;AACf,eAAK,GAAG,KAAK,aAAa,WAAY;AACpC,mBAAO,YAAY,aAAa;AAAA,UAClC,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AAAA,KAAC;AAED,QAAI,aAAa;AACf,kBAAY,aAAa,IAAI;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA;AAEA,IAAM,mBAAmB,CAAC;AAC1B,IAAM,gBAAgB,IAAI,YAAY;AACtC,IAAM,uBAAuB;AAE7B,SAAS,cAAc,MAAM;AAG3B,SAAO,KAAK,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG;AACjE;AAEA,SAAS,SAASH,UAAS;AAGzB,SAAOA,SAAQ,WAAW,KAAK,MAAM,KAAKA,SAAQ,CAAC,EAAE,GAAG;AAC1D;AAEA,SAAS,UAAU,IAAI,GAAG,MAAM;AAC9B,MAAI;AACF,OAAG,KAAK,SAAS,CAAC;AAAA,EACpB,SAAS,KAAK;AACZ;AAAA,MAAe;AAAA,MACb;AAAA,IAG+C;AACjD,mBAAe,SAAS,GAAG,IAAI;AAAA,EACjC;AACF;AA4BA,SAAS,wBAAwBG,eAAcC,SAAQC,UAASC,gBAAe;AAE7E,WAAS,OAAO,IAAI,KAAK,KAAK;AAG5B,QAAI;AACF,UAAI,GAAG;AAAA,IACT,SAAS,GAAG;AACV,gBAAU,IAAI,GAAG,EAAC,KAAK,IAAG,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,WAAS,UAAU,IAAI,KAAKpC,OAAM,QAAQ,UAAU;AAKlD,QAAI;AACF,aAAO,EAAC,QAAS,IAAIA,OAAM,QAAQ,QAAQ,EAAC;AAAA,IAC9C,SAAS,GAAG;AACV,gBAAU,IAAI,GAAG,EAAC,KAAK,MAAAA,OAAM,QAAQ,SAAQ,CAAC;AAC9C,aAAO,EAAC,OAAO,EAAC;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,mBAAmB,GAAG,GAAG;AAChC,UAAM,aAAa,QAAQ,EAAE,KAAK,EAAE,GAAG;AACvC,WAAO,eAAe,IAAI,aAAa,QAAQ,EAAE,OAAO,EAAE,KAAK;AAAA,EACjE;AAEA,WAAS,aAAa,SAAS,OAAO,MAAM;AAC1C,WAAO,QAAQ;AACf,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAAA,IACzC,WAAW,OAAO,GAAG;AACnB,aAAO,QAAQ,MAAM,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAEA,WAAS,WAAW,KAAK;AACvB,UAAM,MAAM,IAAI;AAGhB,UAAM,QAAS,OAAO,OAAO,QAAQ,YAAY,IAAI,OAAQ,IAAI;AACjE,WAAO;AAAA,EACT;AAEA,WAASqC,+BAA8B,KAAK;AAC1C,eAAW,OAAO,IAAI,MAAM;AAC1B,YAAM,OAAO,IAAI,OAAO,IAAI,IAAI;AAChC,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,iBAAW,YAAY,OAAO,KAAK,IAAI,GAAG;AACxC,cAAM,MAAM,KAAK,QAAQ;AACzB,aAAK,QAAQ,EAAE,OAAO,aAAa,IAAI,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AAEA,WAAS,uBAAuB,MAAM;AACpC,WAAO,SAAU,KAAK;AACpB,UAAI,KAAK,gBAAgB,KAAK,eAAe,KAAK,QAAQ;AACxD,QAAAA,+BAA8B,GAAG;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,aAAa,WAAW,MAAM,QAAQ,QAAQ;AAErD,QAAI,MAAM,KAAK,SAAS;AACxB,QAAI,OAAO,QAAQ,aAAa;AAC9B,UAAI,QAAQ;AACV,cAAM,mBAAmB,KAAK,UAAU,GAAG,CAAC;AAAA,MAC9C;AACA,aAAO,KAAK,YAAY,MAAM,GAAG;AAAA,IACnC;AAAA,EACF;AAEA,WAAS,cAAc,kBAAkB;AACvC,QAAI,OAAO,qBAAqB,aAAa;AAC3C,YAAM,WAAW,OAAO,gBAAgB;AAExC,UAAI,CAAC,MAAM,QAAQ,KAAK,aAAa,SAAS,kBAAkB,EAAE,GAAG;AACnE,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,WAAS,cAAc,MAAM;AAC3B,SAAK,cAAc,cAAc,KAAK,WAAW;AACjD,SAAK,QAAQ,cAAc,KAAK,KAAK;AACrC,SAAK,OAAO,cAAc,KAAK,IAAI;AACnC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,QAAQ;AACV,UAAI,OAAO,WAAW,UAAU;AAC9B,eAAQ,IAAI,gBAAgB,+BAA+B,MAAM,GAAG;AAAA,MACtE;AACA,UAAI,SAAS,GAAG;AACd,eAAO,IAAI,gBAAgB,wCAAwC,MAAM,GAAG;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AAEA,WAAS,qBAAqB,SAAS,KAAK;AAC1C,UAAM,eAAe,QAAQ,aAAa,WAAW;AACrD,UAAM,aAAa,QAAQ,aAAa,aAAa;AAErD,QAAI,OAAO,QAAQ,YAAY,MAAM,eACnC,OAAO,QAAQ,UAAU,MAAM,eAC/B,QAAQ,QAAQ,YAAY,GAAG,QAAQ,UAAU,CAAC,IAAI,GAAG;AACzD,YAAM,IAAI,gBAAgB,iGACuC;AAAA,IACnE,WAAW,IAAI,UAAU,QAAQ,WAAW,OAAO;AACjD,UAAI,QAAQ,cAAc;AACxB,cAAM,IAAI,gBAAgB,2CAA2C;AAAA,MACvE,WAAW,QAAQ,QAAQ,QAAQ,KAAK,SAAS,KAC/C,CAAC,QAAQ,SAAS,CAAC,QAAQ,aAAa;AACxC,cAAM,IAAI,gBAAgB,2DACT;AAAA,MACnB;AAAA,IACF;AACA,eAAW,cAAc,CAAC,eAAe,SAAS,MAAM,GAAG;AACzD,YAAM,QAAQ,qBAAqB,QAAQ,UAAU,CAAC;AACtD,UAAI,OAAO;AACT,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,WAAe,UAAU,IAAI,KAAK,MAAM;AAAA;AAEtC,UAAI,SAAS,CAAC;AACd,UAAI;AACJ,UAAI,SAAS;AACb,UAAI;AAMJ,mBAAa,UAAU,MAAM,MAAM;AACnC,mBAAa,gBAAgB,MAAM,MAAM;AACzC,mBAAa,eAAe,MAAM,MAAM;AACxC,mBAAa,SAAS,MAAM,MAAM;AAClC,mBAAa,cAAc,MAAM,MAAM;AACvC,mBAAa,SAAS,MAAM,MAAM;AAClC,mBAAa,eAAe,MAAM,MAAM;AACxC,mBAAa,QAAQ,MAAM,MAAM;AACjC,mBAAa,SAAS,MAAM,MAAM;AAClC,mBAAa,aAAa,MAAM,MAAM;AACtC,mBAAa,YAAY,MAAM,QAAQ,IAAI;AAC3C,mBAAa,aAAa,MAAM,QAAQ,IAAI;AAC5C,mBAAa,UAAU,MAAM,QAAQ,IAAI;AACzC,mBAAa,WAAW,MAAM,QAAQ,IAAI;AAC1C,mBAAa,iBAAiB,MAAM,MAAM;AAC1C,mBAAa,OAAO,MAAM,QAAQ,IAAI;AACtC,mBAAa,cAAc,MAAM,MAAM;AAGvC,eAAS,OAAO,KAAK,GAAG;AACxB,eAAS,WAAW,KAAK,KAAK,MAAM;AAIpC,UAAI,OAAO,KAAK,SAAS,aAAa;AACpC,cAAM,iBAAiB;AAIvB,cAAM,eAAe,QAAQ,mBAAmB,KAAK,UAAU,KAAK,IAAI,CAAC,CAAC;AAC1E,YAAI,aAAa,SAAS,OAAO,SAAS,KAAK,gBAAgB;AAG7D,qBAAW,OAAO,CAAC,MAAM,MAAM,MAAM,OAAO;AAAA,QAC9C,OAAO;AACL,mBAAS;AACT,cAAI,OAAO,QAAQ,UAAU;AAC3B,mBAAO,EAAC,MAAM,KAAK,KAAI;AAAA,UACzB,OAAO;AACL,gBAAI,OAAO,KAAK;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAGA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,QAAQ,cAAc,GAAG;AAE/B,cAAMC,YAAW,MAAM,GAAG,MAAM,aAAa,MAAM,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI,QAAQ;AAAA,UACrF,SAAS,IAAI,EAAE,EAAC,gBAAgB,mBAAkB,CAAC;AAAA,UACnD;AAAA,UACA,MAAM,KAAK,UAAU,IAAI;AAAA,QAC3B,CAAC;AACD,aAAKA,UAAS;AAEd,cAAMC,UAAS,MAAMD,UAAS,KAAK;AAEnC,YAAI,CAAC,IAAI;AACP,UAAAC,QAAO,SAASD,UAAS;AACzB,gBAAM,0BAA0BC,OAAM;AAAA,QACxC;AAGA,mBAAW,OAAOA,QAAO,MAAM;AAE7B,cAAI,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI,MAAM,UAAU,wBAAwB;AAC9E,kBAAM,IAAI,MAAM,IAAI,MAAM;AAAA,UAC5B;AAAA,QACF;AAEA,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,kBAAQA,OAAM;AAAA,QAChB,CAAC,EAAE,KAAK,uBAAuB,IAAI,CAAC;AAAA,MACtC;AAGA,aAAO,QAAQ,CAAC;AAChB,iBAAW,OAAO,OAAO,KAAK,GAAG,GAAG;AAClC,YAAI,MAAM,QAAQ,IAAI,GAAG,CAAC,GAAG;AAC3B,eAAK,GAAG,IAAI,IAAI,GAAG;AAAA,QACrB,OAAO;AACL,eAAK,GAAG,IAAI,IAAI,GAAG,EAAE,SAAS;AAAA,QAChC;AAAA,MACF;AAEA,YAAM,WAAW,MAAM,GAAG,MAAM,eAAe,QAAQ;AAAA,QACrD,SAAS,IAAI,EAAE,EAAC,gBAAgB,mBAAkB,CAAC;AAAA,QACnD,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B,CAAC;AAED,WAAK,SAAS;AAEd,YAAM,SAAS,MAAM,SAAS,KAAK;AACnC,UAAI,CAAC,IAAI;AACP,eAAO,SAAS,SAAS;AACzB,cAAM,0BAA0B,MAAM;AAAA,MACxC;AAEA,aAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,gBAAQ,MAAM;AAAA,MAChB,CAAC,EAAE,KAAK,uBAAuB,IAAI,CAAC;AAAA,IACtC;AAAA;AAKA,WAAS,YAAY,IAAI,KAAK,MAAM;AAClC,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,SAAG,OAAO,KAAK,MAAM,SAAU,KAAK,KAAK;AACvC,YAAI,KAAK;AACP,iBAAO,OAAO,GAAG;AAAA,QACnB;AACA,gBAAQ,GAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAKA,WAAS,kBAAkB,IAAI;AAC7B,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,SAAG,aAAa,SAAU,KAAK,KAAK;AAClC,YAAI,KAAK;AACP,iBAAO,OAAO,GAAG;AAAA,QACnB;AACA,gBAAQ,GAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,WAAS,WAAW,OAAO;AACzB,WAAO,SAAU,QAAQ;AAEvB,UAAI,OAAO,WAAW,KAAK;AACzB,eAAO;AAAA,MACT,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAKA,WAAe,iBAAiB,OAAO,MAAM,yBAAyB;AAAA;AACpE,YAAM,YAAY,gBAAgB;AAClC,YAAM,iBAAiB,EAAC,KAAK,WAAW,MAAM,CAAC,EAAC;AAChD,YAAM,UAAU,wBAAwB,IAAI,KAAK;AACjD,YAAM,2BAA2B,QAAQ,CAAC;AAC1C,YAAMT,WAAU,QAAQ,CAAC;AAEzB,eAAS,aAAa;AACpB,YAAI,SAASA,QAAO,GAAG;AAGrB,iBAAO,QAAQ,QAAQ,cAAc;AAAA,QACvC;AACA,eAAO,KAAK,GAAG,IAAI,SAAS,EAAE,MAAM,WAAW,cAAc,CAAC;AAAA,MAChE;AAEA,eAAS,gBAAgBU,UAAS;AAChC,YAAI,CAACA,SAAQ,KAAK,QAAQ;AAExB,iBAAO,QAAQ,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC;AAAA,QACnC;AACA,eAAO,KAAK,GAAG,QAAQ;AAAA,UACrB,MAAMA,SAAQ;AAAA,UACd,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAEA,eAAS,oBAAoBA,UAAS,WAAW;AAC/C,cAAM,SAAS,CAAC;AAChB,cAAM,UAAU,oBAAI,IAAI;AAExB,mBAAW,OAAO,UAAU,MAAM;AAChC,gBAAM,MAAM,IAAI;AAChB,cAAI,CAAC,KAAK;AACR;AAAA,UACF;AACA,iBAAO,KAAK,GAAG;AACf,kBAAQ,IAAI,IAAI,GAAG;AACnB,cAAI,WAAW,CAAC,yBAAyB,IAAI,IAAI,GAAG;AACpD,cAAI,CAAC,IAAI,UAAU;AACjB,kBAAM,WAAW,yBAAyB,IAAI,IAAI,GAAG;AACrD,gBAAI,WAAW,UAAU;AACvB,kBAAI,QAAQ,SAAS;AAAA,YACvB;AAAA,UACF;AAAA,QACF;AACA,cAAM,UAAU,eAAe,wBAAwB;AACvD,mBAAW,OAAO,SAAS;AACzB,cAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AAErB,kBAAM,QAAQ;AAAA,cACZ,KAAK;AAAA,YACP;AACA,kBAAM,WAAW,yBAAyB,IAAI,GAAG;AACjD,gBAAI,WAAW,UAAU;AACvB,oBAAM,QAAQ,SAAS;AAAA,YACzB;AACA,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AACA,QAAAA,SAAQ,OAAO,KAAK,QAAQ,OAAOA,SAAQ,IAAI,CAAC;AAChD,eAAO,KAAKA,QAAO;AAEnB,eAAO;AAAA,MACT;AAEA,YAAM,UAAU,MAAM,WAAW;AACjC,YAAM,eAAe,MAAM,gBAAgB,OAAO;AAClD,aAAO,oBAAoB,SAAS,YAAY;AAAA,IAClD;AAAA;AAEA,WAAS,eAAe,MAAM;AAG5B,WAAO,KAAK,SAAS,IAAI,eAAe,EAAE,KAAK,SAAU,KAAK;AAC5D,YAAM,WAAW,IAAI;AACrB,aAAO,KAAK,GAAG,IAAI,iBAAiB,EAAE,KAAK,SAAUC,MAAK;AACxD,eAAOA,KAAI;AAAA,MACb,CAAC,EACA,MAAM,WAAW,MAAS,CAAC,EAC3B,KAAK,SAAU,QAAQ;AACtB,eAAO,KAAK,GAAG,IAAI;AAAA,UACjB,KAAK;AAAA,UACL,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,UAAI,IAAI,WAAW,KAAK;AACtB,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAIA,WAAS,cAAc,MAAM,yBAAyB,KAAK;AACzD,QAAI,WAAW;AACf,WAAO,KAAK,GAAG,IAAI,QAAQ,EACxB,MAAM,WAAW,EAAC,KAAK,UAAU,KAAK,EAAC,CAAC,CAAC,EACzC,KAAK,SAAU,YAAY;AAC1B,UAAI,SAAS,eAAe,uBAAuB;AACnD,aAAO,QAAQ,IAAI,OAAO,IAAI,SAAU,OAAO;AAC7C,eAAO,iBAAiB,OAAO,MAAM,uBAAuB;AAAA,MAC9D,CAAC,CAAC,EAAE,KAAK,SAAU,qBAAqB;AACtC,YAAI,gBAAgB,oBAAoB,KAAK;AAC7C,mBAAW,MAAM;AACjB,sBAAc,KAAK,UAAU;AAE7B,eAAO,KAAK,GAAG,SAAS,EAAC,MAAO,cAAa,CAAC;AAAA,MAChD,CAAC,EAGE,KAAK,MAAM,eAAe,IAAI,CAAC;AAAA,IACpC,CAAC;AAAA,EACL;AAEA,WAAS,SAAS,MAAM;AACtB,UAAM,WAAW,OAAO,SAAS,WAAW,OAAO,KAAK;AACxD,QAAItC,SAAQ,iBAAiB,QAAQ;AACrC,QAAI,CAACA,QAAO;AACV,MAAAA,SAAQ,iBAAiB,QAAQ,IAAI,IAAI,YAAY;AAAA,IACvD;AACA,WAAOA;AAAA,EACT;AAEA,WAAe,WAAW,MAAM,MAAM;AAAA;AACpC,aAAO,cAAc,SAAS,IAAI,GAAG,WAAY;AAC/C,eAAO,kBAAkB,MAAM,IAAI;AAAA,MACrC,CAAC,EAAE;AAAA,IACL;AAAA;AAEA,WAAe,kBAAkB,MAAM,MAAM;AAAA;AAE3C,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,eAAS,KAAK,KAAK,OAAO;AACxB,cAAM,SAAS,EAAC,IAAI,IAAI,KAAK,KAAK,aAAa,GAAG,EAAC;AAGnD,YAAI,OAAO,UAAU,eAAe,UAAU,MAAM;AAClD,iBAAO,QAAQ,aAAa,KAAK;AAAA,QACnC;AACA,mBAAW,KAAK,MAAM;AAAA,MACxB;AAEA,YAAM,SAAS+B,QAAO,KAAK,QAAQ,IAAI;AAEvC,UAAI,aAAa,KAAK,OAAO;AAE7B,eAAS,aAAa;AACpB,eAAO,KAAK,SAAS,KAAK,EAAE,KAAK,SAAU,MAAM;AAC/C,mBAAS,KAAK,SAAS,YAAY,IAAI;AAAA,YACrC,MAAM;AAAA,YACN,aAAa,KAAK,aAAa;AAAA,UACjC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,eAASQ,eAAc,yBAAyB,KAAK;AACnD,eAAO,WAAY;AACjB,iBAAO,cAAc,MAAM,yBAAyB,GAAG;AAAA,QACzD;AAAA,MACF;AAEA,UAAI,eAAe;AACnB,YAAM,WAAW;AAAA,QACf,MAAM,KAAK;AAAA,QACX;AAAA,MACF;AACA,WAAK,SAAS,KAAK,YAAY,QAAQ;AAEvC,YAAMvC,SAAQ,IAAI,YAAY;AAE9B,eAAe,mBAAmB;AAAA;AAChC,gBAAM,WAAW,MAAM,KAAK,SAAS,QAAQ;AAAA,YAC3C,aAAa;AAAA,YACb,WAAW;AAAA,YACX,cAAc;AAAA,YACd,OAAO;AAAA,YACP,OAAO;AAAA,YACP,OAAO,KAAK;AAAA,UACd,CAAC;AACD,gBAAM,SAAS,MAAM,gBAAgB;AACrC,iBAAO,aAAa,UAAU,MAAM;AAAA,QACtC;AAAA;AAEA,eAAS,kBAAkB;AACzB,eAAO,KAAK,GAAG,IAAI,iBAAiB,EAAE,KAAK,SAAU,KAAK;AACxD,iBAAO,IAAI;AAAA,QACb,CAAC,EACA,MAAM,WAAW,EAAE,CAAC,EACpB,KAAK,SAAU,UAAU;AACxB,iBAAO,KAAK,SAAS,IAAI,eAAe,EAAE,KAAK,SAAU,KAAK;AAC5D,kBAAM,eAAe,IAAI,OAAO,OAAO,SAAU,OAAO,OAAO;AAC7D,qBAAO,QAAQ;AAAA,YACjB,CAAC,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK;AAE7B,kBAAM,eAAe,aAAa,OAAO,SAAU,OAAO,OAAO;AAC/D,qBAAO,aAAa,QAAQ,KAAK,MAAM;AAAA,YACzC,CAAC;AAED,mBAAO,QAAQ,IAAI,aAAa,IAAI,SAAU,OAAO;AACnD,qBAAO,KAAK,SAAS,IAAI,KAAK,EAAE,KAAK,SAAUmB,MAAK;AAClD,uBAAO,EAAE,OAAO,KAAAA,KAAI;AAAA,cACtB,CAAC,EACA,MAAM,WAAW,EAAE,MAAM,CAAC,CAAC;AAAA,YAC9B,CAAC,CAAC;AAAA,UACJ,CAAC,EACA,MAAM,WAAW,CAAC,CAAC,CAAC;AAAA,QACvB,CAAC;AAAA,MACH;AAEA,eAAS,aAAa,UAAU,QAAQ;AACtC,cAAM,UAAU,SAAS;AACzB,YAAI,CAAC,QAAQ,UAAU,CAAC,OAAO,QAAQ;AACrC;AAAA,QACF;AAEA,mBAAW,SAAS,QAAQ;AAC1B,gBAAM,QAAQ,QAAQ,UAAU,SAAU,QAAQ;AAChD,mBAAO,OAAO,OAAO,MAAM;AAAA,UAC7B,CAAC;AACD,cAAI,QAAQ,GAAG;AAEb,kBAAM,QAAQ;AAAA,cACZ,KAAK,MAAM;AAAA,cACX,KAAK;AAAA,gBACH,KAAK,MAAM;AAAA,gBACX,UAAU;AAAA,cACZ;AAAA,cACA,SAAS,CAAC;AAAA,YACZ;AAEA,gBAAI,MAAM,KAAK;AAEb,oBAAM,MAAM,MAAM;AAClB,oBAAM,QAAQ,KAAK,EAAE,KAAK,MAAM,IAAI,KAAK,CAAC;AAAA,YAC5C;AAEA,oBAAQ,KAAK,KAAK;AAAA,UACpB;AAAA,QACF;AAEA,cAAM,0BAA0B,8BAA8B,OAAO;AAErE,QAAAnB,OAAM,IAAIuC,eAAc,yBAAyB,UAAU,CAAC;AAE5D,uBAAe,eAAe,QAAQ;AACtC,cAAMC,YAAW;AAAA,UACf,MAAM,KAAK;AAAA,UACX,UAAU,SAAS;AAAA,UACnB,eAAe,QAAQ;AAAA,UACvB;AAAA,QACF;AACA,aAAK,SAAS,KAAK,YAAYA,SAAQ;AACvC,aAAK,SAAS,YAAY,OAAO,QAAQ,EAAC,iBAAiB,aAAY,CAAC;AAExE,YAAI,QAAQ,SAAS,KAAK,oBAAoB;AAC5C;AAAA,QACF;AACA,eAAO,iBAAiB;AAAA,MAC1B;AAEA,eAAS,8BAA8B,SAAS;AAC9C,cAAM,0BAA0B,oBAAI,IAAI;AACxC,mBAAW,UAAU,SAAS;AAC5B,cAAI,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK;AAC7B,yBAAa,CAAC;AACd,kBAAM,OAAO;AAEb,gBAAI,CAAC,IAAI,UAAU;AACjB,qBAAO,KAAK,UAAU,QAAQ,GAAG;AAAA,YACnC;AACA,uBAAW,KAAK,kBAAkB;AAElC,kBAAM,2BAA2B,+BAA+B,UAAU;AAC1E,oCAAwB,IAAI,OAAO,IAAI,KAAK;AAAA,cAC1C;AAAA,cACA,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AACA,uBAAa,OAAO;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AAEA,eAAS,+BAA+BC,aAAY;AAClD,cAAM,2BAA2B,oBAAI,IAAI;AACzC,YAAI;AACJ,iBAAS,IAAI,GAAG,MAAMA,YAAW,QAAQ,IAAI,KAAK,KAAK;AACrD,gBAAM,kBAAkBA,YAAW,CAAC;AACpC,gBAAM,aAAa,CAAC,gBAAgB,KAAK,gBAAgB,EAAE;AAC3D,cAAI,IAAI,KAAK,QAAQ,gBAAgB,KAAK,OAAO,MAAM,GAAG;AACxD,uBAAW,KAAK,CAAC;AAAA,UACnB;AACA,mCAAyB,IAAI,kBAAkB,UAAU,GAAG,eAAe;AAC3E,oBAAU,gBAAgB;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAEA,UAAI;AACF,cAAM,WAAW;AACjB,cAAM,iBAAiB;AACvB,cAAMzC,OAAM,OAAO;AACnB,aAAK,MAAM;AACX,aAAK,SAAS,YAAY,OAAO,MAAM;AAAA,MACzC,SAAS,OAAO;AACd,aAAK,SAAS,YAAY,OAAO,QAAQ,KAAK;AAAA,MAChD;AAAA,IACF;AAAA;AAEA,WAAS,WAAW,MAAM,SAAS,SAAS;AAC1C,QAAI,QAAQ,gBAAgB,GAAG;AAC7B,aAAO,QAAQ;AAAA,IACjB;AAEA,UAAM,cAAc,QAAQ,SAAS,QAAQ;AAC7C,UAAM,YAAYgC,SAAQ,KAAK,SAAS;AACxC,UAAM,SAAS,CAAC;AAChB,UAAM,MAAM,MAAM,QAAQ,WAAW,IACjC,OAAO,oBACP,QAAQ;AAEZ,eAAW,UAAU,SAAS;AAC5B,YAAM,OAAO,OAAO,OAAO,SAAS,CAAC;AACrC,UAAI,WAAW,cAAc,OAAO,MAAM;AAG1C,UAAI,eAAe,MAAM,QAAQ,QAAQ,GAAG;AAC1C,mBAAW,SAAS,MAAM,GAAG,GAAG;AAAA,MAClC;AAEA,UAAI,QAAQ,QAAQ,KAAK,UAAU,QAAQ,MAAM,GAAG;AAClD,aAAK,KAAK,KAAK,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AACtC,aAAK,OAAO,KAAK,OAAO,KAAK;AAC7B;AAAA,MACF;AACA,aAAO,KAAK;AAAA,QACV,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AAAA,QAC9B,QAAQ,CAAC,OAAO,KAAK;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,cAAU,CAAC;AACX,eAAW,SAAS,QAAQ;AAC1B,YAAM,YAAY,UAAU,KAAK,UAAU,WAAW,MAAM,MAAM,MAAM,QAAQ,KAAK;AACrF,UAAI,UAAU,SAAS,UAAU,iBAAiB,cAAc;AAE9D,cAAM,UAAU;AAAA,MAClB;AACA,cAAQ,KAAK;AAAA;AAAA,QAEX,OAAO,UAAU,QAAQ,OAAO,UAAU;AAAA,QAC1C,KAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH;AAEA,WAAO,EAAE,MAAM,aAAa,SAAS,QAAQ,OAAO,QAAQ,IAAI,EAAE;AAAA,EACpE;AAEA,WAAS,UAAU,MAAM,MAAM;AAC7B,WAAO,cAAc,SAAS,IAAI,GAAG,WAAY;AAC/C,aAAO,iBAAiB,MAAM,IAAI;AAAA,IACpC,CAAC,EAAE;AAAA,EACL;AAEA,WAAe,iBAAiB,MAAM,MAAM;AAAA;AAC1C,UAAI;AACJ,YAAM,eAAe,KAAK,aAAa,KAAK,WAAW;AACvD,YAAM,OAAO,KAAK,QAAQ;AAC1B,UAAI,OAAO,KAAK,SAAS,eAAe,CAAC,KAAK,KAAK,QAAQ;AAEzD,aAAK,QAAQ;AACb,eAAO,KAAK;AAAA,MACd;AAEA,eAAe,cAAc,UAAU;AAAA;AACrC,mBAAS,eAAe;AACxB,gBAAM,MAAM,MAAM,KAAK,GAAG,QAAQ,QAAQ;AAC1C,sBAAY,IAAI;AAEhB,iBAAO,IAAI,KAAK,IAAI,SAAU,QAAQ;AAKpC,gBAAI,WAAW,OAAO,OAAO,OAAO,OAAO,IAAI,UAAU,YACvD,OAAO,IAAI,UAAU,MAAM;AAC3B,oBAAMnC,QAAO,OAAO,KAAK,OAAO,IAAI,KAAK,EAAE,KAAK;AAGhD,oBAAM,eAAe,CAAC,MAAM,OAAO,OAAO;AAC1C,kBAAI,EAAEA,QAAO,gBAAgBA,QAAO,eAAe;AACjD,uBAAO,OAAO,IAAI;AAAA,cACpB;AAAA,YACF;AAEA,kBAAM,oBAAoB,qBAAqB,OAAO,IAAI,GAAG;AAC7D,mBAAO;AAAA,cACL,KAAK,kBAAkB,CAAC;AAAA,cACxB,IAAI,kBAAkB,CAAC;AAAA,cACvB,OAAQ,WAAW,OAAO,MAAM,OAAO,IAAI,QAAQ;AAAA,YACrD;AAAA,UACF,CAAC;AAAA,QACH;AAAA;AAEA,eAAe,kBAAkB,MAAM;AAAA;AACrC,cAAI;AACJ,cAAI,cAAc;AAChB,2BAAe,WAAW,MAAM,MAAM,IAAI;AAAA,UAC5C,WAAW,OAAO,KAAK,SAAS,aAAa;AAC3C,2BAAe;AAAA,cACb,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR;AAAA,YACF;AAAA,UACF,OAAO;AAEL,2BAAe;AAAA,cACb,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,MAAM,aAAa,MAAK,KAAK,OAAM,KAAK,IAAI;AAAA,YAC9C;AAAA,UACF;AAEA,cAAI,KAAK,YAAY;AACnB,yBAAa,aAAa,KAAK;AAAA,UACjC;AACA,cAAI,KAAK,cAAc;AACrB,kBAAM,SAAS,KAAK,KAAK,IAAI,UAAU,CAAC;AAExC,kBAAM,aAAa,MAAM,KAAK,SAAS,QAAQ;AAAA,cAC7C,MAAM;AAAA,cACN,cAAc;AAAA,cACd,WAAW,KAAK;AAAA,cAChB,aAAa,KAAK;AAAA,cAClB,QAAQ,KAAK;AAAA,YACf,CAAC;AACD,kBAAM,eAAe,oBAAI,IAAI;AAC7B,uBAAW,OAAO,WAAW,MAAM;AACjC,2BAAa,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA,YAClC;AACA,uBAAW,OAAO,MAAM;AACtB,oBAAM,QAAQ,WAAW,GAAG;AAC5B,oBAAM,MAAM,aAAa,IAAI,KAAK;AAClC,kBAAI,KAAK;AACP,oBAAI,MAAM;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA;AAEA,UAAI,OAAO,KAAK,SAAS,aAAa;AACpC,cAAMA,QAAO,KAAK;AAClB,cAAM,gBAAgBA,MAAK,IAAI,SAAU,KAAK;AAC5C,gBAAM,WAAW;AAAA,YACf,UAAW,kBAAkB,CAAC,GAAG,CAAC;AAAA,YAClC,QAAW,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AAAA,UACxC;AAEA,cAAI,KAAK,YAAY;AACnB,qBAAS,aAAa;AAAA,UACxB;AACA,iBAAO,cAAc,QAAQ;AAAA,QAC/B,CAAC;AACD,cAAM,SAAS,MAAM,QAAQ,IAAI,aAAa;AAC9C,cAAM,kBAAkB,OAAO,KAAK;AACpC,eAAO,kBAAkB,eAAe;AAAA,MAC1C,OAAO;AACL,cAAM,WAAW;AAAA,UACf,YAAa,KAAK;AAAA,QACpB;AAEA,YAAI,KAAK,YAAY;AACnB,mBAAS,aAAa;AAAA,QACxB;AACA,YAAI;AACJ,YAAI;AACJ,YAAI,eAAe,MAAM;AACvB,qBAAW,KAAK;AAAA,QAClB;AACA,YAAI,cAAc,MAAM;AACtB,qBAAW,KAAK;AAAA,QAClB;AACA,YAAI,aAAa,MAAM;AACrB,mBAAS,KAAK;AAAA,QAChB;AACA,YAAI,YAAY,MAAM;AACpB,mBAAS,KAAK;AAAA,QAChB;AACA,YAAI,OAAO,aAAa,aAAa;AACnC,mBAAS,WAAW,KAAK,aACvB,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,IAChC,kBAAkB,CAAC,QAAQ,CAAC;AAAA,QAChC;AACA,YAAI,OAAO,WAAW,aAAa;AACjC,cAAI,eAAe,KAAK,kBAAkB;AAC1C,cAAI,KAAK,YAAY;AACnB,2BAAe,CAAC;AAAA,UAClB;AAEA,mBAAS,SAAS;AAAA,YAChB,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM;AAAA,UAAC;AAAA,QAC1C;AACA,YAAI,OAAO,KAAK,QAAQ,aAAa;AACnC,gBAAM,WAAW,kBAAkB,CAAC,KAAK,GAAG,CAAC;AAC7C,gBAAM,SAAS,kBAAkB,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAC/C,cAAI,SAAS,YAAY;AACvB,qBAAS,SAAS;AAClB,qBAAS,WAAW;AAAA,UACtB,OAAO;AACL,qBAAS,WAAW;AACpB,qBAAS,SAAS;AAAA,UACpB;AAAA,QACF;AACA,YAAI,CAAC,cAAc;AACjB,cAAI,OAAO,KAAK,UAAU,UAAU;AAClC,qBAAS,QAAQ,KAAK;AAAA,UACxB;AACA,mBAAS,OAAO;AAAA,QAClB;AAEA,cAAM,SAAS,MAAM,cAAc,QAAQ;AAC3C,eAAO,kBAAkB,MAAM;AAAA,MACjC;AAAA,IACF;AAAA;AAEA,WAAe,gBAAgB,IAAI;AAAA;AACjC,YAAM,WAAW,MAAM,GAAG,MAAM,iBAAiB;AAAA,QAC/C,SAAS,IAAI,EAAE,EAAC,gBAAgB,mBAAkB,CAAC;AAAA,QACnD,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA;AAEA,WAAe,iBAAiB,IAAI;AAAA;AAClC,UAAI;AACF,cAAM,UAAU,MAAM,GAAG,IAAI,YAAYiC,aAAY;AACrD,cAAM,cAAc,oBAAI,IAAI;AAE5B,mBAAW,gBAAgB,OAAO,KAAK,QAAQ,KAAK,GAAG;AACrD,gBAAM,QAAQ,cAAc,YAAY;AACxC,gBAAM,gBAAgB,aAAa,MAAM,CAAC;AAC1C,gBAAM,WAAW,MAAM,CAAC;AACxB,cAAI,QAAQ,YAAY,IAAI,aAAa;AACzC,cAAI,CAAC,OAAO;AACV,oBAAQ,oBAAI,IAAI;AAChB,wBAAY,IAAI,eAAe,KAAK;AAAA,UACtC;AACA,gBAAM,IAAI,QAAQ;AAAA,QACpB;AACA,cAAM,OAAO;AAAA,UACX,MAAO,eAAe,WAAW;AAAA,UACjC,cAAe;AAAA,QACjB;AAEA,cAAM,MAAM,MAAM,GAAG,QAAQ,IAAI;AACjC,cAAM,gBAAgB,CAAC;AACvB,mBAAW,OAAO,IAAI,MAAM;AAC1B,gBAAM,WAAW,IAAI,IAAI,UAAU,CAAC;AACpC,qBAAW,YAAY,YAAY,IAAI,IAAI,GAAG,GAAG;AAC/C,gBAAI,eAAe,WAAW,MAAM;AAEpC,gBAAI,CAAC,QAAQ,MAAM,YAAY,GAAG;AAGhC,6BAAe;AAAA,YACjB;AACA,kBAAM,cAAc,OAAO,KAAK,QAAQ,MAAM,YAAY,CAAC;AAE3D,kBAAM,eAAe,IAAI,OAAO,IAAI,IAAI,SACtC,IAAI,IAAI,MAAM,QAAQ;AACxB,uBAAW,cAAc,aAAa;AACpC,4BAAc,UAAU,IAAI,cAAc,UAAU,KAAK;AAAA,YAC3D;AAAA,UACF;AAAA,QACF;AAEA,cAAM,cAAc,OAAO,KAAK,aAAa,EAC1C,OAAO,SAAU,YAAY;AAAE,iBAAO,CAAC,cAAc,UAAU;AAAA,QAAG,CAAC;AAEtE,cAAM,kBAAkB,YAAY,IAAI,SAAU,YAAY;AAC5D,iBAAO,cAAc,SAAS,UAAU,GAAG,WAAY;AACrD,mBAAO,IAAI,GAAG,YAAY,YAAY,GAAG,MAAM,EAAE,QAAQ;AAAA,UAC3D,CAAC,EAAE;AAAA,QACL,CAAC;AAED,eAAO,QAAQ,IAAI,eAAe,EAAE,KAAK,WAAY;AACnD,iBAAO,EAAC,IAAI,KAAI;AAAA,QAClB,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,YAAI,IAAI,WAAW,KAAK;AACtB,iBAAO,EAAC,IAAI,KAAI;AAAA,QAClB,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA;AAEA,WAAe,cAAc,IAAI,KAAK,MAAM;AAAA;AAE1C,UAAI,OAAO,GAAG,WAAW,YAAY;AACnC,eAAO,YAAY,IAAI,KAAK,IAAI;AAAA,MAClC;AACA,UAAI,SAAS,EAAE,GAAG;AAChB,eAAO,UAAU,IAAI,KAAK,IAAI;AAAA,MAChC;AAEA,YAAM,iBAAiB;AAAA,QACrB,oBAAoB,GAAG,OAAO,kCAAkC;AAAA,MAClE;AAEA,UAAI,OAAO,QAAQ,UAAU;AAE3B,6BAAqB,MAAM,GAAG;AAE9B,sBAAc,IAAI,WAAkB;AAAA;AAClC,kBAAM,OAAO,MAAM;AAAA;AAAA,cACF;AAAA;AAAA,cACA;AAAA;AAAA,cACF,IAAI;AAAA;AAAA,cACD,IAAI;AAAA;AAAA,cACJ;AAAA;AAAA,cACGA;AAAA,YAAY;AAEjC,mBAAO;AAAA,cAAI,WAAW,MAAM,cAAc,EAAE;AAAA,gBAC1C,WAAY;AAAE,yBAAO,UAAU,MAAM,IAAI;AAAA,gBAAG;AAAA,cAAC;AAAA,cAC7C,WAAY;AAAE,uBAAO,KAAK,GAAG,QAAQ;AAAA,cAAG;AAAA,YAC1C;AAAA,UACF;AAAA,SAAC;AACD,eAAO,cAAc,OAAO;AAAA,MAC9B,OAAO;AAEL,cAAM,eAAe;AACrB,cAAM,QAAQ,cAAc,YAAY;AACxC,cAAM,gBAAgB,MAAM,CAAC;AAC7B,cAAM,WAAW,MAAM,CAAC;AAExB,cAAM,MAAM,MAAM,GAAG,IAAI,aAAa,aAAa;AACnD,cAAM,IAAI,SAAS,IAAI,MAAM,QAAQ;AAErC,YAAI,CAAC,KAAK;AAER,gBAAM,IAAI,cAAc,QAAQ,IAAI,GAAG,sBAAsB,QAAQ,EAAE;AAAA,QACzE;AAEA,QAAAG,eAAc,KAAK,QAAQ;AAC3B,6BAAqB,MAAM,GAAG;AAE9B,cAAM,OAAO,MAAM;AAAA;AAAA,UACF;AAAA;AAAA,UACA;AAAA;AAAA,UACF,IAAI;AAAA;AAAA,UACD,IAAI;AAAA;AAAA,UACJ;AAAA;AAAA,UACGH;AAAA,QAAY;AAEjC,YAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,gBAAgB;AACxD,cAAI,KAAK,UAAU,gBAAgB;AACjC,qBAAS,WAAY;AACnB,yBAAW,MAAM,cAAc;AAAA,YACjC,CAAC;AAAA,UACH;AACA,iBAAO,UAAU,MAAM,IAAI;AAAA,QAC7B,OAAO;AACL,gBAAM,WAAW,MAAM,cAAc;AACrC,iBAAO,UAAU,MAAM,IAAI;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA;AAEA,WAAS,cAAc,KAAK,MAAM,UAAU;AAC1C,UAAM,KAAK;AACX,QAAI,OAAO,SAAS,YAAY;AAC9B,iBAAW;AACX,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,cAAc,IAAI,IAAI,CAAC;AAErC,QAAI,OAAO,QAAQ,YAAY;AAC7B,YAAM,EAAC,KAAM,IAAG;AAAA,IAClB;AAEA,UAAM,UAAU,QAAQ,QAAQ,EAAE,KAAK,WAAY;AACjD,aAAO,cAAc,IAAI,KAAK,IAAI;AAAA,IACpC,CAAC;AACD,qBAAiB,SAAS,QAAQ;AAClC,WAAO;AAAA,EACT;AAEA,QAAM,sBAAsB,YAAY,WAAY;AAClD,UAAM,KAAK;AAEX,QAAI,OAAO,GAAG,iBAAiB,YAAY;AACzC,aAAO,kBAAkB,EAAE;AAAA,IAC7B;AACA,QAAI,SAAS,EAAE,GAAG;AAChB,aAAO,gBAAgB,EAAE;AAAA,IAC3B;AACA,WAAO,iBAAiB,EAAE;AAAA,EAC5B,CAAC;AAED,SAAO;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AACF;AAEA,IAAI,gBAAgB;AAAA,EAClB,MAAM,SAAUjC,OAAM,QAAQ;AAC5B,WAAO,IAAI,MAAM;AAAA,EACnB;AAAA,EAEA,QAAQ,SAAUA,OAAM,QAAQ;AAC9B,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,QAAQ,SAAUA,OAAM,QAAQ;AAG9B,aAAS,OAAO6C,SAAQ;AACtB,UAAI,UAAU;AACd,eAAS,IAAI,GAAG,MAAMA,QAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAI,MAAMA,QAAO,CAAC;AAClB,mBAAY,MAAM;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,KAAU,IAAI,MAAM;AAAA,MACpB,KAAU,KAAK,IAAI,MAAM,MAAM,MAAM;AAAA,MACrC,KAAU,KAAK,IAAI,MAAM,MAAM,MAAM;AAAA,MACrC,OAAU,OAAO;AAAA,MACjB,QAAS,OAAO,MAAM;AAAA,IACxB;AAAA,EACF;AACF;AAEA,SAAS,WAAW,iBAAiB;AACnC,MAAI,QAAQ,KAAK,eAAe,GAAG;AACjC,WAAO,cAAc;AAAA,EACvB,WAAW,UAAU,KAAK,eAAe,GAAG;AAC1C,WAAO,cAAc;AAAA,EACvB,WAAW,UAAU,KAAK,eAAe,GAAG;AAC1C,WAAO,cAAc;AAAA,EACvB,WAAW,KAAK,KAAK,eAAe,GAAG;AACrC,UAAM,IAAI,MAAM,kBAAkB,sCAAsC;AAAA,EAC1E;AACF;AAEA,SAAS,OAAO,QAAQ,MAAM;AAE5B,MAAI,OAAO,WAAW,cAAc,OAAO,WAAW,GAAG;AACvD,QAAI,UAAU;AACd,WAAO,SAAU,KAAK;AACpB,aAAO,QAAQ,KAAK,IAAI;AAAA,IAC1B;AAAA,EACF,OAAO;AACL,WAAO,qBAAqB,OAAO,SAAS,GAAG,IAAI;AAAA,EACrD;AACF;AAEA,SAAS,QAAQ,WAAW;AAC1B,MAAI,kBAAkB,UAAU,SAAS;AACzC,MAAI,UAAU,WAAW,eAAe;AACxC,MAAI,SAAS;AACX,WAAO;AAAA,EACT,OAAO;AACL,WAAO,qBAAqB,eAAe;AAAA,EAC7C;AACF;AAEA,SAAS,cAAc,MAAM,UAAU;AACrC,MAAI,MAAM,KAAK,SAAS,KAAK,MAAM,QAAQ;AAC3C,MAAI,OAAO,IAAI,QAAQ,UAAU;AAC/B,UAAM,IAAI,cAAc,UAAU,KAAK,MAAM,+BAC3C,WAAW,qCAAqC,OAAO,IAAI,GAAG;AAAA,EAClE;AACF;AAEA,IAAI,eAAe;AACnB,IAAI,WAAW,wBAAwB,cAAc,QAAQ,SAAS,aAAa;AAEnF,SAAS,MAAM,KAAK,MAAM,UAAU;AAClC,SAAO,SAAS,MAAM,KAAK,MAAM,KAAK,MAAM,QAAQ;AACtD;AAEA,SAAS,YAAY,UAAU;AAC7B,SAAO,SAAS,YAAY,KAAK,MAAM,QAAQ;AACjD;AAEA,IAAI,YAAY;AAAA,EACd;AAAA,EACA;AACF;AAEA,SAAS,eAAe,UAAU,WAAW,UAAU;AACrD,SAAO,CAAC,SAAS,gBACV,CAAC,SAAS,aAAa,QAAQ,KAC/B,SAAS,aAAa,QAAQ,EAAE,WAAW,UAAU,aAAa,QAAQ,EAAE;AACrF;AAEA,SAAS,kBAAkB,IAAI,KAAK;AAClC,MAAI,YAAY,OAAO,KAAK,IAAI,YAAY;AAC5C,SAAO,QAAQ,IAAI,UAAU,IAAI,SAAU,UAAU;AACnD,WAAO,GAAG,cAAc,IAAI,KAAK,UAAU,EAAC,KAAK,IAAI,KAAI,CAAC;AAAA,EAC5D,CAAC,CAAC;AACJ;AAEA,SAAS,oCAAoC,QAAQ,KAAK,KAAK;AAC7D,MAAI,6BAA6B,SAAS,GAAG,KAAK,CAAC,SAAS,MAAM;AAClE,MAAI,YAAY,OAAO,KAAK,IAAI,YAAY;AAE5C,MAAI,CAAC,4BAA4B;AAC/B,WAAO,kBAAkB,KAAK,GAAG;AAAA,EACnC;AAEA,SAAO,OAAO,IAAI,IAAI,GAAG,EAAE,KAAK,SAAU,UAAU;AAClD,WAAO,QAAQ,IAAI,UAAU,IAAI,SAAU,UAAU;AACnD,UAAI,eAAe,UAAU,KAAK,QAAQ,GAAG;AAC3C,eAAO,IAAI,cAAc,IAAI,KAAK,QAAQ;AAAA,MAC5C;AAEA,aAAO,OAAO,cAAc,SAAS,KAAK,QAAQ;AAAA,IACpD,CAAC,CAAC;AAAA,EACJ,CAAC,EAAE,MAAM,SAAU,OAAO;AAExB,QAAI,MAAM,WAAW,KAAK;AACxB,YAAM;AAAA,IACR;AAEA,WAAO,kBAAkB,KAAK,GAAG;AAAA,EACnC,CAAC;AACH;AAEA,SAAS,kBAAkB,OAAO;AAChC,MAAI,WAAW,CAAC;AAChB,SAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,IAAI;AACvC,QAAI,cAAc,MAAM,EAAE,EAAE;AAC5B,gBAAY,QAAQ,SAAU,YAAY;AACxC,eAAS,KAAK;AAAA,QACZ;AAAA,QACA,KAAK;AAAA,MACP,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,EACV;AACF;AAQA,SAAS,QAAQ,KAAK,QAAQ,OAAO,OAAO;AAC1C,UAAQ,MAAM,KAAK;AAEnB,MAAI,aAAa,CAAC,GACd,KAAK;AAET,WAAS,aAAa;AAEpB,QAAI,cAAc,kBAAkB,KAAK;AAEzC,QAAI,CAAC,YAAY,KAAK,QAAQ;AAC5B;AAAA,IACF;AAEA,WAAO,IAAI,QAAQ,WAAW,EAAE,KAAK,SAAU,iBAAiB;AAE9D,UAAI,MAAM,WAAW;AACnB,cAAM,IAAI,MAAM,WAAW;AAAA,MAC7B;AACA,aAAO,QAAQ,IAAI,gBAAgB,QAAQ,IAAI,SAAU,aAAa;AACpE,eAAO,QAAQ,IAAI,YAAY,KAAK,IAAI,SAAU,KAAK;AACrD,cAAI,YAAY,IAAI;AAEpB,cAAI,IAAI,OAAO;AAGb,iBAAK;AAAA,UACP;AAEA,cAAI,CAAC,aAAa,CAAC,UAAU,cAAc;AACzC,mBAAO;AAAA,UACT;AAEA,iBAAO,oCAAoC,QAAQ,KAAK,SAAS,EAAE,KAAK,CAAC,gBAAgB;AACvF,gBAAI,YAAY,OAAO,KAAK,UAAU,YAAY;AAClD,wBAAY,QAAQ,SAAU,YAAY,GAAG;AAC3C,kBAAI,MAAM,UAAU,aAAa,UAAU,CAAC,CAAC;AAC7C,qBAAO,IAAI;AACX,qBAAO,IAAI;AACX,kBAAI,OAAO;AAAA,YACb,CAAC;AAED,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,CAAC,CAAC;AAAA,MACJ,CAAC,CAAC,EAED,KAAK,SAAU,SAAS;AACvB,qBAAa,WAAW,OAAO,QAAQ,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,WAAS,eAAe;AACtB,WAAO,EAAE,IAAI,MAAK,WAAW;AAAA,EAC/B;AAEA,SAAO,QAAQ,QAAQ,EACpB,KAAK,UAAU,EACf,KAAK,YAAY;AACtB;AAEA,IAAI,qBAAqB;AACzB,IAAI,aAAa;AAQjB,IAAI,0BAA0B;AAC9B,IAAI,aAAa;AAEjB,SAAS,iBAAiB,IAAI,IAAI,YAAY,SAAS,aAAa;AAClE,SAAO,GAAG,IAAI,EAAE,EAAE,MAAM,SAAU,KAAK;AACrC,QAAI,IAAI,WAAW,KAAK;AACtB,UAAI,GAAG,YAAY,UAAU,GAAG,YAAY,SAAS;AACnD;AAAA,UACE;AAAA,UAAK;AAAA,QACP;AAAA,MACF;AACA,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,SAAS,CAAC;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,IACF;AACA,UAAM;AAAA,EACR,CAAC,EAAE,KAAK,SAAU,KAAK;AACrB,QAAI,YAAY,WAAW;AACzB;AAAA,IACF;AAGA,QAAI,IAAI,aAAa,YAAY;AAC/B;AAAA,IACF;AAGA,QAAI,WAAW,IAAI,WAAW,CAAC,GAAG,OAAO,SAAU,MAAM;AACvD,aAAO,KAAK,eAAe;AAAA,IAC7B,CAAC;AAGD,QAAI,QAAQ,QAAQ;AAAA,MAClB,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAKD,QAAI,UAAU,IAAI,QAAQ,MAAM,GAAG,uBAAuB;AAE1D,QAAI,UAAU;AACd,QAAI,aAAa;AAEjB,QAAI,aAAa;AACjB,QAAI,WAAW;AAEf,WAAO,GAAG,IAAI,GAAG,EAAE,MAAM,SAAU,KAAK;AACtC,UAAI,IAAI,WAAW,KAAK;AAEtB,eAAO,iBAAiB,IAAI,IAAI,YAAY,SAAS,WAAW;AAAA,MAClE;AACA,YAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AAEA,IAAM,uBAAN,MAA2B;AAAA,EACzB,YAAY,KAAK,QAAQ,IAAI,aAAa,OAAO;AAAA,IAC/C,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,EACzB,GAAG;AACD,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,KAAK;AACV,SAAK,cAAc;AACnB,SAAK,OAAO;AAEZ,QAAI,OAAO,KAAK,0BAA0B,aAAa;AACrD,WAAK,wBAAwB;AAAA,IAC/B;AAEA,QAAI,OAAO,KAAK,0BAA0B,aAAa;AACrD,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AAAA,EAEA,gBAAgB,YAAY,SAAS;AACnC,QAAIlD,QAAO;AACX,WAAO,KAAK,aAAa,YAAY,OAAO,EAAE,KAAK,WAAY;AAC7D,aAAOA,MAAK,aAAa,YAAY,OAAO;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EAEA,aAAa,YAAY,SAAS;AAChC,QAAI,KAAK,KAAK,uBAAuB;AACnC,aAAO;AAAA,QAAiB,KAAK;AAAA,QAAQ,KAAK;AAAA,QAAI;AAAA,QAC5C;AAAA,QAAS,KAAK;AAAA,MAAW;AAAA,IAC7B,OAAO;AACL,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,aAAa,YAAY,SAAS;AAChC,QAAI,KAAK,KAAK,uBAAuB;AACnC,UAAIA,QAAO;AACX,aAAO;AAAA,QAAiB,KAAK;AAAA,QAAK,KAAK;AAAA,QAAI;AAAA,QACzC;AAAA,QAAS,KAAK;AAAA,MAAW,EACxB,MAAM,SAAU,KAAK;AACpB,YAAI,iBAAiB,GAAG,GAAG;AACzB,UAAAA,MAAK,KAAK,wBAAwB;AAClC,iBAAO;AAAA,QACT;AACA,cAAM;AAAA,MACR,CAAC;AAAA,IACL,OAAO;AACL,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,QAAIA,QAAO;AAEX,QAAI,CAACA,MAAK,KAAK,yBAAyB,CAACA,MAAK,KAAK,uBAAuB;AACxE,aAAO,QAAQ,QAAQ,UAAU;AAAA,IACnC;AAEA,QAAIA,MAAK,QAAQA,MAAK,KAAK,yBAAyB,CAACA,MAAK,KAAK,uBAAuB;AACpF,aAAOA,MAAK,IAAI,IAAIA,MAAK,EAAE,EAAE,KAAK,SAAU,WAAW;AACrD,eAAO,UAAU,YAAY;AAAA,MAC/B,CAAC,EAAE,MAAM,SAAU,KAAK;AAEtB,YAAI,IAAI,WAAW,KAAK;AACtB,gBAAM;AAAA,QACR;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,WAAOA,MAAK,OAAO,IAAIA,MAAK,EAAE,EAAE,KAAK,SAAU,WAAW;AACxD,UAAIA,MAAK,QAAQA,MAAK,KAAK,yBAAyB,CAACA,MAAK,KAAK,uBAAuB;AACpF,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,aAAOA,MAAK,IAAI,IAAIA,MAAK,EAAE,EAAE,KAAK,SAAU,WAAW;AAIrD,YAAI,UAAU,YAAY,UAAU,SAAS;AAC3C,iBAAO;AAAA,QACT;AAEA,YAAImD;AACJ,YAAI,UAAU,SAAS;AACrB,UAAAA,WAAU,UAAU,QAAQ,SAAS;AAAA,QACvC,OAAO;AACL,UAAAA,WAAU;AAAA,QACZ;AAEA,YAAIA,YAAW,aAAa;AAC1B,iBAAO,YAAYA,QAAO,EAAE,WAAW,SAAS;AAAA,QAClD;AAEA,eAAO;AAAA,MACT,GAAG,SAAU,KAAK;AAChB,YAAI,IAAI,WAAW,OAAO,UAAU,UAAU;AAC5C,iBAAOnD,MAAK,IAAI,IAAI;AAAA,YAClB,KAAKA,MAAK;AAAA,YACV,UAAU;AAAA,UACZ,CAAC,EAAE,KAAK,WAAY;AAClB,mBAAO;AAAA,UACT,GAAG,SAAUa,MAAK;AAChB,gBAAI,iBAAiBA,IAAG,GAAG;AACzB,cAAAb,MAAK,KAAK,wBAAwB;AAClC,qBAAO,UAAU;AAAA,YACnB;AAEA,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,cAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,UAAI,IAAI,WAAW,KAAK;AACtB,cAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEA,IAAI,cAAc;AAAA,EAChB,aAAa,SAAU,WAAW,WAAW;AAE3C,QAAI,QAAQ,UAAU,UAAU,UAAU,QAAQ,MAAM,GAAG;AACzD,aAAO,UAAU;AAAA,IACnB;AAEA,WAAO;AAAA,EACT;AAAA,EACA,KAAK,SAAU,WAAW,WAAW;AAEnC,WAAO,uBAAuB,WAAW,SAAS,EAAE;AAAA,EACtD;AACF;AAMA,SAAS,uBAAuB,QAAQ,QAAQ;AAC9C,MAAI,OAAO,eAAe,OAAO,YAAY;AAC3C,WAAO;AAAA,MACL,UAAU,OAAO;AAAA,MACjB,SAAS,OAAO;AAAA,IAClB;AAAA,EACF;AAEA,SAAO,0BAA0B,OAAO,SAAS,OAAO,OAAO;AACjE;AAEA,SAAS,0BAA0B,eAAe,eAAe;AAG/D,MAAI,IAAI,cAAc,CAAC;AACvB,MAAI,aAAa,cAAc,MAAM,CAAC;AACtC,MAAI,IAAI,cAAc,CAAC;AACvB,MAAI,aAAa,cAAc,MAAM,CAAC;AAEtC,MAAI,CAAC,KAAK,cAAc,WAAW,GAAG;AACpC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS,CAAC;AAAA,IACZ;AAAA,EACF;AAEA,MAAI,WAAW,EAAE;AAEjB,MAAI,aAAa,UAAU,aAAa,GAAG;AACzC,WAAO;AAAA,MACL,UAAU,EAAE;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,EACF;AAEA,MAAI,WAAW,EAAE;AACjB,MAAI,aAAa,UAAU,UAAU,GAAG;AACtC,WAAO;AAAA,MACL,UAAU,EAAE;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,EACF;AAEA,SAAO,0BAA0B,YAAY,UAAU;AACzD;AAEA,SAAS,aAAa,WAAW,SAAS;AACxC,MAAI,QAAQ,QAAQ,CAAC;AACrB,MAAI,OAAO,QAAQ,MAAM,CAAC;AAE1B,MAAI,CAAC,aAAa,QAAQ,WAAW,GAAG;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,MAAM,YAAY;AAClC,WAAO;AAAA,EACT;AAEA,SAAO,aAAa,WAAW,IAAI;AACrC;AAEA,SAAS,iBAAiB,KAAK;AAC7B,SAAO,OAAO,IAAI,WAAW,YAAY,KAAK,MAAM,IAAI,SAAS,GAAG,MAAM;AAC5E;AAEA,SAAS,aAAa,KAAK,QAAQ,IAAI,aAAa,MAAM;AACxD,MAAI,EAAE,gBAAgB,uBAAuB;AAC3C,WAAO,IAAI,qBAAqB,KAAK,QAAQ,IAAI,aAAa,IAAI;AAAA,EACpE;AACA,SAAO;AACT;AAEA,IAAI,oBAAoB;AAExB,SAAS,QAAQ,MAAM,aAAa,OAAO,UAAU;AACnD,MAAI,KAAK,UAAU,OAAO;AACxB,gBAAY,KAAK,SAAS,KAAK;AAC/B,gBAAY,mBAAmB;AAC/B;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,sBAAsB,YAAY;AAChD,SAAK,oBAAoB;AAAA,EAC3B;AACA,cAAY,KAAK,gBAAgB,KAAK;AACtC,MAAI,YAAY,UAAU,YAAY,YAAY,UAAU,WAAW;AACrE,gBAAY,KAAK,UAAU,KAAK;AAChC,gBAAY,QAAQ;AACpB,QAAI,aAAa,SAAS,iBAAiB;AACzC,WAAK,mBAAmB;AAAA,IAC1B;AACA,QAAI,sBAAsB,SAAS,uBAAuB;AACxD,kBAAY,eAAe,UAAU,UAAU;AAAA,IACjD;AACA,gBAAY,KAAK,UAAU,mBAAmB;AAC9C,gBAAY,KAAK,UAAU,UAAU;AAAA,EACvC;AAEA,OAAK,mBAAmB,KAAK,oBAAoB;AACjD,OAAK,mBAAmB,KAAK,kBAAkB,KAAK,gBAAgB;AACpE,aAAW,UAAU,KAAK,gBAAgB;AAC5C;AAEA,SAAS,0BAA0B,aAAa;AAC9C,SAAO,OAAO,KAAK,WAAW,EAAE,KAAK,OAAO,EAAE,OAAO,SAAU,QAAQ,KAAK;AAC1E,WAAO,GAAG,IAAI,YAAY,GAAG;AAC7B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAIA,SAAS,sBAAsB,KAAK,QAAQ,MAAM;AAChD,MAAI,SAAS,KAAK,UAAU,KAAK,QAAQ,KAAK,OAAO,IAAI;AACzD,MAAI,YAAY,KAAK,SAAS,KAAK,OAAO,SAAS,IAAI;AACvD,MAAI,cAAc;AAClB,MAAI,iBAAkB;AACtB,MAAI,WAAW;AAKf,MAAI,KAAK,UAAU;AACjB,eAAW,KAAK,UAAU,KAAK,QAAQ;AAAA,EACzC;AAEA,MAAI,KAAK,UAAU,KAAK,cAAc;AACpC,kBAAc,KAAK,UAAU,0BAA0B,KAAK,YAAY,CAAC;AAAA,EAC3E;AAEA,MAAI,KAAK,UAAU,KAAK,WAAW,SAAS;AAC1C,qBAAiB,KAAK,KAAK,SAAS;AAAA,EACtC;AAEA,SAAO,QAAQ,IAAI,CAAC,IAAI,GAAG,GAAG,OAAO,GAAG,CAAC,CAAC,EAAE,KAAK,SAAU,KAAK;AAC9D,QAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,YAAY,iBAC5C,cAAc,SAAS;AACzB,WAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,gBAAU,WAAW,OAAO;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,EAAE,KAAK,SAAU,QAAQ;AAIxB,aAAS,OAAO,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACtD,WAAO,YAAY;AAAA,EACrB,CAAC;AACH;AAEA,SAAS,UAAU,KAAK,QAAQ,MAAM,aAAa,QAAQ;AACzD,MAAI,UAAU,CAAC;AACf,MAAI;AACJ,MAAI,eAAe;AAAA,IACjB,KAAK;AAAA,IACL,SAAS,CAAC;AAAA,IACV,MAAM,CAAC;AAAA,EACT;AACA,MAAI,oBAAoB;AACxB,MAAI,mBAAmB;AACvB,MAAI,uBAAuB;AAI3B,MAAI,mBAAmB;AACvB,MAAI,WAAW;AACf,MAAI,aAAa,KAAK,cAAc,KAAK,QAAQ;AACjD,MAAI,aAAa,KAAK,cAAc;AACpC,MAAI,gBAAgB,KAAK,iBAAiB;AAC1C,MAAI,QAAQ,KAAK,SAAS;AAC1B,MAAI,iBAAiB;AACrB,MAAI,UAAU,KAAK;AACnB,MAAI,WAAW,KAAK;AACpB,MAAI;AACJ,MAAI;AACJ,MAAI,cAAc,CAAC;AAEnB,MAAI,UAAU,KAAK;AACnB,MAAI;AAEJ,WAAS,UAAU;AAAA,IACjB,IAAI;AAAA,IACJ,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,IACnC,WAAW;AAAA,IACX,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,QAAQ,CAAC;AAAA,EACX;AAEA,MAAI,cAAc,CAAC;AACnB,cAAY,MAAM,KAAK,MAAM;AAE7B,WAAS,mBAAmB;AAC1B,QAAI,cAAc;AAChB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,sBAAsB,KAAK,QAAQ,IAAI,EAAE,KAAK,SAAU,KAAK;AAClE,cAAQ;AAER,UAAI,iBAAiB,CAAC;AACtB,UAAI,KAAK,eAAe,OAAO;AAC7B,yBAAiB,EAAE,uBAAuB,OAAO,uBAAuB,MAAM;AAAA,MAChF,WAAW,KAAK,eAAe,UAAU;AACvC,yBAAiB,EAAE,uBAAuB,MAAM,uBAAuB,MAAM;AAAA,MAC/E,WAAW,KAAK,eAAe,UAAU;AACvC,yBAAiB,EAAE,uBAAuB,OAAO,uBAAuB,KAAK;AAAA,MAC/E,OAAO;AACL,yBAAiB,EAAE,uBAAuB,MAAM,uBAAuB,KAAK;AAAA,MAC9E;AAEA,qBAAe,IAAI,aAAa,KAAK,QAAQ,OAAO,aAAa,cAAc;AAAA,IACjF,CAAC;AAAA,EACH;AAEA,WAAS,YAAY;AACnB,kBAAc,CAAC;AAEf,QAAI,aAAa,KAAK,WAAW,GAAG;AAClC;AAAA,IACF;AACA,QAAI,OAAO,aAAa;AACxB,QAAI,WAAW,EAAC,SAAS,KAAK,QAAO;AACrC,WAAO,OAAO,SAAS,EAAC,MAAM,WAAW,MAAK,GAAG,QAAQ,EAAE,KAAK,SAAU,KAAK;AAE7E,UAAI,YAAY,WAAW;AACzB,4BAAoB;AACpB,cAAM,IAAI,MAAM,WAAW;AAAA,MAC7B;AAIA,UAAI,aAAa,uBAAO,OAAO,IAAI;AACnC,UAAI,QAAQ,SAAU8C,MAAK;AACzB,YAAIA,KAAI,OAAO;AACb,qBAAWA,KAAI,EAAE,IAAIA;AAAA,QACvB;AAAA,MACF,CAAC;AAED,UAAI,WAAW,OAAO,KAAK,UAAU,EAAE;AACvC,aAAO,sBAAsB;AAC7B,aAAO,gBAAgB,KAAK,SAAS;AAErC,WAAK,QAAQ,SAAU,KAAK;AAC1B,YAAI,QAAQ,WAAW,IAAI,GAAG;AAC9B,YAAI,OAAO;AACT,iBAAO,OAAO,KAAK,KAAK;AAExB,cAAI,aAAa,MAAM,QAAQ,IAAI,YAAY;AAC/C,cAAI,cAAc,kBAAkB,cAAc,aAAa;AAC7D,wBAAY,KAAK,UAAU,MAAM,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF,OAAO;AACL,sBAAY,KAAK,GAAG;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IAEH,GAAG,SAAU,KAAK;AAChB,aAAO,sBAAsB,KAAK;AAClC,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAEA,WAAS,cAAc;AACrB,QAAI,aAAa,OAAO;AACtB,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACrD;AACA,WAAO,WAAW,WAAW,aAAa;AAC1C,QAAI,YAAY,MAAM,MAAM;AAC5B,QAAI,YAAY,QAAQ;AACtB,gBAAU,OAAO;AAGjB,UAAI,OAAO,aAAa,YAAY,UAAU;AAC5C,kBAAU,UAAU,aAAa;AACjC,eAAO,aAAa;AAAA,MACtB;AACA,kBAAY,KAAK,UAAU,SAAS;AAAA,IACtC;AACA,wBAAoB;AAEpB,QAAI,KAAK,EAAE,KAAK,SAAU,MAAM;AAC9B,UAAI,OAAO,IAAI,YAAY,IAAI,MAAM;AACrC,UAAI,CAAC,gBAAgB,CAAC,MAAM;AAC1B;AAAA,MACF;AAEA,UAAI,YAAY,KAAK,mBAAmB;AACxC,UAAI,cAAc,SAAS,KAAK,YAAY,EAAE,IAAI,SAAS,kBAAkB,EAAE;AAC/E,UAAI,YAAY,OAAO,QAAQ;AAAA,QAC7B,iBAAiB,YAAY,aAAa,QAAQ;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAED,WAAO,aAAa;AAAA,MAAgB,aAAa;AAAA,MAC7C;AAAA,IAAO,EAAE,KAAK,WAAY;AAC5B,kBAAY,KAAK,cAAc,EAAE,cAAc,aAAa,IAAI,CAAC;AACjE,0BAAoB;AAEpB,UAAI,YAAY,WAAW;AACzB,4BAAoB;AACpB,cAAM,IAAI,MAAM,WAAW;AAAA,MAC7B;AACA,qBAAe;AACf,iBAAW;AAAA,IACb,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,wBAAkB,GAAG;AACrB,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAEA,WAAS,WAAW;AAClB,QAAI,OAAO,CAAC;AACZ,iBAAa,QAAQ,QAAQ,SAAU,QAAQ;AAC7C,kBAAY,KAAK,cAAc,EAAE,aAAa,OAAO,CAAC;AAGtD,UAAI,OAAO,OAAO,UAAU;AAC1B;AAAA,MACF;AACA,WAAK,OAAO,EAAE,IAAI,OAAO,QAAQ,IAAI,SAAU,GAAG;AAChD,eAAO,EAAE;AAAA,MACX,CAAC;AAAA,IACH,CAAC;AACD,WAAO,OAAO,SAAS,IAAI,EAAE,KAAK,SAAU,OAAO;AAEjD,UAAI,YAAY,WAAW;AACzB,4BAAoB;AACpB,cAAM,IAAI,MAAM,WAAW;AAAA,MAC7B;AAEA,mBAAa,QAAQ;AAAA,IACvB,CAAC;AAAA,EACH;AAEA,WAAS,eAAe;AACtB,WAAO,QAAQ,KAAK,QAAQ,aAAa,OAAO,WAAW,EAAE,KAAK,SAAU,KAAK;AAC/E,mBAAa,QAAQ,CAAC,IAAI;AAC1B,UAAI,KAAK,QAAQ,SAAU,KAAK;AAC9B,eAAO,aAAa,MAAM,IAAI,GAAG;AACjC,eAAO;AACP,qBAAa,KAAK,KAAK,GAAG;AAAA,MAC5B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,WAAS,iBAAiB;AACxB,QAAI,YAAY,aAAa,cAAc;AACzC;AAAA,IACF;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,0BAAoB,IAAI;AACxB;AAAA,IACF;AACA,mBAAe,QAAQ,MAAM;AAC7B,gBAAY,KAAK,cAAc,EAAE,oBAAoB,aAAa,IAAI,CAAC;AACvE,aAAS,EACN,KAAK,YAAY,EACjB,KAAK,SAAS,EACd,KAAK,WAAW,EAChB,KAAK,cAAc,EACnB,MAAM,SAAU,KAAK;AACpB,uBAAiB,0CAA0C,GAAG;AAAA,IAChE,CAAC;AAAA,EACL;AAGA,WAAS,oBAAoB,WAAW;AACtC,QAAI,aAAa,QAAQ,WAAW,GAAG;AACrC,UAAI,QAAQ,WAAW,KAAK,CAAC,cAAc;AACzC,YAAK,cAAc,YAAY,QAAS,kBAAkB;AACxD,sBAAY,QAAQ;AACpB,sBAAY,KAAK,QAAQ;AAAA,QAC3B;AACA,YAAI,kBAAkB;AACpB,8BAAoB;AAAA,QACtB;AAAA,MACF;AACA;AAAA,IACF;AACA,QACE,aACA,oBACA,aAAa,QAAQ,UAAU,YAC/B;AACA,cAAQ,KAAK,YAAY;AACzB,qBAAe;AAAA,QACb,KAAK;AAAA,QACL,SAAS,CAAC;AAAA,QACV,MAAM,CAAC;AAAA,MACT;AACA,UAAI,YAAY,UAAU,aAAa,YAAY,UAAU,WAAW;AACtE,oBAAY,QAAQ;AACpB,oBAAY,KAAK,QAAQ;AAAA,MAC3B;AACA,qBAAe;AAAA,IACjB;AAAA,EACF;AAGA,WAAS,iBAAiB,QAAQ,KAAK;AACrC,QAAI,sBAAsB;AACxB;AAAA,IACF;AACA,QAAI,CAAC,IAAI,SAAS;AAChB,UAAI,UAAU;AAAA,IAChB;AACA,WAAO,KAAK;AACZ,WAAO,SAAS;AAChB,cAAU,CAAC;AACX,mBAAe;AAAA,MACb,KAAK;AAAA,MACL,SAAS,CAAC;AAAA,MACV,MAAM,CAAC;AAAA,IACT;AACA,wBAAoB,GAAG;AAAA,EACzB;AAGA,WAAS,oBAAoB,YAAY;AACvC,QAAI,sBAAsB;AACxB;AAAA,IACF;AAEA,QAAI,YAAY,WAAW;AACzB,aAAO,SAAS;AAChB,UAAI,mBAAmB;AACrB;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,OAAO,UAAU;AACjC,WAAO,YAAW,oBAAI,KAAK,GAAE,YAAY;AACzC,WAAO,WAAW;AAClB,2BAAuB;AAEvB,QAAI,YAAY,OAAO,QAAQ,UAAU;AAEzC,QAAI,YAAY;AAEd,mBAAa,YAAY,UAAU;AACnC,iBAAW,SAAS;AAGpB,UAAI,aAAa,WAAW,QAAQ,IAAI,YAAY;AACpD,UAAI,cAAc,kBAAkB,cAAc,aAAa;AAC7D,oBAAY,KAAK,SAAS,UAAU;AACpC,oBAAY,mBAAmB;AAAA,MACjC,OAAO;AACL,gBAAQ,MAAM,aAAa,YAAY,WAAY;AACjD,oBAAU,KAAK,QAAQ,MAAM,WAAW;AAAA,QAC1C,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,kBAAY,KAAK,YAAY,MAAM;AACnC,kBAAY,mBAAmB;AAAA,IACjC;AAAA,EACF;AAEA,WAAS,SAAS,QAAQ,SAAS,SAAS;AAE1C,QAAI,YAAY,WAAW;AACzB,aAAO,oBAAoB;AAAA,IAC7B;AAGA,QAAI,OAAO,YAAY,UAAU;AAC/B,mBAAa,UAAU;AAAA,IACzB;AAEA,QAAI1C,UAAS,aAAa,IAAI,EAAE,MAAM;AACtC,QAAI,CAACA,SAAQ;AAEX,UAAI,OAAO,IAAI,YAAY,IAAI,MAAM;AACrC,UAAI,MAAM;AAER,YAAI,YAAY,KAAK,mBAAmB;AACxC,YAAI,YAAY,OAAO,QAAQ,EAAC,iBAAiB,EAAE,UAAS,CAAC;AAAA,MAC/D;AACA;AAAA,IACF;AACA,iBAAa,MAAM,OAAO,OAAO;AACjC,iBAAa,QAAQ,KAAK,MAAM;AAChC,gBAAY,KAAK,cAAc,EAAE,iBAAiB,aAAa,IAAI,CAAC;AACpE,aAAS,WAAY;AACnB,0BAAoB,QAAQ,WAAW,KAAK,YAAY,IAAI;AAAA,IAC9D,CAAC;AAAA,EACH;AAGA,WAAS,kBAAkB+B,UAAS;AAClC,qBAAiB;AAEjB,QAAI,YAAY,WAAW;AACzB,aAAO,oBAAoB;AAAA,IAC7B;AAIA,QAAIA,SAAQ,QAAQ,SAAS,GAAG;AAC9B,kBAAY,QAAQA,SAAQ,QAAQA,SAAQ,QAAQ,SAAS,CAAC,EAAE;AAChE,iBAAW;AACX,0BAAoB,IAAI;AAAA,IAC1B,OAAO;AAEL,UAAI,WAAW,WAAY;AACzB,YAAI,YAAY;AACd,sBAAY,OAAO;AACnB,qBAAW;AAAA,QACb,OAAO;AACL,6BAAmB;AAAA,QACrB;AACA,4BAAoB,IAAI;AAAA,MAC1B;AAGA,UAAI,CAAC,gBAAgBA,SAAQ,QAAQ,WAAW,GAAG;AACjD,4BAAoB;AACpB,qBAAa;AAAA,UAAgBA,SAAQ;AAAA,UACjC;AAAA,QAAO,EAAE,KAAK,WAAY;AAC5B,8BAAoB;AACpB,iBAAO,WAAW,WAAWA,SAAQ;AACrC,cAAI,YAAY,WAAW;AACzB,gCAAoB;AACpB,kBAAM,IAAI,MAAM,WAAW;AAAA,UAC7B,OAAO;AACL,qBAAS;AAAA,UACX;AAAA,QACF,CAAC,EACA,MAAM,iBAAiB;AAAA,MAC1B,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAGA,WAAS,eAAe,KAAK;AAC3B,qBAAiB;AAEjB,QAAI,YAAY,WAAW;AACzB,aAAO,oBAAoB;AAAA,IAC7B;AACA,qBAAiB,oBAAoB,GAAG;AAAA,EAC1C;AAGA,WAAS,aAAa;AACpB,QAAI,EACF,CAAC,kBACD,CAAC,oBACD,QAAQ,SAAS,gBACd;AACH;AAAA,IACF;AACA,qBAAiB;AACjB,aAAS,eAAe;AACtB,MAAAA,SAAQ,OAAO;AAAA,IACjB;AACA,aAAS,iBAAiB;AACxB,kBAAY,eAAe,UAAU,YAAY;AAAA,IACnD;AAEA,QAAI,YAAY,UAAU;AACxB,kBAAY,eAAe,UAAU,YAAY,aAAa;AAC9D,kBAAY,SAAS,OAAO;AAAA,IAC9B;AACA,gBAAY,KAAK,UAAU,YAAY;AAEvC,QAAIA,WAAU,IAAI,QAAQ,WAAW,EAClC,GAAG,UAAU,QAAQ;AACxB,IAAAA,SAAQ,KAAK,gBAAgB,cAAc;AAC3C,IAAAA,SAAQ,KAAK,iBAAiB,EAC3B,MAAM,cAAc;AAEvB,QAAI,KAAK,OAAO;AAEd,kBAAY,WAAWA;AACvB,kBAAY,gBAAgB;AAAA,IAC9B;AAAA,EACF;AAEA,WAAS,WAAW,YAAY;AAC9B,WAAO,IAAI,KAAK,EAAE,KAAK,SAAU,MAAM;AACrC,UAAI,cAAc,OAAO,KAAK,UAAU,cACtC,SAAS,KAAK,YAAY,EAAE,IAAI,SAAS,YAAY,EAAE,IACvD,SAAS,KAAK,YAAY,EAAE;AAE9B,eAAS,IAAI,YAAY,IAAI;AAAA,QAC3B,MAAM,GAAG,aAAa,gBAAgB,EAAE,oBAAoB,KAAK,OAAO;AAAA,QACxE;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,WAAS,eAAe;AACtB,qBAAiB,EAAE,KAAK,WAAY;AAElC,UAAI,YAAY,WAAW;AACzB,4BAAoB;AACpB;AAAA,MACF;AACA,aAAO,aAAa,cAAc,EAAE,KAAK,UAAU,EAAE,KAAK,SAAU,YAAY;AAC9E,mBAAW;AACX,2BAAmB;AACnB,sBAAc;AAAA,UACZ,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,aAAa;AAAA;AAAA,QACf;AACA,YAAI,KAAK,QAAQ;AACf,cAAI,OAAO,KAAK,WAAW,UAAU;AAEnC,wBAAY,eAAe;AAAA,UAC7B,OAAO;AACL,wBAAY,SAAS,KAAK;AAAA,UAC5B;AAAA,QACF;AACA,YAAI,eAAe,MAAM;AACvB,sBAAY,YAAY,KAAK;AAAA,QAC/B;AACA,YAAI,aAAa,MAAM;AACrB,sBAAY,UAAU,KAAK;AAAA,QAC7B;AACA,YAAI,KAAK,cAAc;AACrB,sBAAY,eAAe,KAAK;AAAA,QAClC;AACA,YAAI,KAAK,MAAM;AACb,sBAAY,OAAO,KAAK;AAAA,QAC1B;AACA,mBAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,uBAAiB,gCAAgC,GAAG;AAAA,IACtD,CAAC;AAAA,EACH;AAGA,WAAS,kBAAkB,KAAK;AAC9B,wBAAoB;AACpB,qBAAiB,wCAAwC,GAAG;AAAA,EAC9D;AAGA,MAAI,YAAY,WAAW;AACzB,wBAAoB;AACpB;AAAA,EACF;AAEA,MAAI,CAAC,YAAY,iBAAiB;AAChC,gBAAY,KAAK,UAAU,mBAAmB;AAE9C,QAAI,OAAO,KAAK,aAAa,YAAY;AACvC,kBAAY,KAAK,SAAS,KAAK,QAAQ;AACvC,kBAAY,KAAK,YAAY,SAAUS,SAAQ;AAC7C,aAAK,SAAS,MAAMA,OAAM;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,gBAAY,kBAAkB;AAAA,EAChC;AAEA,MAAI,OAAO,KAAK,UAAU,aAAa;AACrC,iBAAa;AAAA,EACf,OAAO;AACL,qBAAiB,EAAE,KAAK,WAAY;AAClC,0BAAoB;AACpB,aAAO,aAAa,gBAAgB,KAAK,OAAO,OAAO;AAAA,IACzD,CAAC,EAAE,KAAK,WAAY;AAClB,0BAAoB;AAEpB,UAAI,YAAY,WAAW;AACzB,4BAAoB;AACpB;AAAA,MACF;AACA,iBAAW,KAAK;AAChB,mBAAa;AAAA,IACf,CAAC,EAAE,MAAM,iBAAiB;AAAA,EAC5B;AACF;AAIA,IAAM,cAAN,cAA0B,cAAA1C,QAAG;AAAA,EAC3B,cAAc;AACZ,UAAM;AACN,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,UAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,WAAK,KAAK,YAAY,OAAO;AAC7B,WAAK,KAAK,SAAS,MAAM;AAAA,IAC3B,CAAC;AACD,SAAK,OAAO,SAAU,SAAS,QAAQ;AACrC,aAAO,QAAQ,KAAK,SAAS,MAAM;AAAA,IACrC;AACA,SAAK,QAAQ,SAAU,QAAQ;AAC7B,aAAO,QAAQ,MAAM,MAAM;AAAA,IAC7B;AAGA,SAAK,MAAM,WAAY;AAAA,IAAC,CAAC;AAAA,EAC3B;AAAA,EAEA,SAAS;AACP,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,KAAK,QAAQ;AAAA,EACpB;AAAA,EAEA,MAAM,KAAK,QAAQ;AACjB,QAAI,KAAK,cAAc;AACrB;AAAA,IACF;AACA,SAAK,eAAe;AAEpB,UAAM,YAAY,MAAM;AACtB,WAAK,OAAO;AAAA,IACd;AACA,QAAI,KAAK,aAAa,SAAS;AAC/B,WAAO,KAAK,aAAa,SAAS;AAClC,aAAS,UAAU;AACjB,UAAI,eAAe,aAAa,SAAS;AACzC,aAAO,eAAe,aAAa,SAAS;AAAA,IAC9C;AACA,SAAK,KAAK,YAAY,OAAO;AAC7B,SAAK,KAAK,SAAS,OAAO;AAAA,EAC5B;AACF;AAEA,SAAS,QAAQ,IAAI,MAAM;AACzB,MAAI,mBAAmB,KAAK;AAC5B,MAAI,OAAO,OAAO,UAAU;AAC1B,WAAO,IAAI,iBAAiB,IAAI,IAAI;AAAA,EACtC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iBAAiB,KAAK,QAAQ,MAAM,UAAU;AAErD,MAAI,OAAO,SAAS,YAAY;AAC9B,eAAW;AACX,WAAO,CAAC;AAAA,EACV;AACA,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,KAAK,WAAW,CAAC,MAAM,QAAQ,KAAK,OAAO,GAAG;AAChD,UAAM;AAAA,MAAY;AAAA,MACC;AAAA,IAA2C;AAAA,EAChE;AAEA,OAAK,WAAW;AAChB,SAAO,MAAM,IAAI;AACjB,OAAK,aAAa,KAAK,cAAc,KAAK;AAC1C,OAAK,QAAS,WAAW,OAAQ,KAAK,QAAQ;AAC9C,OAAK,mBAAmB,KAAK,oBAAoB;AACjD,MAAI,eAAe,IAAI,YAAY,IAAI;AACvC,MAAI,WAAW,QAAQ,KAAK,IAAI;AAChC,MAAI,cAAc,QAAQ,QAAQ,IAAI;AACtC,YAAU,UAAU,aAAa,MAAM,YAAY;AACnD,SAAO;AACT;AAEA,SAAS,KAAK,KAAK,QAAQ,MAAM,UAAU;AACzC,MAAI,OAAO,SAAS,YAAY;AAC9B,eAAW;AACX,WAAO,CAAC;AAAA,EACV;AACA,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAO,CAAC;AAAA,EACV;AACA,SAAO,MAAM,IAAI;AACjB,OAAK,mBAAmB,KAAK,oBAAoB;AACjD,QAAM,QAAQ,KAAK,IAAI;AACvB,WAAS,QAAQ,QAAQ,IAAI;AAC7B,SAAO,IAAI,KAAK,KAAK,QAAQ,MAAM,QAAQ;AAC7C;AAEA,IAAM,OAAN,cAAmB,cAAAA,QAAG;AAAA,EACpB,YAAY,KAAK,QAAQ,MAAM,UAAU;AACvC,UAAM;AACN,SAAK,WAAW;AAEhB,UAAM,WAAW,KAAK,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,KAAK,IAAI,IAAI;AAClE,UAAM,WAAW,KAAK,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,KAAK,IAAI,IAAI;AAElE,SAAK,OAAO,iBAAiB,KAAK,QAAQ,QAAQ;AAClD,SAAK,OAAO,iBAAiB,QAAQ,KAAK,QAAQ;AAElD,SAAK,aAAa;AAClB,SAAK,aAAa;AAElB,UAAM,aAAa,CAAC,WAAW;AAC7B,WAAK,KAAK,UAAU;AAAA,QAClB,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,aAAa,CAAC,WAAW;AAC7B,WAAK,KAAK,UAAU;AAAA,QAClB,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,aAAa,CAAC,QAAQ;AAC1B,WAAK,KAAK,UAAU;AAAA,QAClB,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,aAAa,CAAC,QAAQ;AAC1B,WAAK,KAAK,UAAU;AAAA,QAClB,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,aAAa,MAAM;AACvB,WAAK,aAAa;AAElB,UAAI,KAAK,YAAY;AACnB,aAAK,KAAK,QAAQ;AAAA,MACpB;AAAA,IACF;AACA,UAAM,aAAa,MAAM;AACvB,WAAK,aAAa;AAElB,UAAI,KAAK,YAAY;AACnB,aAAK,KAAK,QAAQ;AAAA,MACpB;AAAA,IACF;AACA,UAAM,aAAa,MAAM;AACvB,WAAK,aAAa;AAElB,UAAI,KAAK,YAAY;AACnB,aAAK,KAAK,UAAU;AAAA,UAClB,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,aAAa,MAAM;AACvB,WAAK,aAAa;AAElB,UAAI,KAAK,YAAY;AACnB,aAAK,KAAK,UAAU;AAAA,UAClB,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,UAAU,CAAC;AAEf,UAAM,YAAY,CAAC,SAAS;AAC1B,aAAO,CAAC,OAAO,SAAS;AACtB,cAAM,WAAW,UAAU,aACxB,SAAS,cAAc,SAAS;AACnC,cAAM,WAAW,UAAU,aACxB,SAAS,cAAc,SAAS;AACnC,cAAM,WAAW,UAAU,aACxB,SAAS,cAAc,SAAS;AACnC,cAAM,WAAW,UAAU,aACxB,SAAS,cAAc,SAAS;AAEnC,YAAI,YAAY,YAAY,YAAY,UAAU;AAChD,cAAI,EAAE,SAAS,UAAU;AACvB,oBAAQ,KAAK,IAAI,CAAC;AAAA,UACpB;AACA,kBAAQ,KAAK,EAAE,IAAI,IAAI;AACvB,cAAI,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,WAAW,GAAG;AAE5C,iBAAK,mBAAmB,KAAK;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,GAAG,YAAY,KAAK,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC;AACzD,WAAK,KAAK,GAAG,YAAY,KAAK,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC;AAAA,IAC3D;AAEA,aAAS,eAAe,IAAI,OAAO,UAAU;AAC3C,UAAI,GAAG,UAAU,KAAK,EAAE,QAAQ,QAAQ,KAAK,IAAI;AAC/C,WAAG,GAAG,OAAO,QAAQ;AAAA,MACvB;AAAA,IACF;AAEA,SAAK,GAAG,eAAe,SAAU,OAAO;AACtC,UAAI,UAAU,UAAU;AACtB,uBAAe,KAAK,MAAM,UAAU,UAAU;AAC9C,uBAAe,KAAK,MAAM,UAAU,UAAU;AAAA,MAChD,WAAW,UAAU,UAAU;AAC7B,uBAAe,KAAK,MAAM,UAAU,UAAU;AAC9C,uBAAe,KAAK,MAAM,UAAU,UAAU;AAAA,MAChD,WAAW,UAAU,UAAU;AAC7B,uBAAe,KAAK,MAAM,UAAU,UAAU;AAC9C,uBAAe,KAAK,MAAM,UAAU,UAAU;AAAA,MAChD,WAAW,UAAU,UAAU;AAC7B,uBAAe,KAAK,MAAM,UAAU,UAAU;AAC9C,uBAAe,KAAK,MAAM,UAAU,UAAU;AAAA,MAChD;AAAA,IACF,CAAC;AAED,SAAK,GAAG,kBAAkB,SAAU,OAAO;AACzC,UAAI,UAAU,UAAU;AACtB,aAAK,KAAK,eAAe,UAAU,UAAU;AAC7C,aAAK,KAAK,eAAe,UAAU,UAAU;AAAA,MAC/C,WAAW,UAAU,UAAU;AAC7B,aAAK,KAAK,eAAe,UAAU,UAAU;AAC7C,aAAK,KAAK,eAAe,UAAU,UAAU;AAAA,MAC/C,WAAW,UAAU,UAAU;AAC7B,aAAK,KAAK,eAAe,UAAU,UAAU;AAC7C,aAAK,KAAK,eAAe,UAAU,UAAU;AAAA,MAC/C,WAAW,UAAU,UAAU;AAC7B,aAAK,KAAK,eAAe,UAAU,UAAU;AAC7C,aAAK,KAAK,eAAe,UAAU,UAAU;AAAA,MAC/C;AAAA,IACF,CAAC;AAED,SAAK,KAAK,GAAG,kBAAkB,UAAU,MAAM,CAAC;AAChD,SAAK,KAAK,GAAG,kBAAkB,UAAU,MAAM,CAAC;AAEhD,UAAM,UAAU,QAAQ,IAAI;AAAA,MAC1B,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,YAAM,MAAM;AAAA,QACV,MAAM,KAAK,CAAC;AAAA,QACZ,MAAM,KAAK,CAAC;AAAA,MACd;AACA,WAAK,KAAK,YAAY,GAAG;AACzB,UAAI,UAAU;AACZ,iBAAS,MAAM,GAAG;AAAA,MACpB;AACA,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACT,GAAG,CAAC,QAAQ;AACV,WAAK,OAAO;AACZ,UAAI,UAAU;AAGZ,iBAAS,GAAG;AAAA,MACd,OAAO;AAIL,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AACA,WAAK,mBAAmB;AACxB,UAAI,UAAU;AAEZ,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAED,SAAK,OAAO,SAAU,SAAS,KAAK;AAClC,aAAO,QAAQ,KAAK,SAAS,GAAG;AAAA,IAClC;AAEA,SAAK,QAAQ,SAAU,KAAK;AAC1B,aAAO,QAAQ,MAAM,GAAG;AAAA,IAC1B;AAAA,EACF;AAAA,EAEA,SAAS;AACP,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,KAAK,OAAO;AACjB,WAAK,KAAK,OAAO;AAAA,IACnB;AAAA,EACF;AACF;AAEA,SAAS,YAAYa,UAAS;AAC5B,EAAAA,SAAQ,YAAY;AACpB,EAAAA,SAAQ,OAAO;AAEf,SAAO,eAAeA,SAAQ,WAAW,aAAa;AAAA,IACpD,KAAK,WAAY;AACf,UAAIf,QAAO;AACX,UAAI,OAAO,KAAK,qBAAqB,aAAa;AAChD,aAAK,mBAAmB;AAAA,UACtB,MAAM,SAAU,OAAO,MAAM,UAAU;AACrC,mBAAOA,MAAK,YAAY,UAAU,OAAOA,OAAM,MAAM,QAAQ;AAAA,UAC/D;AAAA,UACA,IAAI,SAAU,OAAO,MAAM,UAAU;AACnC,mBAAOA,MAAK,YAAY,UAAUA,OAAM,OAAO,MAAM,QAAQ;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AACA,aAAO,KAAK;AAAA,IACd;AAAA,EACF,CAAC;AAED,EAAAe,SAAQ,UAAU,OAAO,SAAU,QAAQ,MAAM,UAAU;AACzD,WAAO,KAAK,YAAY,KAAK,MAAM,QAAQ,MAAM,QAAQ;AAAA,EAC3D;AACF;AAEA,QAAQ,OAAO,QAAQ,EACpB,OAAO,WAAW,EAClB,OAAO,SAAS,EAChB,OAAO,WAAW;AAErB,IAAO,mBAAQ;", "names": ["stringify", "queue", "keys", "pop", "collationIndex", "self", "nextTick", "EE", "reason", "filter", "keys", "Md5", "rev", "queue", "opts", "i", "l", "key", "err", "incompatibleOpt", "PouchDB", "error", "match", "init", "fields", "field", "matcher", "isArray", "collationIndex", "<PERSON><PERSON><PERSON><PERSON>", "vu<PERSON><PERSON>a", "winning<PERSON>ev", "doc", "count", "cursor", "len", "req", "callback", "e", "putReq", "changes", "running", "dbUrl", "localDocName", "mapper", "reducer", "ddocValidator", "readAttachmentsAsBlobOrBuffer", "response", "result", "metaDoc", "res", "processChange", "progress", "mapResults", "values", "version"]}